#!/bin/sh

set -x

G=com.youxin
A=risk-metrics
V=*******.final
U=http://artifactory.youxin.com:80/artifactory/risk
R=releases

MVN_CMD="mvn -e"
MVN_CMD="$MVN_CMD deploy:deploy-file"

F=./target/$A-1.0.0-SNAPSHOT.jar
MVN_CMD="$MVN_CMD -DgroupId=$G -DartifactId=$A -Dversion=$V -Dpackaging=jar -DpomFile=pom.xml"

MVN_CMD="$MVN_CMD -Dmaven.test.skip=true -Dfile=$F"
MVN_CMD="$MVN_CMD -Durl=$U -DrepositoryId=$R"
eval $MVN_CMD
