#关闭metrics
metrics.stop=false
# 命名空间(对应数据库)，长度不能大于32，只支持数字、字母、下划线，必输项
metrics.namespace=
# 远端队列类型：redis,kafka，必输项
metrics.remote.queue=
# 远端队列类型是redis时，redis是否集群模式，1|true-是，0|false-否
metrics.remote.redis.is.cluster=
#127.0.0.1:8379，必输项
metrics.remote.queue.server=
#自定义消息推送器，优先级高于metrics.remote.queue
metrics.pull.sender.type=

#远端队列key
metrics.remote.queue.redis.key=metrics-point-queue
#远端队列最大长度（对redis的保护，当大于该长度不再pull）
metrics.remote.queue.redis.max.size=500000
#远端redis队列密码
metrics.remote.queue.redis.password=
#use.pool=true时连接池配置
metrics.remote.queue.redis.max.total=8
metrics.remote.queue.redis.min.idle=2
metrics.remote.queue.redis.max.wait.millis=5000
metrics.remote.queue.redis.testOnBorrow=false
metrics.remote.queue.redis.testOnReturn=false
metrics.remote.queue.redis.testWhileIdle=true
#心跳检查间隔
metrics.remote.queue.redis.timeBetweenEvictionRunsMillis=60000
#心跳检查每次检查数目，建议大于连接池的最大连接数（metrics.remote.queue.redis.max.total）
metrics.remote.queue.redis.numTestsPerEvictionRun=1000
#空闲时间超过此值的会被销毁
metrics.remote.queue.redis.minEvictableIdleTimeMillis=300000

#消息推送到远端队列重试次数
metrics.remote.queue.retries=2
#消息推送到远端队列抄送时间（毫秒）
metrics.remote.queue.timeout=3000

#本地队列大小
metrics.local.queue.size=10000
#推送消息任务数（消费metrics.local.queue.size线程数）
metrics.pull.thread.size=8
#批量推送消息大小
metrics.pull.batch.size=30




