/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.shadow.dao.hfq;

import org.apache.ibatis.annotations.Param;

import com.youxin.risk.shadow.model.ShadowUserLineManagement;

public interface ShadowUserLineManagementMapper {

    int insertReplace(ShadowUserLineManagement entry);

    int updateStatusInvalid(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

	ShadowUserLineManagement selectLastValidRec(@Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);
}
