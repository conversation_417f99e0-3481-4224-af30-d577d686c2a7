<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.shadow.dao.hfq.ShadowLoanAuditStrategyResultMinimalMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.shadow.model.ShadowLoanAuditStrategyResult">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="loan_key" jdbcType="VARCHAR" property="loanKey" />
        <result column="loan_type" jdbcType="VARCHAR" property="loanType" />
        <result column="fund_channel" jdbcType="VARCHAR" property="fundChannel" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="loan" jdbcType="VARCHAR" property="loan" />
        <result column="loan_id" jdbcType="INTEGER" property="loanId" />
        <result column="user_line_id" jdbcType="BIGINT" property="userLineId" />
        <result column="lend_id" jdbcType="INTEGER" property="lendId" />
        <result column="mid_verify_id" jdbcType="INTEGER" property="midVerifyId" />
        <result column="is_passed" jdbcType="TINYINT" property="isPassed"  />
        <result column="status" jdbcType="TINYINT" property="status"  />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="repay_mob" jdbcType="TINYINT" property="repayMob"  />
        <result column="repay_lastpayoff_flag" jdbcType="TINYINT" property="repayLastpayoffFlag"  />
        <result column="repay_lastpayoff_time" jdbcType="TIMESTAMP" property="repayLastpayoffTime" />
    </resultMap>

    <sql id="table_name">
      loan_audit_strategy_result_minimal
    </sql>

    <sql id="save_Base_Column_List">
      user_key, loan_key, loan_type, fund_channel, order_no, loan, loan_id,
      user_line_id, lend_id, mid_verify_id, is_passed, status, create_time,repay_mob, repay_lastpayoff_flag, repay_lastpayoff_time
    </sql>

    <sql id="Base_Column_List">
        <include refid="save_Base_Column_List"/>
    </sql>

    <insert id="insertReplace" parameterType="com.youxin.risk.shadow.model.ShadowLoanAuditStrategyResult">
        replace into <include refid="table_name"/> (<include refid="Base_Column_List"/>) values
        (#{userKey}, #{loanKey}, #{loanType},
        #{fundChannel}, #{orderNo}, #{loan}, #{loanId}, #{userLineId}, #{lendId},
        #{midVerifyId}, #{isPassed}, #{status}, #{createTime},#{repayMob},#{repayLastpayoffFlag},#{repayLastpayoffTime})
    </insert>

    <select id="countByLoanKey" resultType="java.lang.Integer">
        select count(1)
          from loan_audit_strategy_result_minimal
         where user_key=#{userKey}
           and loan_key=#{loanKey}
    </select>
</mapper>