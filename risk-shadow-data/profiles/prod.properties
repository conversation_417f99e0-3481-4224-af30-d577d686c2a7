mode.name=prod
app.name=risk-shadow-data

home.base=/opt/app/tomcat

app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}
tomcat.port=8131
tomcat.shutdown.port=8132
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx3550m -Xms3550m -Xmn1500m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=OFF

datasource.maxActive=30
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=${SEC_RISK_ADMIN_DB_USERNAME}
admin.datasource.pwd=${SEC_RISK_ADMIN_DB_PASSWORD}

#上线前申请
shadow.datasource.url=******************************************?${datasource.url.params}
shadow.datasource.username=${SEC_RISK_SHADOW_DB_USERNAME}
shadow.datasource.pwd=${SEC_RISK_SHADOW_DB_PASSWORD}

# Redis集群信息，不同类型的Redis集群！！！不要随意共用
# 如果访问量大，需要单独一个，量不大的可以共用一个。
# 比如：监控打点单独一个, 数据缓存单独的，
# http://wiki.youxin.com/pages/viewpage.action?pageId=48993218
redis.maxTotal=300
redis.maxIdle=30
redis.minIdle=10
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=1c1zOc9cHCGE9xmOZ51jzM
redis.cluster.nodes=***********:6398,***********:6395,***********:6396,************:6393,************:6394,***********:6399

kafka.dp.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.mirror.dp.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
kafka.shadow.datatrans.topic=kafka.shadow.save.topic
kafka.shadow.datatrans.topic.group.id=youxin_risk_shadow_Group_57495827e957c6263

kafka.shadow.monitor.datatrans.topic=kafka.shadow.monitor.topic
kafka.shadow.monitor.datatrans.topic.group.id=youxin_risk_shadow_monitor_Group_57495827e957c6259
#engine shadow
kafka.engine.shadow.topic=risk.engine.event.shadow
kafka.engine.shadow.topic.group.id=youxin_risk_engine_event_shadow_Group

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

url.notify.hfq=https://api-m.haohuan.com/internal/v1/audit/update-account-info
notify.encrypt.key.hfq=HAOHUAN_PASSWORD

datasource.paydayloanVerify.url=********************************************************************************************************************************
datasource.paydayloanVerify.username=fk_verify_x
datasource.paydayloanVerify.password=d6549d86af576ba6

xxl.job.admin.addresses=http://risk-xxl-job-manager.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=risk-shadow-data
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

youxin.env=PROD


metrics.point.kafka.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
#metrics.point.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
metrics.point.kafka.topic=metrics.point.kafka.topic
metrics.point.kafka.group.id=metrics.point.kafka.group
metrics.point.mirror.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092

metrics.point.kafka.topic.list=metrics.point.kafka.topic,metrics.point.kafka.topic.gateway
