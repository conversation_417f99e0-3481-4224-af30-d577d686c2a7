<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.mapper.VerifySubmitIdcardMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.verify.model.VerifySubmitIdcard">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="operation_log_id" jdbcType="INTEGER" property="operationLogId"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="idcard_number" jdbcType="VARCHAR" property="idcardNumber"
                typeHandler="com.youxin.risk.commons.mybatis.IdCardMaskHandler"/>
        <result column="idcard_name" jdbcType="VARCHAR" property="idcardName"
                typeHandler="com.youxin.risk.commons.mybatis.IdCardMaskHandler"/>
        <result column="idcard_address" jdbcType="VARCHAR" property="idcardAddress"/>
        <result column="pid_positive_url" jdbcType="VARCHAR" property="pidPositiveUrl"/>
        <result column="pid_negative_url" jdbcType="VARCHAR" property="pidNegativeUrl"/>
        <result column="face_url" jdbcType="VARCHAR" property="faceUrl"/>
        <result column="curve_face_url" jdbcType="VARCHAR" property="curveFaceUrl"/>
        <result column="face_score" jdbcType="DOUBLE" property="faceScore"/>
        <result column="pid_auth" jdbcType="BOOLEAN" property="pidAuth"/>
        <result column="face_threshold" jdbcType="VARCHAR" property="faceThreshold"/>
        <result column="face_channel" jdbcType="INTEGER" property="faceChannel"/>
        <result column="positive_channel" jdbcType="INTEGER" property="positiveChannel"/>
        <result column="negative_channel" jdbcType="INTEGER" property="negativeChannel"/>
        <result column="live_rate" jdbcType="DOUBLE" property="liveRate"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>


    <sql id="table_name">
      submit_idcard
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.verify.model.VerifySubmitIdcard">
        insert into
        <include refid="table_name"/>
        (operation_log_id,user_key,idcard_number,idcard_name,idcard_address,pid_positive_url,pid_negative_url,face_url,curve_face_url,face_score,pid_auth,face_threshold,face_channel,positive_channel,negative_channel,live_rate,update_time,create_time,version)
        values
        (#{operationLogId},#{userKey},#{idcardNumber,typeHandler=com.youxin.risk.commons.mybatis.IdCardMaskHandler},
            #{idcardName,typeHandler=com.youxin.risk.commons.mybatis.IdCardMaskHandler},#{idcardAddress},#{pidPositiveUrl},#{pidNegativeUrl},#{faceUrl},#{curveFaceUrl},#{faceScore},#{pidAuth},#{faceThreshold},#{faceChannel},#{positiveChannel},#{negativeChannel},#{liveRate},now(),now(),#{version})
    </insert>


    <update id="update" parameterType="com.youxin.risk.ra.model.SubmitIdcard">
        update
        <include refid="table_name"/>
        <set>
            <if test="operationLogId != null">
                operation_log_id = #{operationLogId},
            </if>
            <if test="userKey != null">
                user_key = #{userKey},
            </if>
            <if test="idcardNumber != null">
                idcard_number = #{idcardNumber,typeHandler=com.youxin.risk.commons.mybatis.IdCardMaskHandler},
            </if>
            <if test="idcardName != null">
                idcard_name = #{idcardName,typeHandler=com.youxin.risk.commons.mybatis.IdCardMaskHandler},
            </if>
            <if test="idcardAddress != null">
                idcard_address = #{idcardAddress},
            </if>
            <if test="pidPositiveUrl != null">
                pid_positive_url = #{pidPositiveUrl},
            </if>
            <if test="pidNegativeUrl != null">
                pid_negative_url = #{pidNegativeUrl},
            </if>
            <if test="faceUrl != null">
                face_url = #{faceUrl},
            </if>
            <if test="curveFaceUrl != null">
                curve_face_url = #{curveFaceUrl},
            </if>
            <if test="faceScore != null">
                face_score = #{faceScore},
            </if>
            <if test="pidAuth != null">
                pid_auth = #{pidAuth},
            </if>
            <if test="faceThreshold != null">
                face_threshold = #{faceThreshold},
            </if>
            <if test="faceChannel != null">
                face_channel = #{faceChannel},
            </if>
            <if test="positiveChannel != null">
                positive_channel = #{positiveChannel},
            </if>
            <if test="negativeChannel != null">
                negative_channel = #{negativeChannel},
            </if>
            <if test="liveRate != null">
                live_rate = #{liveRate},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findIdCard" resultMap="BaseResultMap">
        select * from <include refid="table_name"/>
        WHERE user_key = #{userKey} ORDER BY id DESC limit 1
    </select>

    <select id="queryById" parameterType="map" resultType="java.util.HashMap">
        select id, idcard_number, idcard_name from <include refid="table_name"/> where id between #{start} and #{end}
    </select>

    <update id="updateById" >
        update <include refid="table_name"/> set idcard_number = #{idcard_number}, idcard_name = #{idcard_name} WHERE id = #{id}
    </update>


</mapper>