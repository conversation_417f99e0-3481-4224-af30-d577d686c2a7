<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.SubmitBankCardMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.SubmitBankCard">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="apply_id" jdbcType="INTEGER" property="applyId" />
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="bank_card_no" jdbcType="VARCHAR" property="bankcardNo" />
        <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
        <result column="bank_address" jdbcType="VARCHAR" property="bankAddress" />
        <result column="reserved_mobile" jdbcType="VARCHAR" property="reservedMobile"
                typeHandler="com.youxin.risk.commons.mybatis.BankCardMaskHandler"/>
        <result column="validation" jdbcType="BIT" property="validation" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>


    <sql id="table_name">
      ra_bank_card
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.ra.model.SubmitBankCard">
        insert into
        <include refid="table_name"/>
        (apply_id,source_system,user_key,bank_card_no,bank_name,bank_address,reserved_mobile,validation,update_time,create_time,version)
        values
        (#{applyId},#{sourceSystem},#{userKey},#{bankcardNo},#{bankName},#{bankAddress},#{reservedMobile,typeHandler=com.youxin.risk.commons.mybatis.BankCardMaskHandler},
            #{validation},now(),now(),#{version})
    </insert>

    <select id="findByApplyId" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        WHERE apply_id = #{applyId} limit 1
    </select>

    <select id="findAllLastSubmitInfoByUserKey" resultMap="BaseResultMap">
        select id,bank_card_no from <include refid="table_name"/>
        WHERE user_key = #{userKey}
    </select>

    <update id="updateBankCardById" parameterType="com.youxin.risk.ra.model.SubmitBankCard">
        update  <include refid="table_name"/> set bank_card_no=#{bankcardNo}
        WHERE id = #{id}
    </update>

    <select id="getLastByUserKey" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        WHERE user_key = #{userKey} order by id desc limit 1
    </select>

    <select id="queryBankCardInfoById" parameterType="map" resultType="java.util.HashMap">
        select id,reserved_mobile from <include refid="table_name"/> where id between #{start} and #{end}
    </select>

    <update id="updateMobileById" >
        update <include refid="table_name"/> set reserved_mobile=#{reserved_mobile}
        WHERE id = #{id}
    </update>

</mapper>