<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.SubmitApplyMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.SubmitApply">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="app_version" jdbcType="VARCHAR" property="appVersion"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="mobile_model" jdbcType="VARCHAR" property="mobileModel"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="os_version" jdbcType="VARCHAR" property="osVersion"/>
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="apply_type" jdbcType="VARCHAR" property="applyType"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="jail_broken" jdbcType="INTEGER" property="jailBroken"/>
        <result column="device" jdbcType="VARCHAR" property="device"/>
        <result column="is_copy_package" jdbcType="INTEGER" property="isCopyPackage"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>


    <sql id="table_name">
      ra_apply
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.ra.model.SubmitApply">
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into
        <include refid="table_name"/>
        (app_version, channel_code,mobile_model, device_id, platform,os_version, source_system, user_key,ip,apply_type,channel,jail_broken,device,is_copy_package,update_time,create_time,version)
        values (#{appVersion}, #{channelCode},#{mobileModel}, #{deviceId}, #{platform},#{osVersion}, #{sourceSystem}, #{userKey},#{ip},#{applyType},#{channel},#{jailBroken},#{device},#{isCopyPackage},
        now(),now(),#{version}
        )
    </insert>


    <select id="get" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where id = #{applyId}
    </select>

    <select id="findLastApplyByUser" resultMap="BaseResultMap">
        select * FROM <include refid="table_name"/>  t1 JOIN (
        select
        MAX(id) AS id
        FROM
        <include refid="table_name"/>
        WHERE source_system = #{sourceSystem}
        AND user_key = #{userKey}  GROUP BY apply_type) t2 ON t1.id = t2.id

    </select>


    <select id="findAllApplyByUser" resultMap="BaseResultMap">
        select * FROM <include refid="table_name"/> WHERE source_system = #{sourceSystem} AND user_key = #{userKey}
    </select>

    <select id="getLastApplySubmitByUserKeyAndSourceSystem" resultMap="BaseResultMap">
        select * FROM <include refid="table_name"/>
        WHERE source_system IN
        <foreach collection="sourceSystemList" item="sourceSystem" index="index" open="(" separator="," close=")">
            #{sourceSystem}
        </foreach>
        AND user_key = #{userKey}  and apply_type = #{applyType}
        limit 1
    </select>

    <select id="findLastApplyByUserAndType" resultMap="BaseResultMap">
        select * FROM <include refid="table_name"/>
        WHERE source_system = #{sourceSystem} AND user_key = #{userKey}  and apply_type = #{applyType} order by id desc
        limit 1
    </select>


</mapper>