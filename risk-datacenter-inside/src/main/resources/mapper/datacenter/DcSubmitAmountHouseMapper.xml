<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcSubmitAmountHouseMapper" >
  <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcSubmitAmountHouse" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER"/>
    <result column="user_key" property="userKey" jdbcType="VARCHAR" />
    <result column="living_city" property="livingCity" jdbcType="VARCHAR" />
    <result column="living_address" property="livingAddress" jdbcType="VARCHAR" />
    <result column="living_type" property="livingType" jdbcType="VARCHAR" />
    <result column="total_amount" property="totalAmount" jdbcType="VARCHAR"/>
    <result column="buy_period" property="buyPeriod" jdbcType="VARCHAR"/>
    <result column="is_other_house" property="isOtherHouse" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, operation_log_id,user_key, living_city, living_address, living_type, total_amount, buy_period,is_other_house,create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from dc_submit_amount_house
    where id = #{id,jdbcType=INTEGER}
  </select>


  <insert id="insert" parameterType="com.youxin.risk.datacenter.model.DcSubmitAmountHouse" >
    <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
      SELECT LAST_INSERT_ID() AS id
    </selectKey>
    insert into dc_submit_amount_house (operation_log_id, user_key, living_city,
      living_address, living_type, total_amount,buy_period,is_other_house, create_time,
      update_time)
    values (#{operationLogId}, #{userKey}, #{livingCity},
      #{livingAddress}, #{livingType},#{totalAmount},#{buyPeriod}, #{isOtherHouse},#{createTime},
      #{updateTime})
  </insert>


  <select id="getByUserKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dc_submit_amount_house
    where user_key = #{userKey} order  by id DESC limit 1
  </select>

  <select id="getLatestByUserKey" resultMap="BaseResultMap">
    select living_city,living_address,living_type,create_time,total_amount,buy_period,is_other_house
    from dc_submit_amount_house
    where user_key = #{userKey} order  by id DESC limit 1
  </select>

</mapper>