<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcAmountRetrialAuditMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcAmountRetrialAudit" >
        <result column="id" property="id"  />
        <result column="user_key" property="userKey"  />
        <result column="loan_key" property="loanKey"  />
        <result column="user_account" property="userAccount"  />
        <result column="loan_line" property="loanLine"  />
        <result column="fix_line" property="fixLine" />
        <result column="temp_line" property="tempLine" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <insert id="insert" parameterType="com.youxin.risk.datacenter.model.DcAmountRetrialAudit">
        insert into dc_amount_retrial_audit (
                                    user_key,
                                    loan_key,
                                    user_account,
                                    loan_line,
                                    fix_line,
                                    temp_line,
                                    create_time,
                                    update_time
        )
        values (
                #{userKey},
                #{loanKey},
                #{userAccount},
                #{loanLine},
                #{fixLine},
                #{tempLine},
                #{createTime},
                #{updateTime}
        )
    </insert>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select id, user_key, loan_key, user_account, loan_line, fix_line,
               temp_line, create_time
        from dc_amount_retrial_audit
        where user_key = #{userKey} order by id desc limit 1;
    </select>

</mapper>