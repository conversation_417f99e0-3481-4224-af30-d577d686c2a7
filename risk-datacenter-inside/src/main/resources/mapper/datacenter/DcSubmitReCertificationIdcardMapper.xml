<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcSubmitReCertificationIdcardMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcSubmitIdcard" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="idcard_number" property="idcardNumber" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.IdCardMaskHandler"/>
        <result column="idcard_name" property="idcardName" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.IdCardMaskHandler"/>
        <result column="idcard_address" property="idcardAddress" jdbcType="VARCHAR" />
        <result column="pid_positive_url" property="pidPositiveUrl" jdbcType="VARCHAR" />
        <result column="pid_negative_url" property="pidNegativeUrl" jdbcType="VARCHAR" />
        <result column="face_url" property="faceUrl" jdbcType="VARCHAR" />
        <result column="curve_face_url" property="curveFaceUrl" jdbcType="VARCHAR" />
        <result column="face_score" property="faceScore" jdbcType="VARCHAR" />
        <result column="face_threshold" property="faceThreshold" jdbcType="VARCHAR" />
        <result column="pid_auth" property="pidAuth" jdbcType="BIT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />

        <result column="idcard_nation" jdbcType="VARCHAR" property="idcardNation" />
        <result column="idcard_issue" jdbcType="LONGVARCHAR" property="idcardIssue" />
        <result column="idcard_valid" jdbcType="VARCHAR" property="idcardValid" />
        <result column="idcard_legality_front" jdbcType="LONGVARCHAR" property="idcardLegalityFront" />
        <result column="idcard_legality_back" jdbcType="LONGVARCHAR" property="idcardLegalityBack" />
        <result column="face_genuineness" jdbcType="LONGVARCHAR" property="faceGenuineness" />
        <result column="face_channel" jdbcType="INTEGER" property="faceChannel" />
        <result column="positive_channel" jdbcType="INTEGER" property="positiveChannel" />
        <result column="negative_channel" jdbcType="INTEGER" property="negativeChannel" />
        <result column="live_rate" jdbcType="DOUBLE" property="liveRate" />

        <result column="location_label" jdbcType="INTEGER" property="locationLabel" />
        <result column="hit_rule" jdbcType="VARCHAR" property="hitRule" />
        <result column="validity_start_before_recertification" jdbcType="VARCHAR" property="validityStartBeforeReCertification" />
        <result column="validity_end_before_recertification" jdbcType="VARCHAR" property="validityEndBeforeReCertification" />
        <result column="recertification_flag" jdbcType="BIT" property="reCertificationFlag" />
        <result column="missing_information" jdbcType="VARCHAR" property="missingInformation" />
        <result column="face_hit_rule" jdbcType="VARCHAR" property="faceHitRule" />
        <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    </resultMap>

    <sql id="table_name">
        dc_submit_recertification_idcard
    </sql>

    <sql id="Base_Column_List" >
        id, operation_log_id, user_key, idcard_number, idcard_name, idcard_address, pid_positive_url,
        pid_negative_url, face_url, curve_face_url, face_score, face_threshold, pid_auth,
        create_time, update_time,idcard_nation,idcard_issue,idcard_valid,idcard_legality_front,idcard_legality_back,face_genuineness,face_channel,positive_channel,negative_channel,live_rate,
        location_label,hit_rule,validity_start_before_recertification,validity_end_before_recertification,recertification_flag,missing_information,face_hit_rule,device_id
    </sql>



    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitIdcard" >
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into <include refid="table_name"/> (operation_log_id, user_key,
            idcard_number, idcard_name, idcard_address,
            pid_positive_url, pid_negative_url, face_url,
            curve_face_url, face_score, face_threshold,
            pid_auth, create_time, update_time,idcard_nation,idcard_issue,idcard_valid,idcard_legality_front,idcard_legality_back,face_genuineness,
            face_channel,positive_channel,negative_channel,live_rate,location_label,hit_rule,validity_start_before_recertification,validity_end_before_recertification,recertification_flag,missing_information,
            face_hit_rule,device_id
            )
        values (#{operationLogId,jdbcType=INTEGER}, #{userKey,jdbcType=VARCHAR},
            #{idcardNumber,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.IdCardMaskHandler},
            #{idcardName,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.IdCardMaskHandler}, #{idcardAddress,jdbcType=VARCHAR},
            #{pidPositiveUrl,jdbcType=VARCHAR}, #{pidNegativeUrl,jdbcType=VARCHAR}, #{faceUrl,jdbcType=VARCHAR},
            #{curveFaceUrl,jdbcType=VARCHAR}, #{faceScore,jdbcType=VARCHAR}, #{faceThreshold,jdbcType=VARCHAR},
            #{pidAuth,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
        #{idcardNation,jdbcType=VARCHAR},#{idcardIssue,jdbcType=LONGVARCHAR},#{idcardValid,jdbcType=VARCHAR},
        #{idcardLegalityFront,jdbcType=LONGVARCHAR},#{idcardLegalityBack,jdbcType=LONGVARCHAR},#{faceGenuineness,jdbcType=LONGVARCHAR},
        #{faceChannel,jdbcType=INTEGER},#{positiveChannel,jdbcType=INTEGER},#{negativeChannel,jdbcType=INTEGER},#{liveRate,jdbcType=DOUBLE},
        #{locationLabel},#{hitRule},#{validityStartBeforeReCertification},#{validityEndBeforeReCertification},#{reCertificationFlag},#{missingInformation},
        #{faceHitRule},#{deviceId}
            )
    </insert>


    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where user_key = #{userKey} order by id DESC limit 1
    </select>

    <select id="queryById" parameterType="map" resultType="java.util.HashMap">
        select id, idcard_number, idcard_name from <include refid="table_name"/> where id between #{start} and #{end}
    </select>

    <update id="updateById" >
        update <include refid="table_name"/> set idcard_number = #{idcard_number}, idcard_name = #{idcard_name} WHERE id = #{id}
    </update>

</mapper>