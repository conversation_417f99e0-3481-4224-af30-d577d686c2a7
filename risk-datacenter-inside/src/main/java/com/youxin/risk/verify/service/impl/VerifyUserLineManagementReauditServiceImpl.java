package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.service.verify.VerifyResultService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.BooleanResult;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.verify.model.VerifyUserLineManagementReaudit;
import com.youxin.risk.verify.service.VerifyUserLineManagementReauditService;
import com.youxin.risk.verify.sharding.mapper.VerifyUserLineManagementReauditMapper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Service
public class VerifyUserLineManagementReauditServiceImpl implements VerifyUserLineManagementReauditService {
    private Logger logger = LoggerFactory.getLogger(getClass());

    private static String REASON_CODE = "reason_code";
    private static String IS_FINAL_PASSED = "is_final_passed";

    @Resource
    private VerifyUserLineManagementReauditMapper verifyUserLineManagementReauditMapper;
    @Resource
    private VerifyResultService verifyResultService;

    /**
     * 通用保存额度方法
     */
    @Override
    public BooleanResult process(JSONObject param) {
        BooleanResult booleanResult = new BooleanResult(false);
        // check幂等；保存结果；更新幂等信息
        JSONObject record = param.getJSONObject("amountParams");
        String userKey = record.getString("userKey");
        String sourceSystem = record.getString("sourceSystem");
        Integer loanId = record.getInteger("loanId");
        String loanKey = record.getString("loanKey");
        if (param.getJSONObject("verifyResult") != null && param.getJSONObject("verifyResult").getInteger("loanId") != null) {
            loanId = param.getJSONObject("verifyResult").getInteger("loanId");
        }
        logger.info("getLoanKeyFromVerifyResult: BaseAmountProcesser: strategyType={},loanKey={}", param.getString("strategyType"), loanKey);
        booleanResult = saveUserAmount(param, sourceSystem, userKey, getLoanKeyFromVerifyResult(loanKey, userKey, loanId, param.getString("strategyType")), loanId);
        return booleanResult;
    }

    /**
     * 更新额度流程
     * @param param
     * @return
     */
    @Override
    public BooleanResult updateProcess(JSONObject param){
        BooleanResult booleanResult = new BooleanResult(false);
        // check幂等；保存结果；更新幂等信息
        String userKey = param.getString("userKey");
        String sourceSystem = param.getString("sourceSystem");
        String loanKey = param.getString("loanKey");
        logger.info("getLoanKeyFromVerifyResult: BaseAmountProcesser: strategyType={},loanKey={}", param.getString("strategyType"), loanKey);
        booleanResult = updateUserAmount(param, sourceSystem, userKey, loanKey);
        return booleanResult;
    }

    public String getLoanKeyFromVerifyResult(String currentLoanKey, String userKey, Integer loanId, String strategyType) {
        String jumpOverForStrategyType = NacosClient.getByNameSpace(ApolloNamespace.DATACENTER_NAMESPACE, "jumpover.strategy.type", "&&");
        logger.info("jumpOverForStrategyType: apollo={}, target={},ifResult={}", jumpOverForStrategyType, strategyType, jumpOverForStrategyType.contains("&" + strategyType + "&"));
        if (jumpOverForStrategyType.contains("&" + strategyType + "&")) {
            return currentLoanKey;
        }
        logger.info("getLoanKeyFromVerifyResult, currentLoanKey: {}, strategyType: {}", currentLoanKey, strategyType);
        String loanKey = currentLoanKey;
        VerifyResult lastVerifyResult = verifyResultService.getByUserKeyAndLoanId(
                userKey, loanId);
        if (lastVerifyResult != null && StringUtils.isNotEmpty(lastVerifyResult.getLoanKey())) {
            loanKey = lastVerifyResult.getLoanKey();
        }
        return loanKey;
    }

    /**
     * 保存用户额度信息
     *
     * @param param        param
     * @param sourceSystem sourceSystem
     * @param userKey      userKey
     * @param loanKey      loanKey
     * @param loanId       loanId
     * @return result
     */
    public BooleanResult saveUserAmount(JSONObject param, String sourceSystem, String userKey, String loanKey, Integer loanId) {
        // 更新额度前,先lock
        BooleanResult booleanResult = new BooleanResult(false);
        JSONObject verifyResult = param.getJSONObject("verifyResult");
        String strategyType = param.getString("strategyType");
        VerifyUserLineManagementReaudit verifyUserLineManagementReaudit = JSON.parseObject(verifyResult.toJSONString(), VerifyUserLineManagementReaudit.class);
        // id置空
        verifyUserLineManagementReaudit.setId(null);
        verifyUserLineManagementReaudit.setUserKey(userKey);
        verifyUserLineManagementReaudit.setSourceSystem(sourceSystem);
        verifyUserLineManagementReaudit.setLoanId(loanId);
        verifyUserLineManagementReaudit.setLoanKey(loanKey);
        verifyUserLineManagementReaudit.setStrategyType(strategyType);
        verifyUserLineManagementReaudit.setStep((String) param.get("step"));
        verifyUserLineManagementReaudit.setIsFinalPassed(true);
        if (verifyResult.containsKey("originResult")) {
            JSONObject originResult = verifyResult.getJSONObject("originResult");
            if (originResult.containsKey("is_passed"))
                verifyUserLineManagementReaudit.setIsFinalPassed(originResult.getBoolean("is_passed"));
        }

        verifyUserLineManagementReaudit.setVersion(verifyUserLineManagementReaudit.getVersion() == null ? 1 : verifyUserLineManagementReaudit.getVersion() + 1);
        dealTempLineData(verifyResult.getJSONArray("ext1"), verifyUserLineManagementReaudit);
        VerifyUserLineManagementReaudit lastLineManagement = verifyUserLineManagementReauditMapper.getByUserKey(userKey);
        if (lastLineManagement != null) {
            verifyUserLineManagementReaudit.setLastId(Integer.valueOf(String.valueOf(lastLineManagement.getId())));
        }

        // 默认线下策略执行成功，变更用户额度记录其他为失效
        verifyUserLineManagementReaudit.setIsActive(true);
        verifyUserLineManagementReaudit.setStatus(true);
        verifyUserLineManagementReaudit.setLineAssignTime(new Date());
        verifyUserLineManagementReaudit.setCreateTime(new Date());
        verifyUserLineManagementReaudit.setUpdateTime(new Date());
        if (verifyUserLineManagementReaudit.getTestCode().length() > 512) {
            verifyUserLineManagementReaudit.setTestCode(verifyUserLineManagementReaudit.getTestCode().substring(0, 512));
        }
        if (verifyUserLineManagementReaudit.getSegmentCode().length() > 512) {
            verifyUserLineManagementReaudit.setSegmentCode(verifyUserLineManagementReaudit.getSegmentCode().substring(0, 512));
        }
        if (verifyUserLineManagementReaudit.getLastId() != null) {
            verifyUserLineManagementReauditMapper.updateNoInvalid(verifyUserLineManagementReaudit.getUserKey(), verifyUserLineManagementReaudit.getLastId());
        }
        verifyUserLineManagementReauditMapper.saveVerifyUserLineManagementReaudit(verifyUserLineManagementReaudit);

        return booleanResult.message("update amount success");
    }

    /**
     * 更新错误的额度数据
     * @param param
     * @param sourceSystem
     * @param userKey
     * @param loanKey
     * @return
     */
    public BooleanResult updateUserAmount(JSONObject param, String sourceSystem, String userKey, String loanKey) {
        // 更新额度前,先lock
        BooleanResult booleanResult = new BooleanResult(false);
        JSONObject verifyResult = param.getJSONObject("verifyResult");
        String strategyType = param.getString("strategyType");
        VerifyUserLineManagementReaudit verifyUserLineManagementReaudit = JSON.parseObject(verifyResult.toJSONString(), VerifyUserLineManagementReaudit.class);
        // id置空
        verifyUserLineManagementReaudit.setId(null);
        verifyUserLineManagementReaudit.setUserKey(userKey);
        verifyUserLineManagementReaudit.setSourceSystem(sourceSystem);
        verifyUserLineManagementReaudit.setLoanKey(loanKey);
        verifyUserLineManagementReaudit.setStrategyType(strategyType);
        verifyUserLineManagementReaudit.setStep((String) param.get("step"));
        verifyUserLineManagementReaudit.setVersion(verifyUserLineManagementReaudit.getVersion() == null ? 1 : verifyUserLineManagementReaudit.getVersion() + 1);
        dealTempLineData(verifyResult.getJSONArray("ext1"), verifyUserLineManagementReaudit);
        // 默认线下策略执行成功，变更用户额度记录其他为失效
        verifyUserLineManagementReaudit.setIsActive(true);
        verifyUserLineManagementReaudit.setStatus(true);
        verifyUserLineManagementReaudit.setCreateTime(new Date());
        verifyUserLineManagementReaudit.setUpdateTime(new Date());
        if (verifyUserLineManagementReaudit.getTestCode().length() > 512) {
            verifyUserLineManagementReaudit.setTestCode(verifyUserLineManagementReaudit.getTestCode().substring(0, 512));
        }
        if (verifyUserLineManagementReaudit.getSegmentCode().length() > 512) {
            verifyUserLineManagementReaudit.setSegmentCode(verifyUserLineManagementReaudit.getSegmentCode().substring(0, 512));
        }
        verifyUserLineManagementReauditMapper.updateVerifyUserLineManagementReaudit(verifyUserLineManagementReaudit);

        return booleanResult.message("update amount success");
    }


    public void dealTempLineData(JSONArray ext1List, VerifyUserLineManagementReaudit userLineManagement) {
        // ext1字段长度check
        if (CollectionUtils.isNotEmpty(ext1List) && userLineManagement.getExt1().length() > 1024) {
            userLineManagement.setExt1(userLineManagement.getExt1().substring(0, 1024));
        }
        if (CollectionUtils.isNotEmpty(ext1List)) {
            for (int i = 0; i < ext1List.size(); i++) {
                JSONObject tmpRec = ext1List.getJSONObject(i);
                String type = tmpRec.getString("type");
                String tmpLineStatus = tmpRec.getString("tmp_line_status");
                // 0无临额；1提临额；2临额中；3额度失效
                if ("0".equals(tmpLineStatus) || "3".equals(tmpLineStatus)) {
                    continue;
                }
                Date tmpLineEndTime = tmpRec.getDate("tmp_line_end_time");
                switch (type) {
                    // 1 好借；2 好买
                    case "1":
                        userLineManagement.setTmpLoanLineEndTime(tmpLineEndTime);
                        break;
                    case "2":
                        userLineManagement.setTmpShopLineEndTime(tmpLineEndTime);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @Override
    public JsonResultVo queryByUserKey(String userKey) {
        JsonResultVo jsonResultVo = JsonResultVo.success();
        Map<String, Object> resultMap = getDefault();
        if (StringUtils.isBlank(userKey)) {
            jsonResultVo.setData(resultMap);
            return jsonResultVo;
        }
        // 查询信息
        final VerifyUserLineManagementReaudit info = verifyUserLineManagementReauditMapper.getByUserKey(userKey);
        if (Objects.isNull(info)) {
            jsonResultVo.setData(resultMap);
            return jsonResultVo;
        }
        resultMap.put(REASON_CODE, info.getReasonCode());
        resultMap.put(IS_FINAL_PASSED, info.getIsFinalPassed());
        LoggerProxy.info("VerifyUserLineManagementReauditServiceImpl.queryByUserKey", logger, "resultMap.keys={}", resultMap.keySet());
        jsonResultVo.setData(resultMap);
        return jsonResultVo;
    }

    private Map<String, Object> getDefault() {
        Map<String, Object> defaultMap = Maps.newHashMap();
        defaultMap.put(REASON_CODE, "null");
        defaultMap.put(IS_FINAL_PASSED, "null");
        return defaultMap;
    }

}
