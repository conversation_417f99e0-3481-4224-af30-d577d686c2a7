package com.youxin.risk.verify.service;

import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.verify.vo.DcReqVo;

import java.util.Map;


/**
 * Dc系统服务类
 *
 * <AUTHOR> @version
 */
public interface DcSystemService {

    DcReqVo buildDcReq(Map<String, Object> params, SubmitDataType submitDataType, String userKey);

    boolean submitDcData(Object postData) ;





}
