package com.youxin.risk.verify.strategy;

import com.youxin.risk.verify.config.VerifyConfigLoader;
import com.youxin.risk.verify.constants.VerifyChannelConstants;
import com.youxin.risk.verify.enums.VerifyStrategyType;
import com.youxin.risk.verify.strategy.req.RouteStrategyReq;
import com.youxin.risk.verify.strategy.req.StrategyReq;
import com.youxin.risk.verify.strategy.res.StrategyRes;
import com.youxin.risk.verify.vo.VerifyUserLineManagementVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2018-11-09
 */
@Component
public class PythonAnalysisServiceWrapper {

    private final static Logger LOG = LoggerFactory.getLogger(PythonAnalysisServiceWrapper.class);

    @Resource
    private com.youxin.risk.verify.strategy.RMService rmService;



    public StrategyRes<VerifyUserLineManagementVo> wrapExecUserLinePythonAnalysis(String xml, VerifyChannelConstants sourceSystem, VerifyStrategyType strategyType, String strategyNumber, String loanKey, String userKey, Integer loanId) {

        StrategyRes<VerifyUserLineManagementVo> rmResult = executeStrategyInRM(null, xml, sourceSystem, strategyType, strategyNumber, loanKey, userKey, VerifyUserLineManagementVo.class);
        makeUserLineResultCompatible(rmResult.getData());

        return rmResult;
    }
    private <T> StrategyRes<T> executeStrategyInRM(String step, String xml, VerifyChannelConstants sourceSystem, VerifyStrategyType strategyType, String strategyNumber, String loanKey, String userKey, Class<T> clazz) {
        String routeStrategyType;
        if(routeToFS("STRATEGY_FLOW_MERGE_RATE_" + strategyType.name())) {
            // route strategy type
            RouteStrategyReq routeStrategyReq = RouteStrategyReq.build(sourceSystem.name(), strategyType.name(), strategyNumber, userKey);
            routeStrategyType = rmService.routeStrategy(routeStrategyReq);
        } else {
            routeStrategyType = strategyType.name() + "_ALL";
        }

        // run strategy
        StrategyReq req = StrategyReq.build(step, xml, sourceSystem.name(), strategyType.name(), routeStrategyType, loanKey, userKey);
        StrategyRes<T> res = rmService.runStrategy(req, clazz);
        res.setStrategyType(routeStrategyType);
        return res;
    }

    private void makeUserLineResultCompatible(VerifyUserLineManagementVo rmResult) {
        final int maxStringLength = 512;
        if (rmResult.getSegmentCode().length() > maxStringLength) {
            rmResult.setSegmentCode(rmResult.getSegmentCode().substring(0, maxStringLength));
        }
        if (rmResult.getTestCode().length() > maxStringLength) {
            rmResult.setTestCode(rmResult.getTestCode().substring(0, maxStringLength));
        }
        rmResult.setStatus(null);
    }

    private boolean routeToFS(String strategyFlowRate) {
        String value = VerifyConfigLoader.getValue(strategyFlowRate);
        if(StringUtils.isBlank(value)) {
            return true;
        }
        int migrateRate = Integer.parseInt(value);
        return migrateRate < ThreadLocalRandom.current().nextInt(1, 101);
    }
}
