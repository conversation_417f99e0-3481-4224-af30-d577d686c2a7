package com.youxin.risk.verify.schedule;

import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.CaesarUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.blacklist.mapper.*;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MaskBlackListJob implements XxlJobBase {

    private static final Logger LOG = LoggerFactory.getLogger(MaskBlackListJob.class);

    @Autowired
    private UserBlackListMapper userBlackListMapper;

    @Autowired
    private UserGreyListMapper userGreyListMapper;

    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private ContactGreyMapper contactGreyMapper;

    @Autowired
    private CallDetailMapper callDetailMapper;

    @XxlJob(value = "maskBlackListJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        JSONObject paramObj = JSONObject.parseObject(param);
        Long minId = paramObj.getLong("minId");
        long maxId = paramObj.getLong("maxId");
        int pageSize = paramObj.getIntValue("pageSize");
        String typeStr = paramObj.getString("type");
        LOG.info("maskBlackListJob execute start, type: {}", typeStr);
        int index = minId.intValue();
        if ("black".equals(typeStr)) {
            index = dealBlackList(index, maxId, pageSize);
        } else if ("grey".equals(typeStr)) {
            index = dealGrey(index, maxId, pageSize);
        } else if ("contact".equals(typeStr)) {
            index = dealContact(index, maxId, pageSize);
        } else if ("contactGrey".equals(typeStr)) {
            index = dealContactGrey(index, maxId, pageSize);
        } else {
            index = dealByTable(index, maxId, pageSize, typeStr);
        }
        LOG.info("maskBlackListJob execute end, index: {}, type: {}", index, typeStr);
        return ReturnT.SUCCESS;
    }

    private int dealBlackList(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = userBlackListMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String mobile = (String) register.get("mobile");
                    String idNumber = (String) register.get("id_number");
                    String bankCard = (String) register.get("bank_card");
                    try {
                        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(idNumber) && StringUtils.isBlank(bankCard)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(mobile) || !CaesarUtil.isEncrypted(idNumber) || !CaesarUtil.isEncrypted(bankCard)) {
                            mobile = MaskUtils.maskValue(mobile);
                            idNumber = MaskUtils.maskValue(idNumber);
                            bankCard = MaskUtils.maskValue(bankCard);
                            register.put("mobile", mobile);
                            register.put("id_number", idNumber);
                            register.put("bank_card", bankCard);
                            register.put("id", register.get("id"));
                            userBlackListMapper.updateById(register);
                            LoggerProxy.info("maskBlackListJob", LOG, "updateById, id: {}, type: blacklist", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskBlackListJob", LOG, "处理异常, id: {}, type: blacklist", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealGrey(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = userGreyListMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String mobile = (String) register.get("mobile");
                    String idNumber = (String) register.get("id_number");
                    String bankCard = (String) register.get("bank_card");
                    try {
                        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(idNumber) && StringUtils.isBlank(bankCard)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(mobile) || !CaesarUtil.isEncrypted(idNumber) || !CaesarUtil.isEncrypted(bankCard)) {
                            mobile = MaskUtils.maskValue(mobile);
                            idNumber = MaskUtils.maskValue(idNumber);
                            bankCard = MaskUtils.maskValue(bankCard);
                            register.put("mobile", mobile);
                            register.put("id_number", idNumber);
                            register.put("bank_card", bankCard);
                            register.put("id", ((Long) register.get("id")));
                            userGreyListMapper.updateById(register);
                            LoggerProxy.info("maskBlackListJob", LOG, "updateById, id: {}, type: grey", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskBlackListJob", LOG, "处理异常, id: {}, type: grey", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealContact(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = contactMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String mobile = (String) register.get("phonenum");
                    try {
                        if (StringUtils.isBlank(mobile)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(mobile)) {
                            mobile = MaskUtils.maskValue(mobile);
                            register.put("phonenum", mobile);
                            register.put("id", register.get("id"));
                            contactMapper.updateById(register);
                            LoggerProxy.info("maskBlackListJob", LOG, "updateById, id: {}, type: contact", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskBlackListJob", LOG, "mobile解密失败, id: {}, type: contact", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealContactGrey(int index, long maxId, int pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            List<Map<String, Object>> registers = contactGreyMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String mobile = (String) register.get("phonenum");
                    try {
                        if (StringUtils.isBlank(mobile)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(mobile)) {
                            mobile = MaskUtils.maskValue(mobile);
                            register.put("phonenum", mobile);
                            register.put("id", register.get("id"));
                            contactGreyMapper.updateById(register);
                            LoggerProxy.info("maskBlackListJob", LOG, "updateById, id: {}, type: contactGrey", register.get("id"));
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskBlackListJob", LOG, "mobile解密失败, id: {}, type: contactGrey", register.get("id"), e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

    private int dealByTable(int index, long maxId, int pageSize, String tableName) {
        Map<String, Object> paramMap = new HashMap<>();
        while (index <= maxId) {
            paramMap.put("start", index);
            paramMap.put("end", index + pageSize);
            paramMap.put("tableName", tableName);
            List<Map<String, Object>> registers = callDetailMapper.queryById(paramMap);
            if (CollectionUtils.isNotEmpty(registers)) {
                for (Map<String, Object> register : registers) {
                    String mobile = (String) register.get("phonenum");
                    try {
                        if (StringUtils.isBlank(mobile)) {
                            continue;
                        }
                        if (!CaesarUtil.isEncrypted(mobile)) {
                            mobile = MaskUtils.maskValue(mobile);
                            register.put("phonenum", mobile);
                            register.put("id", ((BigInteger) register.get("id")).longValue());
                            register.put("tableName", tableName);
                            callDetailMapper.updateById(register);
                            LoggerProxy.info("maskBlackListJob", LOG, "updateById, id: {}, type: {}", register.get("id"), tableName);
                            Thread.sleep(1);
                        }
                    } catch (Exception e) {
                        LoggerProxy.error("maskBlackListJob", LOG, "mobile解密失败, id: {}, type: {}", register.get("id"), tableName, e);
                    }
                }
            }
            index = index + pageSize;
        }
        return index;
    }

}
