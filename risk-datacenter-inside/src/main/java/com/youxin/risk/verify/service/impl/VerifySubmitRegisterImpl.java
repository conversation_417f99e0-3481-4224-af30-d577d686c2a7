package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.verify.mapper.VerifySubmitRegisterMapper;
import com.youxin.risk.verify.model.VerifySubmitRegister;
import com.youxin.risk.verify.service.VerifySubmitRegisterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class VerifySubmitRegisterImpl implements VerifySubmitRegisterService {

    private static final Logger logger = LoggerFactory.getLogger(VerifySubmitRegisterImpl.class);

    @Autowired
    private VerifySubmitRegisterMapper verifySubmitRegisterMapper;

    @Autowired
    private DcSubmitRegisterMapper dcSubmitRegisterMapper;

    @Override
    public void saveSubmitRegister(VerifySubmitRegister register) {
        verifySubmitRegisterMapper.insert(register);
    }

    @Override
    public VerifySubmitRegister findSubmitRegisterByUserKey(String userKey) {
        // 先从dc获取，未获取到从verify获取
        final DcSubmitRegister dcSubmitRegister = dcSubmitRegisterMapper.getByUserKey(userKey);
        if (dcSubmitRegister == null) {
            logger.info("findSubmitRegisterByUserKey dc not exist, userKey: {}", userKey);
            return verifySubmitRegisterMapper.findLastSubmitInfoByUserKey(userKey);
        }
        VerifySubmitRegister lastSubmitInfoByUserKey = new VerifySubmitRegister();
        lastSubmitInfoByUserKey.setMobile(dcSubmitRegister.getMobile());
        lastSubmitInfoByUserKey.setUserKey(dcSubmitRegister.getUserKey());
        lastSubmitInfoByUserKey.setTongdunFingerprint(dcSubmitRegister.getTongdunFingerprint());
        lastSubmitInfoByUserKey.setCreateTime(dcSubmitRegister.getCreateTime());
        lastSubmitInfoByUserKey.setUpdateTime(dcSubmitRegister.getUpdateTime());
        return lastSubmitInfoByUserKey;
    }

}
