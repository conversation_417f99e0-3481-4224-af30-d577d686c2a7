package com.youxin.risk.verify.service.impl;

import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.dao.verify.VerifyAnalysisReportSmallMapper;
import com.youxin.risk.commons.model.verify.VerifyAnalysisReport;
import com.youxin.risk.hbase.HbaseService;
import com.youxin.risk.verify.hbase.VerifyAnalysisReportHB;
import com.youxin.risk.verify.service.VerifyReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


@Service
public class VerifyReportServiceImpl implements VerifyReportService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyReportServiceImpl.class);

    @Autowired
    private VerifyAnalysisReportSmallMapper verifyAnalysisReportSmallMapper;

    @Autowired()
    private HbaseService hbaseService;

    @Override
    public VerifyAnalysisReport saveOrUpdateReport(VerifyAnalysisReport report) {
        if (report.getId() == null) {
            this.verifyAnalysisReportSmallMapper.persist(report);
        } else {
            this.verifyAnalysisReportSmallMapper.update(report);
        }
       return report;
    }

    @Override
    public VerifyAnalysisReport getReportByLoanId(Integer loanId, String userKey) {
        return this.verifyAnalysisReportSmallMapper.findVerifyAnalysisReportByLoanId(loanId, userKey);
    }


    @Override
    public VerifyAnalysisReport getReportByLoanKey(String loanKey) {
        return this.verifyAnalysisReportSmallMapper.findByLoanKey(loanKey);
    }

    @Override
    public VerifyAnalysisReportHB queryHbase(String rowkey) {
        VerifyAnalysisReportHB report = new VerifyAnalysisReportHB();
        hbaseService.queryByRowkey(rowkey, report);
        return report;

    }

    @Override
    public Date findMinCreateTime() {
        return this.verifyAnalysisReportSmallMapper.findMinCreateTime();
    }

    @Override
    public int findMinId() {
        return this.verifyAnalysisReportSmallMapper.findMinId();
    }

    @Override
    public int deleteById(int id) {
        int limit = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.DATACENTER_NAMESPACE, "delete.analysisReportSmall.limit", "10"));
        int rows = this.verifyAnalysisReportSmallMapper.deleteById(id, limit);
        LOG.info("清理数据影响行数：{}", rows);
        return rows;
    }


}
