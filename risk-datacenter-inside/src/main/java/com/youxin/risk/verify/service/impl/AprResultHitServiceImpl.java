package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.verify.VerifyResultHitMapper;
import com.youxin.risk.commons.model.verify.VerifyResultHit;
import com.youxin.risk.ra.mapper.FirstLoanTouchMonitorConfigMapper;
import com.youxin.risk.verify.service.ResultHitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: apr监控处理类
 * @author: juxiang
 * @create: 2022-05-06 20:28
 **/
@Service
public class AprResultHitServiceImpl implements ResultHitService {
    @Autowired
    VerifyResultHitMapper verifyResultHitMapper;
    @Autowired
    FirstLoanTouchMonitorConfigMapper firstLoanTouchMonitorConfigMapper;
    @Override
    public List<Map<String, Object>> calculateData(JSONObject params) {
        List<VerifyResultHit> aprHitList = verifyResultHitMapper.queryAprHitRateNew(params);
        List<String> fieldNameList=firstLoanTouchMonitorConfigMapper.selectByMonitorType("apr");
        List<Map<String,Object>> result=new ArrayList<>();
        Map<String,Object> map = new HashMap<>();
        result.add(map);
        map.put("create_hour",params.getString("startTime"));
        // 初始化值
        fieldNameList.forEach(column->{
            map.put(column,0);
        });
        Integer passTotal=0;
        map.put("passTotal",passTotal);
        map.put("cnt_loan_id",aprHitList.size());
        for(VerifyResultHit verifyResultHit:aprHitList){
            String reasonCode = verifyResultHit.getReasonCode();
            Boolean isFinalPassed = verifyResultHit.getIsFinalPassed();
            fieldNameList.forEach(column->{
                if(reasonCode.contains(column)){
                    Integer value=(Integer)map.get(column);
                    map.put(column,value+1);
                }
            });
            if (isFinalPassed){
                passTotal++;
            }
        }
        map.put("cnt_loan_id",aprHitList.size());
        map.put("passTotal",passTotal);
        return result;
    }
}
