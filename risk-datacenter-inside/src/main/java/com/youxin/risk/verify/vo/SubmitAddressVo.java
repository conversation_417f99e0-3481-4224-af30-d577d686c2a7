package com.youxin.risk.verify.vo;

import com.youxin.risk.ra.annotation.ParameterEnum;
import com.youxin.risk.verify.annotion.ParameterNullable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class SubmitAddressVo extends VerifyCommonVo {
    private String province;
    private String city;
    private String district;
    // 地址新增省市区编码，考虑后续系统改造，新增字段暂只留存数据中心
    private String provinceCd;
    private String cityCd;
    private String districtCd;
    private String liveDuration;
    private String liveAddress;
    private String marriage;
    private String childNum;
    private String eduexperience;
    
    public String getProvinceCd() {
		return provinceCd;
	}

	public void setProvinceCd(String provinceCd) {
		this.provinceCd = provinceCd;
	}

	public String getCityCd() {
		return cityCd;
	}

	public void setCityCd(String cityCd) {
		this.cityCd = cityCd;
	}

	public String getDistrictCd() {
		return districtCd;
	}

	public void setDistrictCd(String districtCd) {
		this.districtCd = districtCd;
	}

	public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    @ParameterNullable
    public String getChildNum() {
        return childNum;
    }

    public void setChildNum(String childNum) {
        this.childNum = childNum;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    @ParameterNullable
    @ParameterEnum
    public String getLiveDuration() {
        return liveDuration;
    }

    public void setLiveDuration(String liveDuration) {
        this.liveDuration = liveDuration;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
            ToStringStyle.SHORT_PREFIX_STYLE);
    }

    @ParameterNullable
    public String getLiveAddress() {
        return liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress;
    }

    public String getEduexperience() {
        return eduexperience;
    }

    public void setEduexperience(String eduexperience) {
        this.eduexperience = eduexperience;
    }
}
