package com.youxin.risk.verify.kafka;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.verify.VerifyAnalysisReport;
import com.youxin.risk.hbase.HbaseService;
import com.youxin.risk.verify.hbase.VerifyAnalysisReportHB;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.listener.MessageListener;


public class ReportMsgListener implements MessageListener<String, String> {

    private static final Logger LOG = LoggerFactory.getLogger(ReportMsgListener.class);
    
    @Autowired
    private HbaseService hbaseService;

	@Override
	public void onMessage(ConsumerRecord<String, String> data) {
		if(StringUtils.isBlank(data.value())) {
			return;
		}
		VerifyAnalysisReport report = JSON.parseObject(data.value(), VerifyAnalysisReport.class);
		LOG.info("get msg VerifyAnalysisReport,id:{}",report.getId());
		putInHbase(report);
		
	}
	
	private void putInHbase(VerifyAnalysisReport report) {
		VerifyAnalysisReportHB reportHB = new VerifyAnalysisReportHB(report);
		hbaseService.put(reportHB);
	}

}
