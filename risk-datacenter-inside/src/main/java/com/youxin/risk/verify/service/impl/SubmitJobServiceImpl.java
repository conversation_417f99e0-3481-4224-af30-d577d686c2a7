package com.youxin.risk.verify.service.impl;


import com.youxin.risk.commons.dao.datacenter.DcSubmitJobMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitJob;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.verify.mapper.VerifySubmitJobMapper;
import com.youxin.risk.verify.model.VerifySubmitJob;
import com.youxin.risk.verify.service.SubmitJobService;
import com.youxin.risk.verify.vo.SubmitJobVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("verifySubmitJobService")
public class SubmitJobServiceImpl implements SubmitJobService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubmitJobServiceImpl.class);

    @Autowired
    private VerifySubmitJobMapper submitJobMapper;

    @Autowired
    private DcSubmitJobMapper dcSubmitJobMapper;

    @Override
    public SubmitJobVo getJobInfoByUserKey(String userKey) {
        DcSubmitJob dcSubmitJob = dcSubmitJobMapper.getByUserKey(userKey);
        SubmitJobVo vo = null;
        if (Objects.isNull(dcSubmitJob)) {
            VerifySubmitJob job = submitJobMapper.findLastSubmitInfoByUserKeyNoId(userKey);
            try {
                vo = ObjectTransferUtils.transferObject(job, SubmitJobVo.class);
            } catch (InstantiationException | IllegalAccessException
                    | SecurityException e) {
            }
        } else {
            try {
                vo = ObjectTransferUtils.transferObject(dcSubmitJob, SubmitJobVo.class);
            } catch (Exception ex) {
                LOGGER.error("getJobInfoByUserKey,dc转换异常,userKey: {}", userKey, ex);
            }
        }
        return vo;
    }
}
