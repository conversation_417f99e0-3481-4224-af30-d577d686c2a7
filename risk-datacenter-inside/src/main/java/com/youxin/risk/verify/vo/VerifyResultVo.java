package com.youxin.risk.verify.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

public class VerifyResultVo {
    private Integer id;

    private String reason;

    private String reasonCode;

    private Integer loanId;

    private String loanKey;

    private String userKey;

    private String sourceSystem;

    private Double score;

    private Boolean isAutoPassed;

    private Boolean isManualPassed;

    private Boolean isManual;

    private Date autoVerifyTime;

    private Date manualVerifyTime;

    private Date updateTime;

    private Date createTime;

    private Date finalVerifyTime;

    private Boolean isFinalPassed;

    private Date autoLockDays;

    private Date manualLockDays;

    private Integer strategyId;

    private Integer operatorId;

    private String reasonCodeUser;

    private Boolean isReduceAmountPass;

    private Double newAmount;

    private Boolean isNewAmountAccepted;

    private Date newAmountExpiry;

    private Date userRespondTime;

    private Double loanAmount;//批核额度

    private Integer loanPeriodNos;//分期数

    private Double loanRate;//费率

    private Integer userLevelId;

    private String step;

    private String periodAmount;

    private Double btAmount;//好还额度

    private Integer btPeriodNos;//好还分期数

    private Double btRate;//好还费率

    private Double totalAmount;//总额度


    public String getPeriodAmount() {
		return this.periodAmount;
	}

	public void setPeriodAmount(String periodAmount) {
		this.periodAmount = periodAmount;
	}

	public Integer getOperatorId() {
        return this.operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getStrategyId() {
        return this.strategyId;
    }

    public void setStrategyId(Integer strategyId) {
        this.strategyId = strategyId;
    }

    public Date getAutoLockDays() {
        return this.autoLockDays;
    }

    public void setAutoLockDays(Date autoLockDays) {
        this.autoLockDays = autoLockDays;
    }

    public Date getManualLockDays() {
        return this.manualLockDays;
    }

    public void setManualLockDays(Date manualLockDays) {
        this.manualLockDays = manualLockDays;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReasonCode() {
        return this.reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public Integer getLoanId() {
        return this.loanId;
    }

    public void setLoanId(Integer loanId) {
        this.loanId = loanId;
    }

    public String getUserKey() {
        return this.userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public Double getScore() {
        return this.score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Boolean getIsAutoPassed() {
        return this.isAutoPassed;
    }

    public void setIsAutoPassed(Boolean isAutoPassed) {
        this.isAutoPassed = isAutoPassed;
    }

    public Boolean getIsManualPassed() {
        return this.isManualPassed;
    }

    public void setIsManualPassed(Boolean isManualPassed) {
        this.isManualPassed = isManualPassed;
    }

    public Date getAutoVerifyTime() {
        return this.autoVerifyTime;
    }

    public void setAutoVerifyTime(Date autoVerifyTime) {
        this.autoVerifyTime = autoVerifyTime;
    }

    public Date getManualVerifyTime() {
        return this.manualVerifyTime;
    }

    public void setManualVerifyTime(Date manualVerifyTime) {
        this.manualVerifyTime = manualVerifyTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getFinalVerifyTime() {
        return this.finalVerifyTime;
    }

    public void setFinalVerifyTime(Date finalVerifyTime) {
        this.finalVerifyTime = finalVerifyTime;
    }

    public Boolean getIsFinalPassed() {
        return this.isFinalPassed;
    }

    public void setIsFinalPassed(Boolean isFinalPassed) {
        this.isFinalPassed = isFinalPassed;
    }

	public String getReasonCodeUser() {
		return this.reasonCodeUser;
	}

	public void setReasonCodeUser(String reasonCodeUser) {
		this.reasonCodeUser = reasonCodeUser;
	}

    public Boolean getIsReduceAmountPass() {
		return this.isReduceAmountPass;
	}

	public void setIsReduceAmountPass(Boolean isReduceAmountPass) {
		this.isReduceAmountPass = isReduceAmountPass;
	}

	public Double getNewAmount() {
		return this.newAmount;
	}

	public void setNewAmount(Double newAmount) {
		this.newAmount = newAmount;
	}

	public Boolean getIsNewAmountAccepted() {
		return this.isNewAmountAccepted;
	}

	public void setIsNewAmountAccepted(Boolean isNewAmountAccepted) {
		this.isNewAmountAccepted = isNewAmountAccepted;
	}

	public Date getNewAmountExpiry() {
		return this.newAmountExpiry;
	}

	public void setNewAmountExpiry(Date newAmountExpiry) {
		this.newAmountExpiry = newAmountExpiry;
	}

	public Date getUserRespondTime() {
		return this.userRespondTime;
	}

	public void setUserRespondTime(Date userRespondTime) {
		this.userRespondTime = userRespondTime;
	}

	public Double getLoanAmount() {
		return this.loanAmount;
	}

	public void setLoanAmount(Double loanAmount) {
		this.loanAmount = loanAmount;
	}

	public Integer getLoanPeriodNos() {
		return this.loanPeriodNos;
	}

	public void setLoanPeriodNos(Integer loanPeriodNos) {
		this.loanPeriodNos = loanPeriodNos;
	}

	public Double getLoanRate() {
		return this.loanRate;
	}

	public void setLoanRate(Double loanRate) {
		this.loanRate = loanRate;
	}

	public Integer getUserLevelId() {
		return this.userLevelId;
	}

	public void setUserLevelId(Integer userLevelId) {
		this.userLevelId = userLevelId;
	}

	public String getStep() {
		return this.step;
	}

	public void setStep(String step) {
		this.step = step;
	}

	public Double getBtAmount() {
		return this.btAmount;
	}

	public void setBtAmount(Double btAmount) {
		this.btAmount = btAmount;
	}

	public Integer getBtPeriodNos() {
		return this.btPeriodNos;
	}

	public void setBtPeriodNos(Integer btPeriodNos) {
		this.btPeriodNos = btPeriodNos;
	}

	public Double getBtRate() {
		return this.btRate;
	}

	public void setBtRate(Double btRate) {
		this.btRate = btRate;
	}

	public Double getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(Double totalAmount) {
		this.totalAmount = totalAmount;
	}

	public Boolean getIsManual() {
		return this.isManual;
	}

	public void setIsManual(Boolean isManual) {
		this.isManual = isManual;
	}

	public String getLoanKey() {
		return this.loanKey;
	}

	public void setLoanKey(String loanKey) {
		this.loanKey = loanKey;
	}

	@Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
            ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
