package com.youxin.risk.ra.enums;

import com.youxin.risk.ra.service.CommonApi;
import org.springframework.http.HttpMethod;

import java.util.Map;

public enum AccountSystemApi implements CommonApi {
	LOAN_HISTORY_BY_USER_KEY {
		@Override
		public String toString() {
			return "依据用户id查找所有借款";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.loanByUserKeyUrl;
		}

	},
	NEW_LOAN_HISTORY_BY_USER_KEY {
		@Override
		public String toString() {
			return "依据用户id查找所有借款-新接口";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.newLoanByUserKeyUrl;
		}

	},
	NEW_LOAN_SOURCE_BY_USER_KEY {
		@Override
		public String toString() {
			return "依据用户id查找所有借款来源";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.accountGetLoanSourceByUserKeyUrl;
		}

	},
	NEW_DELAY_CHECK_LOAN_HISTORY_BY_USER_KEY {
		@Override
		public String toString() {
			return "依据用户id查找所有借款-新接口-延迟检查";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseNewUrl
					+ AccountSystemConstants.newDelayCheckLoanByUserKeyUrl;
		}

	},
	LOAN_HISTORY_BY_MOBILE {
		@Override
		public String toString() {
			return "依据手机号查询所有的进件历史";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.loanByMobileUrl;
		}

	},
	LOAN_INFO_BY_LOAN_ID {
		@Override
		public String toString() {
			return "根据借款id查询借款信息";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.loanByIdUrl;
		}
	},
	LOAN_INFO_BY_LOAN_ID_LIST {

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.loanByIdListUrl;
		}

	},
	LOAN_STATUS_BY_LOAN_LIST {

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.GET;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.loanStatusByLoanListUrl;
		}

	},
	BIND_CARD {
		@Override
		public String toString() {
			return "新网绑卡";
		}

		@Override
		public HttpMethod getHttpMethod() {
			return HttpMethod.POST;
		}

		@Override
		public String getPath() {
			return AccountSystemConstants.baseUrl
					+ AccountSystemConstants.bindCardURL;
		}

		@Override
	    public Map<String, String> getheader() {
	        return AccountSystemConstants.jsonHeader;
	    }

	};
	@Override
	public Map<String, String> getheader() {
		return AccountSystemConstants.header;
	}

	@Override
	public boolean debug() {
		return false;
	}
}
