package com.youxin.risk.ra.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
public enum RiskProduct {
	PAY_DAY_LOAN {

		@Override
		public String getAccountSystemPartnerName() {
			return "PAY_DAY_LOAN";
		}

		@Override
		public List<String> getBindSourceSystem() {
			List<String> ret = new ArrayList<>();
			ret.add(PAY_DAY_LOAN.name());
			ret.add(RONG_360.name());
			return ret;
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 1800;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "PAY_DAY_LOAN";
		}

		@Override
		public boolean runModel(ReportStep step) {
			return false;
		}
	},
	REN_REN_DAI {


		@Override
		public String getAccountSystemPartnerName() {
            return "RRD";
		}

		@Override
		public List<String> getBindSourceSystem() {
			List<String> ret = new ArrayList<>();
			ret.add(REN_REN_DAI.name());
			return ret;
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 300;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "REN_REN_DAI";
		}


		@Override
		public boolean runModel(ReportStep step) {
			return true;
		}
	},
	YI_MEI {

		@Override
		public String getAccountSystemPartnerName() {
			return "YI_MEI";
		}

		@Override
		public List<String> getBindSourceSystem() {
			List<String> ret = new ArrayList<>();
			ret.add(YI_MEI.name());
			return ret;
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 300;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "YI_MEI";
		}


		@Override
		public boolean runModel(ReportStep step) {
			return true;
		}
	},
	THREAD {

		@Override
		public String getAccountSystemPartnerName() {
			return "THREAD";
		}

		@Override
		public List<String> getBindSourceSystem() {
			List<String> ret = new ArrayList<>();
			ret.add(THREAD.name());
			return ret;
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 300;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "THREAD";
		}


		@Override
		public boolean runModel(ReportStep step) {
			return true;
		}
	},
	HAO_HUAN {


		@Override
		public String getAccountSystemPartnerName() {
			return "HAOHUAN";
		}

		@Override
		public List<String> getBindSourceSystem() {
			List<String> ret = new ArrayList<>();
			ret.add(HAO_HUAN.name());
			return ret;
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 1800;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "HAO_HUAN";
		}


		@Override
		public boolean runModel(ReportStep step) {
			return step == null || ReportStep.B.equals(step);
		}

	},
	ZHAOLIAN {
		@Override
		public String getAccountSystemPartnerName() {
			return "HAOHUAN";
		}

		@Override
		public List<String> getBindSourceSystem() {
			List<String> ret = new ArrayList<>();
			ret.add(HAO_HUAN.name());
			return ret;
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 180;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "HAO_HUAN";
		}


		@Override
		public boolean runModel(ReportStep step) {
			return false;
		}
	},
	RONG_360 {


		@Override
		public String getAccountSystemPartnerName() {
			// 账务系统中rong360与paydayloan公用同一个sourceSystem
			return "PAY_DAY_LOAN";
		}

		@Override
		public List<String> getBindSourceSystem() {
			return PAY_DAY_LOAN.getBindSourceSystem();
		}

		@Override
		public int getMobileRecordExpireTime() {
			return 1800;
		}

		@Override
		public String getAntiFraudModelBusinessLine() {
			return "PAY_DAY_LOAN";
		}


		@Override
		public boolean runModel(ReportStep step) {
			return true;
		}
	};

	public static RiskProduct findProduct(String sourceSystem) {
		for (RiskProduct product : RiskProduct.values()) {
			if (product.name().equals(sourceSystem)) {
				return product;
			}
		}
		return null;
	}

	public String getAndroidSecretKey() {
		return null;
	}

	public String getIOSSecretKey() {
		return null;
	}

	public String getIOSEventId() {
		return null;
	}

	public String getAndroidEventId() {
		return null;
	}


	public abstract String getAccountSystemPartnerName();

	public abstract int getMobileRecordExpireTime();

	public abstract String getAntiFraudModelBusinessLine();

	public static RiskProduct getRiskProduct(String sourceSystem) {
		return Enum.valueOf(RiskProduct.class, sourceSystem);
	}

	public abstract List<String> getBindSourceSystem();


	public static List<String> antiFraudTaskProducts = Arrays
			.asList(new String[] { REN_REN_DAI.name(), YI_MEI.name(),
					THREAD.name()});



	public static boolean isFirstStep(ReportStep step) {
		return step == null || ReportStep.A.equals(step);
	}

	public static boolean getOwnData(ReportStep step) {
		return step == null || ReportStep.A.equals(step);
	}

	public abstract boolean runModel(ReportStep step);

	public static List<String> payDayLoanSeriesProducts = Arrays
			.asList(new String[] { PAY_DAY_LOAN.name(), RONG_360.name(),HAO_HUAN.name() });

	public static List<String> invokeAntiFraudModelProducts = Arrays
			.asList(new String[] { });
}
