package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.mongo.vo.MobileCallHistoryVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

public class CallRecordResponseVo {
	private String systemID;
	private Long timestamp;
	private Integer count;
	private List<MobileCallHistoryVo> records;

	public String getSystemID() {
		return systemID;
	}

	public void setSystemID(String systemID) {
		this.systemID = systemID;
	}

	public Long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public List<MobileCallHistoryVo> getRecords() {
		return records;
	}

	public void setRecords(List<MobileCallHistoryVo> records) {
		this.records = records;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
