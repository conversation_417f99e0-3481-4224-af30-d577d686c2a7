/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.service.impl;
import com.youxin.risk.ra.enums.DataRequestTaskType;
import com.youxin.risk.ra.mapper.DataKexinFraudMapper;
import com.youxin.risk.ra.mapper.DataKexinScoreMapper;
import com.youxin.risk.ra.model.DataKexinFraud;
import com.youxin.risk.ra.model.DataKexinScore;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.service.DataKeXinService;
import com.youxin.risk.ra.vo.CreditxRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 创建时间：2020-07-21
 */
@Service
public class DataKeXinServiceImpl implements DataKeXinService {

    @Autowired
    private DataKexinFraudMapper dataKexinFraudMapper;

    @Autowired
    private DataKexinScoreMapper dataKexinScoreMapper;

    @Override
    public void saveData(DataRequestTask dataTask, CreditxRecordVo recordVo) {
        DataRequestTaskType taskType = dataTask.getTaskType();
        if (taskType == DataRequestTaskType.CREDITX_FRAUD_RECORDS) {
            DataKexinFraud record = new DataKexinFraud();
            record.setSourceSystem(dataTask.getSourceSystem());
            record.setUserKey(dataTask.getUserKey());
            record.setTaskId(dataTask.getId());
            record.setLoanKey(dataTask.getLoanKey());
            record.setData(recordVo.getCreditXFraudInfo().getJsonstr());
            record.setVersion(0);
            this.dataKexinFraudMapper.insert(record);
        } else if (taskType == DataRequestTaskType.CREDITX_SCORE_RECORDS) {
            DataKexinScore record = new DataKexinScore();
            record.setSourceSystem(dataTask.getSourceSystem());
            record.setUserKey(dataTask.getUserKey());
            record.setTaskId(dataTask.getId());
            record.setLoanKey(dataTask.getLoanKey());
            record.setData(recordVo.getCreditXScoreInfo().getJsonstr());
            record.setVersion(0);
            this.dataKexinScoreMapper.insert(record);
        }
    }

}
