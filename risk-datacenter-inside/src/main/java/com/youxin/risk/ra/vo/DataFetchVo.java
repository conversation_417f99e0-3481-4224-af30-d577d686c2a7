package com.youxin.risk.ra.vo;

public class DataFetchVo {
    private String systemid;

    private String jobid;

    private Integer taskid;

    private String idnumber;

    private Integer after;

    private Boolean ignoreFinished;

    private String userKey;

    private Boolean isnewest;

    private String mobile;

    private String id;

    private String queryType;

    public Integer getTaskid() {
        return taskid;
    }

    public void setTaskid(Integer taskid) {
        this.taskid = taskid;
    }

    public String getSystemid() {
        return this.systemid;
    }

    public void setSystemid(String systemid) {
        this.systemid = systemid;
    }

    public String getJobid() {
        return this.jobid;
    }

    public void setJobid(String jobid) {
        this.jobid = jobid;
    }

    public String getIdnumber() {
        return this.idnumber;
    }

    public void setIdnumber(String idnumber) {
        this.idnumber = idnumber;
    }

    public Integer getAfter() {
        return this.after;
    }

    public void setAfter(Integer after) {
        this.after = after;
    }

    public Boolean getIgnoreFinished() {
        return this.ignoreFinished;
    }

    public void setIgnoreFinished(Boolean ignoreFinished) {
        this.ignoreFinished = ignoreFinished;
    }

    public String getUserKey() {
        return this.userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public Boolean getIsnewest() {
        return this.isnewest;
    }

    public void setIsnewest(Boolean isnewest) {
        this.isnewest = isnewest;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQueryType() {
        return this.queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

}
