package com.youxin.risk.ra.kafka.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.ra.enums.DataRequestTaskType;
import com.youxin.risk.ra.kafka.IConsumer;
import com.youxin.risk.ra.kafka.IProducer;
import com.youxin.risk.ra.kafka.service.BaseKafkaService;
import com.youxin.risk.ra.kafka.vo.GraphBlackformMessageVo;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.mongo.dao.GraphBlackDao;
import com.youxin.risk.ra.mongo.vo.GraphBlackDataVo;
import com.youxin.risk.ra.service.DataRequestTaskService;
import com.youxin.risk.ra.service.impl.DataRequestTaskServiceWapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

public class GraphBlackMessageServiceImpl extends BaseKafkaService<GraphBlackformMessageVo> {

	private static final Logger LOG = LoggerFactory.getLogger(GraphBlackMessageServiceImpl.class);

	@Override
	protected Logger getLogger() {
		return LoggerFactory.getLogger(GraphBlackMessageServiceImpl.class);
	}
	
	@Override
	protected void setProducer(IProducer<GraphBlackformMessageVo> producer) {
	}

	@Autowired
	@Qualifier("graphConsumer")
	@Override
	protected void setConsumer(IConsumer<GraphBlackformMessageVo> consumer) {
		this.consumer = consumer;
	}
	
	@Autowired
	private DataRequestTaskService dataRequestTaskService;
    
    @Autowired
    private GraphBlackDao graphBlackDao;
    
    @Autowired
	private DataRequestTaskServiceWapper dataRequestTaskServiceWapper;


	@Override
	protected void handleMessage(GraphBlackformMessageVo message, String key, String topic) {
		LOG.info("receive graph black result msg = {}", JSONObject.toJSONString(message));
		try {
            long startTime = System.currentTimeMillis();
            JSONObject result = this.handleTrans(JSONObject.toJSONString(message));
            LOG.info("graph black time cost type=verify cost={} jobId={}", System.currentTimeMillis() - startTime, result==null?"":result.getString("job_id"));
        } catch (Exception e) {
            LOG.error("handle graph black feature result error,data={}", JSONObject.toJSONString(message), e);
        }
	}
	
	public JSONObject handleTrans(String data) {
        if(StringUtils.isEmpty(data)) {
            LOG.error("handleGraph data is null");
            return null;
        }
        // 接收关系网指定topic消息，解析消息，jobId查对应task记录，保存mongodb数据，B步拼dataVo获取
        JSONObject graphRet = JSONObject.parseObject(data);
        String jobId = graphRet.getString("job_id");
        if (StringUtils.isNotEmpty(jobId)) {
        	DataRequestTask dataTask = this.dataRequestTaskService.getDataRequestTaskByJobIdAndType(jobId, DataRequestTaskType.GRAPH_BLACK_RECORD.name());
			if (dataTask != null) {
				saveGraphResult(graphRet, dataTask);
				dataRequestTaskService.fetch(dataTask.getId());
				//check all task ready
		        this.dataRequestTaskServiceWapper.checkReportDataTask(dataTask.getSourceSystem(), dataTask.getUserKey());
			}
		}
        return graphRet;
    }

	private void saveGraphResult(JSONObject graphRet, DataRequestTask dataTask) {
		GraphBlackDataVo graphDataVo = new GraphBlackDataVo();
		graphDataVo.setUserKey(dataTask.getUserKey());
		graphDataVo.setSourceSystem(dataTask.getSourceSystem());
		graphDataVo.setLoanKey(dataTask.getLoanKey());
		graphDataVo.setJobId(dataTask.getJobID());
		graphDataVo.setData(graphRet.getString("data"));
		// 最多尝试三次，失败则不继续保存
		int i = 0;
		while (i < 3) {
			try {
				this.graphBlackDao.insert(graphDataVo);
				return;
			} catch (Exception e) {
				if (i == 2) {
					LOG.error("save graph black retMsg failed, params:{} ", JSONObject.toJSONString(graphDataVo), e);
				}
				i++;
			}
		}
	}

	@Override
	protected boolean handleByMultiThread() {
		return false;
	}

	public void start() {
		LOG.info("starting consumer");
		this.beginConsumer();
	}
}
