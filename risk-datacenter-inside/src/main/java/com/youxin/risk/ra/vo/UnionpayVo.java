/**
 * Copyright(c) 2011-2018 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.mongo.vo.AbstractRecordVo;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 创建时间：2018年1月29日-上午11:27:48
 */
public class UnionpayVo extends AbstractRecordVo {

	@Indexed(expireAfterSeconds=7776000)//90 days
	private Date createdTime;

	private UnionpayPersonalVo personal;

	public Date getCreatedTime() {
		return this.createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	public UnionpayPersonalVo getPersonal() {
		return this.personal;
	}

	public void setPersonal(UnionpayPersonalVo personal) {
		this.personal = personal;
	}


}
