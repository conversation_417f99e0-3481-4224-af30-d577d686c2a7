package com.youxin.risk.ra.service;

import com.youxin.risk.ra.model.ReportRequest;
import com.youxin.risk.ra.vo.ReportRequestVo;


public interface ReportRequestService {

    ReportRequest getReportRequest(Integer requestId);

     ReportRequestVo getReportRequestVo(Integer requestId);

	ReportRequest findReportRequestByUserKey(String sourceSystem, String userKey);

    ReportRequest findReportRequestByLoan(String sourceSystem,String userKey,Integer loanId);

    ReportRequest createReportResquest(ReportRequestVo reportResquestVo);

    ReportRequestVo getReportRequestVo(String sourceSystem, String userKey);

}
