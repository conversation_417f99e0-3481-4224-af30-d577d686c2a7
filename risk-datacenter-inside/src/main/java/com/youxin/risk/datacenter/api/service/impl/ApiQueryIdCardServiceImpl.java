package com.youxin.risk.datacenter.api.service.impl;

import com.youxin.risk.commons.dao.datacenter.RiskApiSubmitIdcardMapper;
import com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitIdcard;
import com.youxin.risk.datacenter.api.service.ApiQueryIdCardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("apiQueryIdCardServImpl")
public class ApiQueryIdCardServiceImpl implements ApiQueryIdCardService {

    private static final Logger LOG = LoggerFactory.getLogger(ApiQueryIdCardServiceImpl.class);

    @Autowired
    private RiskApiSubmitIdcardMapper riskApiSubmitIdcardMapper;

    @Override
    public RiskApiSubmitIdcard getByUserKey(String userKey, String apiLoanSource) {
        return riskApiSubmitIdcardMapper.getByUserKey(userKey, apiLoanSource);
    }

}
