package com.youxin.risk.datacenter.delayqueue.handler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.common.delayqueue.DelayMessageHandler;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.impl.SubmitAmountZhimaScoreServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class SubmitAmountZhiMaScoreDelayHandler implements DelayMessageHandler {


    private final static Logger logger = LoggerFactory.getLogger(SubmitAmountZhiMaScoreDelayHandler.class);

    @Autowired
    private SubmitAmountZhimaScoreServiceImpl submitAmountZhimaScoreService;

    @Override
    public Boolean handle(Object object) {

        try {
            JSONObject param = (JSONObject) object;
            //每次延迟重试次数减一，防止永不成功的错误造成死循环
            if (param.getInteger("retryTime") > 0) {
                param.put("retryTime", param.getInteger("retryTime") - 1);
                submitAmountZhimaScoreService.submitAndTrigger(param);
            }

        } catch (Exception e) {
            LoggerProxy.warn("delaySubmitAmountZhiMaScoreDelayError", logger, "param={}", JSON.toJSONString(object), e);
        }
        return true;
    }
}
