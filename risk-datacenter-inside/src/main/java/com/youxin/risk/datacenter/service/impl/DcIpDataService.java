package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.datacenter.mapper.DcIpDataMapper;
import com.youxin.risk.datacenter.model.DcIpData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2021/4/26 10:35
 * @Version 1.0
 */

@Service
public class DcIpDataService{

    private Logger logger = LoggerFactory.getLogger(DcIpDataService.class);

    @Autowired
    private DcIpDataMapper dcIpDataMapper;

    public DcIpData getIpArea(String ip){
        List<DcIpData> areaByIp = dcIpDataMapper.getAreaByIp(ipToLong(ip));
        DcIpData dcIpData =null;
        if(!Objects.isNull(areaByIp) && !areaByIp.isEmpty()){
            dcIpData=areaByIp.get(0);
        }
        if(dcIpData==null){
            //TODO 调用三方服务页面解析
        }
        return dcIpData;
    }

    private Long ipToLong(String ipAddress){
        String[] ipAddressInArray = ipAddress.split("\\.");
        long result = 0;
        for (int i = 0; i < ipAddressInArray.length; i++) {
            int power = 3 - i;
            int ip = Integer.parseInt(ipAddressInArray[i]);
            result += ip * Math.pow(256, power);
        }
        return result;
    }
}
