package com.youxin.risk.datacenter.service;

import com.youxin.risk.commons.model.datacenter.DcVerifySubmit;
import com.youxin.risk.commons.model.verify.VerifySubmit;

import java.util.List;

public interface VerifySubmitService extends BaseDcDbService<DcVerifySubmit>{
//    DcVerifySubmit queryTableDataByService(Long operationId);
List<DcVerifySubmit> getListByUserKey(String userKey, String apiLoanSource);

    VerifySubmit findSubmitByLoanKey(String loanKey);

    void saveOrUpdateVerifySubmit(VerifySubmit verifySubmit);

    VerifySubmit findSubmitByLoan(String loanKey,Integer loanId,String userKey);
}
