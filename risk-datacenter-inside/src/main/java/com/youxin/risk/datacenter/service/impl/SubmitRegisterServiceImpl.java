package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper;
import com.youxin.risk.commons.dao.datacenter.RiskApiSubmitRegisterMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitRegister;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.datacenter.service.SubmitRegisterService;
import com.youxin.risk.ra.vo.RaSubmitRegisterVo;
import com.youxin.risk.verify.service.VerifySubmitService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class SubmitRegisterServiceImpl extends AbstractBatchQueryService<DcSubmitRegister> implements SubmitRegisterService {

    Logger logger = LoggerFactory.getLogger(SubmitRegisterServiceImpl.class);

    @Autowired
    DcSubmitRegisterMapper dcSubmitRegisterMapper;

    @Autowired
    RiskApiSubmitRegisterMapper riskApiSubmitRegisterMapper;

    @Resource
    private HfqUserInfoService hfqUserInfoService;

    @Autowired
    @Qualifier("verifySubmitServImpl")
    private VerifySubmitService verifySubmitService;


    @Override
    public DcSubmitRegister getItem(DcSubmitRegister item) {
        throw new NotImplementedException("SubmitRegisterServiceImpl.getItem");
    }

    @Override
    public DcSubmitRegister getItemByPrimaryKey(long id) {
        return dcSubmitRegisterMapper.selectByPrimaryKey(id);
    }

    @Override
    public Long insertItem(DcSubmitRegister item) {
        dcSubmitRegisterMapper.insert(item);
        return item.getId();
    }


    @Override
    public DcSubmitRegister getByOperationId(Long operationId) {
        return dcSubmitRegisterMapper.getByOperationId(operationId);
    }

    @Override
    public DcSubmitRegister getByUserKey(String userKey, String apiLoanSource) {
        if (StringUtils.isNotBlank(apiLoanSource)) {
            return getDcSubmitRegisterFromApi(userKey, apiLoanSource);
        }
        DcSubmitRegister result = dcSubmitRegisterMapper.getByUserKey(userKey);
        if (Objects.nonNull(result)) {
            return result;
        }
        return getDcSubmitRegisterFromApi(userKey, apiLoanSource);
    }

    private DcSubmitRegister getDcSubmitRegisterFromApi(String userKey, String apiLoanSource) {
        RaSubmitRegisterVo vo = getVoFromApi(userKey, apiLoanSource);
        if (Objects.nonNull(vo)) {
            try {
                return ObjectTransferUtils.transferObject(vo, DcSubmitRegister.class);
            } catch (Exception e) {
                logger.error("register api transfer error", e);
            }
        }
        return null;
    }

    @Override
    public List<DcSubmitRegister> getListByUserKey(String userKey, String apiLoanSource) {
        if (StringUtils.isNotBlank(apiLoanSource)) {
            return getDcSubmitRegistersFromApi(userKey, apiLoanSource);
        }
        List<DcSubmitRegister> listByUserKey = dcSubmitRegisterMapper.getListByUserKey(userKey);
        if (CollectionUtils.isNotEmpty(listByUserKey)) {
            return listByUserKey;
        }
        return getDcSubmitRegistersFromApi(userKey, apiLoanSource);
    }

    private List<DcSubmitRegister> getDcSubmitRegistersFromApi(String userKey, String apiLoanSource) {
        List<DcSubmitRegister> result = Lists.newArrayList();
        List<RiskApiSubmitRegister> apiList = riskApiSubmitRegisterMapper.getListByUserKey(userKey, apiLoanSource);
        if (CollectionUtils.isNotEmpty(apiList)) {
            apiList.forEach(apiInfo -> {
                DcSubmitRegister dcSubmitRegister = new DcSubmitRegister();
                dcSubmitRegister.setCreateTime(apiInfo.getCreateTime());
                dcSubmitRegister.setMobile(apiInfo.getMobile());
                dcSubmitRegister.setUserKey(userKey);
                result.add(dcSubmitRegister);
            });
            return result;
        }
        logger.info("registerList from api empty, userKey: {}, api: {}", userKey, apiLoanSource);
        return result;
    }


    @Override
    public RaSubmitRegisterVo getSubmitRegisterVoByUserKey(String userKey, String apiLoanSource) {
        if (StringUtils.isNotBlank(apiLoanSource)) {
            return getVoFromApi(userKey, apiLoanSource);
        }

        RaSubmitRegisterVo raSubmitRegisterVo = null;
        DcSubmitRegister submitRegister = dcSubmitRegisterMapper.getByUserKey(userKey);

        if (Objects.isNull(submitRegister)) {
            // 兼容某些事件没有apiLoanSource参数
            RaSubmitRegisterVo voFromApi = getVoFromApi(userKey, apiLoanSource);
            if (Objects.nonNull(voFromApi)) {
                return voFromApi;
            }
            try {
                //调用好分期接口获取用户信息
                JSON userInfo = hfqUserInfoService.getUserInfo(userKey);

                raSubmitRegisterVo = JSON.toJavaObject(userInfo, RaSubmitRegisterVo.class);

                LoggerProxy.info("return lost rePoll dcSubmitRegister", logger, "userKey={}", userKey);

            } catch (Exception e) {
                LoggerProxy.error("lost dcRegister insert error", logger, "userKey={}", userKey, e);
            }
        }

        if (submitRegister != null) {
            try {
                raSubmitRegisterVo = ObjectTransferUtils.transferObject(submitRegister, RaSubmitRegisterVo.class);
            } catch (Exception e) {
                this.logger.error("transfer model to vo error");
            }
        }
        return raSubmitRegisterVo;
    }

    private RaSubmitRegisterVo getVoFromApi(String userKey, String apiLoanSource) {
        RaSubmitRegisterVo raSubmitRegisterVo = null;
        RiskApiSubmitRegister apiInfo = riskApiSubmitRegisterMapper.getByUserKey(userKey, apiLoanSource);
        if (Objects.nonNull(apiInfo)) {
            raSubmitRegisterVo = new RaSubmitRegisterVo();
            raSubmitRegisterVo.setCreateTime(apiInfo.getCreateTime());
            raSubmitRegisterVo.setMobile(apiInfo.getMobile());
            raSubmitRegisterVo.setUserKey(userKey);
            return raSubmitRegisterVo;
        }
        logger.info("register from api empty userKey: {}, api: {}", userKey, apiLoanSource);
        return null;
    }
}
