package com.youxin.risk.datacenter.service;

import com.youxin.risk.commons.model.datacenter.DcSubmitJob;
import com.youxin.risk.ra.vo.RaSubmitJobVo;

/**
 * <AUTHOR>
 */
public interface SubmitJobService extends BaseDcDbService<DcSubmitJob>{

    /**
     * 通过userKey 查询用户工作信息
     *
     * @param userKey       用户标识
     * @param apiLoanSource
     * @return 工作信息
     */
    DcSubmitJob getByUserKey(String userKey, String apiLoanSource);

    /**
     * 通过userKey 和 sourceSystem 查询用户工作信息
     *
     * @param sourceSystem 业务线标识
     * @param userKey      用户唯一标识
     * @return 工作信息
     */
    RaSubmitJobVo getRaSubmitJobVoByUserKeyAndSourceSystem(String sourceSystem, String userKey, String apiLoanSource);
}
