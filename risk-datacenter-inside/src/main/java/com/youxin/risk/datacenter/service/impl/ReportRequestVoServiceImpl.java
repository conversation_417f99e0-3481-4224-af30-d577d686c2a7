package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.datacenter.service.search.DataQueryService;
import com.youxin.risk.ra.service.ReportRequestService;
import com.youxin.risk.ra.vo.ReportRequestVo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * <p>
 * 获取 提交申请信息
 */
@Service
public class ReportRequestVoServiceImpl implements DataQueryService<ReportRequestVo> {

    protected Logger LOGGER = LoggerFactory.getLogger(ReportRequestVoServiceImpl.class);

    @Resource
    private DcReportRequestServiceImpl dcReportRequestService;

    @Resource
    private ReportRequestService reportRequestService;


    @Override
    public String getServiceCode() {
        return "reportRequestVo";
    }

    /**
     * 从  mysql 表查询自有数据
     *
     * @param param
     * @return
     */
    @Override
    public ReportRequestVo queryDcData(JSONObject param) {

        String userKey = param.getString("userKey");
        String sourceSystem = param.getString("sourceSystem");
        String apiLoanSource = (String) param.get("apiLoanSource");

        ReportRequestVo reportRequestVo = this.dcReportRequestService.getReportRequestVo(userKey, apiLoanSource);

        if (StringUtils.isBlank(apiLoanSource) && reportRequestVo == null) {
            LOGGER.info("queryDcData从ra库获取信息, userKey: {}", userKey);
            reportRequestVo = this.reportRequestService.getReportRequestVo(sourceSystem, userKey);
        }

        return reportRequestVo;

    }
}
