package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitHouseMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitHouse;
import com.youxin.risk.datacenter.service.SubmitHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Desc 获取房产信息
 * <AUTHOR>
 * @date 2022-10-17 22:00
 */
@Service
public class SubmitHouseServiceImpl extends AbstractBatchQueryService<DcSubmitHouse> implements SubmitHouseService {

    @Autowired
    private DcSubmitHouseMapper dcSubmitHouseMapper;

    @Override
    public DcSubmitHouse getLastByUserKey(String userKey) {
        return dcSubmitHouseMapper.getByUserKey(userKey);
    }
}
