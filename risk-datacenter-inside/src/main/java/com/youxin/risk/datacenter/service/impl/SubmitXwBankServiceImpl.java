package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitXwMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitXw;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.datacenter.service.SubmitXwBankService;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class SubmitXwBankServiceImpl extends AbstractBatchQueryService<DcSubmitXw> implements SubmitXwBankService {
    @Autowired
    DcSubmitXwMapper dcSubmitXwMapper;


    @Override
    public DcSubmitXw getItem(DcSubmitXw item) {
        return dcSubmitXwMapper.selectByItem(item);
    }

    @Override
    public DcSubmitXw getItemByPrimaryKey(long id) {
        return dcSubmitXwMapper.selectByPrimaryKey(id);
    }

    @Override
    public Long insertItem(DcSubmitXw item) {
        dcSubmitXwMapper.insert(item);
        return item.getId();
    }

    @Override
    public DcSubmitXw getByOperationId(Long operationId) {
        throw new NotImplementedException("SubmitXwBankServiceImpl.getByOperationId");
    }

    @Override
    public DcSubmitXw queryTableDataByService(DcRequestService dcRequestService) throws Exception{
        Map<String, Object> params = dcRequestService.getParams();
        DcSubmitXw example = new DcSubmitXw();
        String userKey = (String) params.get("userKey");
        String sourceSystem = (String) params.get("sourceSystem");
        if(userKey == null || sourceSystem == null){
            return null;
        }
        Integer loanId = (Integer) params.get("loanId");
        example.setUserKey(userKey);
        example.setSourceSystem(sourceSystem);
        example.setLoanId(loanId);
        DcSubmitXw item = this.getItem(example);
        return item;
    }

    @Override
    public DcSubmitXw getByUserKey(String userKey) {
        return dcSubmitXwMapper.getByUserKey(userKey);
    }

}
