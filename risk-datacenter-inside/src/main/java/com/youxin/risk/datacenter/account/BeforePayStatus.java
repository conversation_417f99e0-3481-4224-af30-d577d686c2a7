package com.youxin.risk.datacenter.account;

public enum BeforePayStatus {
                             COMMON(0, "正常还款", RepayTransType.COMMON_REPAY),
                             BEFORE(1, "提前还款", RepayTransType.IN_REPAY),
                             REFUND(2, "退款", RepayTransType.HELP_IN_REPAY),
                             HELP_COMMON(3, "代付正常还款", RepayTransType.HELP_COMMON_REPAY),
                             HELP_IN_REPAY(4, "代付提前还款", RepayTransType.HELP_IN_REPAY);
    private int status;
    private String desc;
    private RepayTransType accountRepayTransType;

    private BeforePayStatus(int status, String desc, RepayTransType accountRepayTransType) {
        this.status = status;
        this.desc = desc;
        this.accountRepayTransType = accountRepayTransType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    
    public RepayTransType getAccountRepayTransType() {
        return accountRepayTransType;
    }

    public void setAccountRepayTransType(RepayTransType accountRepayTransType) {
        this.accountRepayTransType = accountRepayTransType;
    }

    public static BeforePayStatus getEnum(String val) {
        for (BeforePayStatus status : BeforePayStatus.values()) {
            if (String.valueOf(status.getStatus()).equals(val)) {
                return status;
            }
        }
        return null;
    }

    public static String getMessage(int val) {
        for (BeforePayStatus status : BeforePayStatus.values()) {
            if (status.getStatus()==val) {
                return status.getDesc();
            }
        }
        return null;
    }
   
}
