package com.youxin.risk.datacenter.service.search;

import com.google.common.base.Preconditions;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountJobMapper;
import com.youxin.risk.datacenter.model.DcSubmitAmountJob;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
public class AmountJobQueryServiceImpl implements DcQueryService<DcSubmitAmountJob> {


    private static final Logger logger = LoggerFactory.getLogger(AmountJobQueryServiceImpl.class);


    @Autowired
    DcSubmitAmountJobMapper dcSubmitAmountJobMapper;


    @DcServiceCode(name = {"AMOUNT_JOB"})
    public DcSubmitAmountJob getByUserKey(Map<String, Object> params) {

        String userKey = (String) params.get("userKey");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));

        DcSubmitAmountJob dcSubmitAmountEducation = dcSubmitAmountJobMapper.getByUserKey(userKey);


        return dcSubmitAmountEducation;
    }

}