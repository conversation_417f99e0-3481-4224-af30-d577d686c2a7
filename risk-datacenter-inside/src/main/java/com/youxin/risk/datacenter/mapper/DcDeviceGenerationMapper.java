package com.youxin.risk.datacenter.mapper;


import com.youxin.risk.datacenter.model.DcDeviceGeneration;

/**
 * <AUTHOR>
 */
public interface DcDeviceGenerationMapper {

    /**
     * 插入数据
     * @param dcDeviceGeneration dcDeviceGeneration
     */
    void insert(DcDeviceGeneration dcDeviceGeneration);


    /**
     * 更新数据
     * @param dcDeviceGeneration dcDeviceGeneration
     */
    void updateByIdentifier(DcDeviceGeneration dcDeviceGeneration);


    /**
     * 通过identifier查询dcDeviceGeneration
     * @param identifier identifier
     * @return dcDeviceGeneration
     */
    DcDeviceGeneration getByIdentifier(String identifier);

}
