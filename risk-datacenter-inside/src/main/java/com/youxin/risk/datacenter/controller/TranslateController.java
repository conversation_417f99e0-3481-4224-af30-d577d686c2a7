package com.youxin.risk.datacenter.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.HttpResult;
import com.youxin.risk.commons.utils.HttpUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.model.DcIpData;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.datacenter.service.TranslateService;
import com.youxin.risk.datacenter.service.impl.DcIpDataService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static com.youxin.risk.datacenter.pojo.JsonResultVo.ERROR;
import static com.youxin.risk.datacenter.utils.CommonUtil.isMatches;

/**
 * @description: 用于信息的转换
 * @author: juxiang
 * @create: 2021-06-23 14:25
 **/
@RestController
@RequestMapping(value = "/trans")
public class TranslateController {

    private Logger logger = LoggerFactory.getLogger(getClass());
    // gps转换标识
    private static final String GPS="GPS";
    // IP 转换标识
    private static final String IP="IP";

    @Autowired
    QueryController queryController;

    @Autowired
    private DcIpDataService dcIpDataService;

    @Autowired
    private TranslateService translateService;
    /**
     *  根据gps经纬度获取城市的详细信息
     * @param param
     * @return
     */
    @RequestMapping(value ="/gpsOrIp", method = RequestMethod.POST)
    public JsonResultVo transGpsOrIp(@RequestBody JSONObject param){
        String type=param.getString("type");
        if(StringUtils.equals(type,GPS)){
            return translateService.transGps(param);
        }
        if(StringUtils.equals(type,IP)){
            return this.transIp(param);
        }
        logger.error("unknown type：{}",param.getString("type"));
        return JsonResultVo.error(ERROR,String.format("unknown type：%s",type ));
    }

    /**
     * 根据ip获取所在城市
     * @param param
     * @return
     */
    private JsonResultVo transIp(JSONObject param){
        try {
            LoggerProxy.info("queryIP", logger, "query Ip ,param={}", param.toJSONString());
            String cellIp=param.getString("cellIp");
            String wifiIp=param.getString("wifiIp");
            long startTime = System.currentTimeMillis();
            DcIpData cellIpData =null;
            DcIpData wifiIpData = null;
            if(isMatches(cellIp)){
                cellIpData = dcIpDataService.getIpArea(cellIp);
            }
            if(isMatches(wifiIp)){
                wifiIpData = dcIpDataService.getIpArea(wifiIp);
            }
            Map<String,Object> resultMap=new HashMap<>();

            if(!Objects.isNull(cellIpData)){
                resultMap.put("cellProvince", cellIpData.getRegion());
                resultMap.put("cellCity", cellIpData.getCity());
            }
            if(!Objects.isNull(wifiIpData)){
                resultMap.put("wifiProvince", wifiIpData.getRegion());
                resultMap.put("wifiCity", wifiIpData.getCity());
            }
            JsonResultVo resultVo = JsonResultVo.success();
            resultVo.setData(resultMap);
            LoggerProxy.info("queryIpDatas", logger, "query city by ip cost={},param={}", System.currentTimeMillis() - startTime, param.toJSONString());
            return resultVo;
        } catch (Exception e) {
            LoggerProxy.error("queryCityByIpError", logger, "param={}", param.toJSONString(), e);
            return JsonResultVo.error();
        }
    }
}
