package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.datacenter.dto.LoanRequestDTO;
import com.youxin.risk.datacenter.service.LoanService;
import com.youxin.risk.datacenter.service.NifaService;
import com.youxin.risk.ra.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年10月18日 下午5:34
 */
@Slf4j
@Service
public class NifaServiceImpl implements NifaService {
    private static final String RESPONSE_STATUS_SUCCESS = "000000";

    @Autowired
    private LoanService loanService;

    @Override
    public Map<String, Object> buildParams(JSONObject params) {
        String userKey = params.getString("userKey");
        String loanKey = params.getString("loanKey");
        String eventCode = params.getString("eventCode");
        Stopwatch stop = Stopwatch.createStarted();
        log.info("NifaService buildParams start, userKey:{}, loanKey:{}", userKey, loanKey);

        params.put("reason", "a");

        List<String> eventCodes = ApolloClientAdapter.getListConfig(ApolloNamespaceEnum.DC_INSIDE_SPACE,
                "nifaservice.huijin.reason.events", String.class);

        if (eventCodes.contains(eventCode)) {
            buildReason(params);
        }

        if ("dataPreCache".equals(eventCode)
                || "dataPreCacheNew".equals(eventCode)
                || "hhLoanSupermarket".equals(eventCode)) {
            params.put("reason", "b");
        }

        return params;
    }

    /**
     * reason入参表达式  请求的hhPreHujinService
     * 特征 loan_history_feature
     * @param params
     */
    private void buildReason(JSONObject params) {
        String userKey = params.getString("userKey");
        String sourceSystem = params.getString("sourceSystem");

        LoanRequestDTO dto = new LoanRequestDTO();
        dto.setPartnerUserId(userKey);
        dto.setPartner(sourceSystem);
        String result = loanService.getLoanHistoryInfo(dto);

        if (StringUtils.isEmpty(result)) {
            return;
        }

        JSONObject resultJson = JSON.parseObject(result);

        if (!RESPONSE_STATUS_SUCCESS.equals(resultJson.getString("status"))
                && !"040001".equals(resultJson.getString("status"))) {
            return;
        }

        JSONObject data = resultJson.getJSONObject("data");

        if (data == null || CollectionUtils.isEmpty(data.getJSONArray("loans"))) {
            return;
        }

        JSONArray loans = data.getJSONArray("loans");

        boolean flag = false;

        for (int i = 0; i< loans.size(); i++) {
            JSONObject loan = loans.getJSONObject(i);
            flag = Objects.equals(loan.getString("originFunder"), "WE")
                    && !Objects.equals(loan.getString("loanStatus"), "PAYOFF");
            if (flag) {
                break;
            }
        }

        if (flag) {
            params.put("reason", "b");
        }
    }


}
