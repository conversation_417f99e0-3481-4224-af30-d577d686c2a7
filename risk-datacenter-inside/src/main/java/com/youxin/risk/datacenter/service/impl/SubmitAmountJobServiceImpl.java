package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountJobMapper;
import com.youxin.risk.datacenter.model.DcSubmitAmountJob;
import com.youxin.risk.datacenter.service.AbstractSubmitAmountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SubmitAmountJobServiceImpl extends AbstractSubmitAmountService {


    private static final Logger logger = LoggerFactory.getLogger(SubmitAmountJobServiceImpl.class);

    @Autowired
    DcSubmitAmountJobMapper dcSubmitAmountJobMapper;

    /**
     * 获取需要校验的数据参数
     */
    @Override
    protected List<String> getCheckNullKeyList() {

        List<String> keyList = new ArrayList<>();
        //从事行业
        keyList.add("industry");
        //工作岗位
        keyList.add("companyPosition");
        //月收入
        keyList.add("salary");
        //工作年限
        keyList.add("workYear");
        //公司名称
        keyList.add("companyName");
        //公司地址
        keyList.add("companyAddress");
        //职务等级
        keyList.add("level");

        return keyList;
    }

    @Override
    protected List<String> checkNullCustomizeParameters(JSONObject param) {
        return new ArrayList<>();
    }

    @Override
    protected void setSubmitType(JSONObject param) {

        param.put("operationType",OperationType.AMOUNT_JOB.name());
    }

    @Override
    protected void insertAmountParam(JSONObject param) {

        DcSubmitAmountJob dcSubmitAmountJob = JSON.toJavaObject(param, DcSubmitAmountJob.class);
        dcSubmitAmountJobMapper.insert(dcSubmitAmountJob);
    }

}
