package com.youxin.risk.datacenter.service.search;

import com.google.common.base.Preconditions;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.model.datacenter.DcSubmitBankCard;
import com.youxin.risk.datacenter.service.SubmitBankCardService;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import com.youxin.risk.verify.model.VerifySubmitBankCard;
import com.youxin.risk.verify.service.VerifySubmitBankCardService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class BankCardQueryService implements DcQueryService<DcSubmitBankCard> {

    private static final Logger logger = LoggerFactory.getLogger(BankCardQueryService.class);

    @Resource
    private SubmitBankCardService submitBankCardService;

    @Autowired
    private VerifySubmitBankCardService verifySubmitBankCardService;

    @DcServiceCode(name = "BANK_CARD")
    public DcSubmitBankCard getByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        String apiLoanSource = (String) params.get("apiLoanSource");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        DcSubmitBankCard dcSubmitBankCard = submitBankCardService.getByUserKey(userKey, apiLoanSource);

        try {
            String querySwitch = NacosClient.getByNameSpace(ApolloNamespace.DATACENTER_NAMESPACE, "bankCard.query.switch", "0");
            if ("0".equals(querySwitch)) {
                return dcSubmitBankCard;
            }
            // 兼容dc获取不到数据的情况
            if (dcSubmitBankCard == null) {
                // 获取verify的数据
                VerifySubmitBankCard verifySubmitBankCard = verifySubmitBankCardService.getBankCardInfoByUserKey(userKey);
                if (verifySubmitBankCard != null) {
                    dcSubmitBankCard = new DcSubmitBankCard();
                    dcSubmitBankCard.setBankCardNo(verifySubmitBankCard.getBankcardNo());
                    String operationLogId = verifySubmitBankCard.getOperationLogId() == null ? "0" : verifySubmitBankCard.getOperationLogId().toString();
                    dcSubmitBankCard.setOperationLogId(Long.parseLong(operationLogId));
                    dcSubmitBankCard.setCreateTime(verifySubmitBankCard.getCreateTime());
                    dcSubmitBankCard.setBankName(verifySubmitBankCard.getBankName());
                    dcSubmitBankCard.setUpdateTime(verifySubmitBankCard.getUpdateTime());
                    String id = verifySubmitBankCard.getId() == null ? "0" : verifySubmitBankCard.getId().toString();
                    dcSubmitBankCard.setId(Long.parseLong(id));
                    dcSubmitBankCard.setUserKey(verifySubmitBankCard.getUserKey());
                    dcSubmitBankCard.setValidation(verifySubmitBankCard.getValidation());
                }
            }
        } catch (Exception ex) {
            logger.error("BankCardQueryService getByUserKey error,userKey: {}", userKey);
        }
        return dcSubmitBankCard;
    }

    @DcServiceCode(name = "BANK_CARD_LIST")
    public List<DcSubmitBankCard> getListByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        String apiLoanSource = (String) params.get("apiLoanSource");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        return submitBankCardService.getListByUserKey(userKey, apiLoanSource);
    }
}