DROP TABLE IF EXISTS `risk_datacenter`.`dc_black_white_verify`;
CREATE TABLE `risk_datacenter`.`dc_black_white_verify` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `business_type` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '业务类型 PBOC:征信，STRATEGY:策略',
    `list_type` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名单类型 BLACK:黑名单，WHITE:白名单',
    `dimension` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '维度 MOBILE:手机号，ID_CARD:身份证号，USER_KEY:用户userKey',
    `dimension_value` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '维度值',
    `event_code` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '事件',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建人',
    `modify_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`),
    INDEX `idx_business_type`(`business_type`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COMMENT = '黑白名单验单表';