# 策略审核白名单-userkey粒度
insert into `risk_datacenter`.`dc_black_white_verify`(business_type, list_type, dimension, dimension_value, creator, modifier) values ('STRATEGY', 'WHITE', 'USER_KEY', '98b260d8ec0b3cda90866bcbd62f85ab', 'migrate', 'migrate');

# 查征白名单-userkey粒度
insert into `risk_datacenter`.`dc_black_white_verify`(business_type, list_type, dimension, dimension_value, creator, modifier) values ('PBOC', 'WHITE', 'USER_KEY', '98b260d8ec0b3cda90866bcbd62f85ab', 'migrate', 'migrate');