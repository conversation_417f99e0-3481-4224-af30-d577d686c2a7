package com.youxin.risk.fs.test;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;

import com.youxin.risk.commons.utils.JsonUtils;
import org.junit.Test;
import org.omg.Messaging.SYNC_WITH_TRANSPORT;


import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/12/28 14:52
 */
public class FsTest extends BaseTest {

    @Resource(name = "featureMessageSender")
    private KafkaSyncSender featureMessageSender;
    @Test
    public void testProperties() {
       // featureMessageSender.send("test");
        String msg = "";


        JSONObject jsonObject = JSON.parseObject(msg);
        String s = JsonUtils.toJson(jsonObject);
        featureMessageSender.send(s);
    }



}
