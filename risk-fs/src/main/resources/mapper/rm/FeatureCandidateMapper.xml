<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.fs.dao.rm.FeatureCandidateMapper">
	<!-- Result Map-->
	<resultMap id="resultMap"
		type="com.youxin.risk.fs.model.FeatureCandidate">
		<id column="id" property="id"/>
		<result column="source_system" property="sourceSystem"/>
		<result column="feature_name" property="featureName"/>
		<result column="feature_ver" property="featureVer"/>
		<result column="node_path" property="nodePath"/>
		<result column="apply_step" property="applyStep"/>
		<result column="developer" property="developer"/>
		<result column="group_name" property="groupName"/>
		<result column="group_ver" property="groupVer"/>
		<result column="call_back_id" property="callBackId" />
		<result column="feature_code_id" property="featureCodeId"/>
		<result column="run_count" property="runCount"/>
		<result column="is_finished" property="isFinished"/>
		<result column="run_status" property="runStatus"/>
		<result column="error_msg" property="errorMsg"/>
		<result column="is_notified" property="isNotified"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="del_flag" property="delFlag"/>
		<result column="version" property="version"/>
	</resultMap>
	       
	<!-- rm_feature_candidate table all fields -->
	<sql id="columnList">
		id,source_system,feature_name,feature_ver,node_path,apply_step,developer,group_name,group_ver,call_back_id,feature_code_id,run_count,is_finished,run_status,error_msg,is_notified,create_time,update_time,del_flag,version
	</sql>
	   
	<!-- 查询条件 -->
	<sql id="whereConditions">

		<where>
			<if test="id != null">
				and id = #{id}
			</if>
			<if test="sourceSystem != null">
				and source_system = #{sourceSystem}
			</if>
			<if test="featureName != null">
				and feature_name = #{featureName}
			</if>
			<if test="featureVer != null">
				and feature_ver = #{featureVer}
			</if>
			<if test="nodePath != null">
				and node_path = #{nodePath}
			</if>
			<if test="applyStep != null">
				and apply_step = #{applyStep}
			</if>
			<if test="developer != null">
				and developer = #{developer}
			</if>
			<if test="groupName != null">
				and group_name = #{groupName}
			</if>
			<if test="groupVer != null">
				and group_ver = #{groupVer}
			</if>
			<if test="callBackId != null">
				and call_back_id = #{callBackId}
			</if>
			<if test="featureCodeId != null">
				and feature_code_id = #{featureCodeId}
			</if>
			<if test="runCount != null">
				and run_count = #{runCount}
			</if>
			<if test="isFinished != null">
				and is_finished = #{isFinished}
			</if>
			<if test="runStatus != null">
				and run_status = #{runStatus}
			</if>
			<if test="errorMsg != null">
				and error_msg = #{errorMsg}
			</if>
			<if test="isNotified != null">
				and is_notified = #{isNotified}
			</if>
			<if test="createTime != null">
				and create_time = #{createTime}
			</if>
			<if test="updateTime != null">
				and update_time = #{updateTime}
			</if>
			<if test="delFlag != null">
				and del_flag = #{delFlag}
			</if>
			<if test="version != null">
				and version = #{version}
			</if>
		</where>
	</sql>
	   
	
	<!-- 插入记录 -->
	<insert id="insert"
		parameterType="com.youxin.risk.fs.model.FeatureCandidate"
		useGeneratedKeys="true" keyColumn="id" keyProperty="id">
		insert into
			rm_feature_candidate(source_system,feature_name,feature_ver,node_path,apply_step,developer,group_name,group_ver,feature_code_id,call_back_id)
		values(#{sourceSystem},#{featureName},#{featureVer},#{nodePath},#{applyStep},#{developer},#{groupName},#{groupVer},#{featureCodeId},#{callBackId})
	</insert>
	
	<!-- 根据id，修改记录-->
	<update id="update"
		parameterType="com.youxin.risk.fs.model.FeatureCandidate">
		update
		rm_feature_candidate set
		source_system=#{sourceSystem},feature_name=#{featureName},feature_ver=#{featureVer},node_path=#{nodePath},apply_step=#{applyStep},developer=#{developer},group_name=#{groupName},group_ver=#{groupVer},call_back_id=#{callBackId},feature_code_id=#{featureCodeId},run_count=#{runCount},is_finished=#{isFinished},run_status=#{runStatus},error_msg=#{errorMsg},is_notified=#{isNotified},create_time=#{createTime},update_time=#{updateTime},del_flag=#{delFlag},version=#{version}
		where id=#{id}
	</update>
	
	<update id="updateStatus"
		parameterType="com.youxin.risk.fs.model.FeatureCandidate">
		update rm_feature_candidate
		 set run_count=#{runCount},is_finished=#{isFinished},run_status=#{runStatus},error_msg=#{errorMsg},is_notified=#{isNotified},update_time=now(),version=#{version}+1
		where id=#{id} AND version=#{version}
	</update>
	 
	<!-- 删除-->
	<delete id="deleteById" parameterType="long">
		update rm_feature_candidate 
		 set del_flag=1,version=#{version}+1 
		where id = #{id}
	</delete>
	
	
	<delete id="deleteByFeatureName">
		update rm_feature_candidate 
		 set del_flag=1,version=version+1 
		where  source_system=#{sourceSystem} AND feature_name=#{featureName}
	</delete>
	 
	<!-- 根据ID查询 -->
	<select id="selectById" resultMap="resultMap"
		parameterType="int">
		select
		<include refid="columnList"/>
		from rm_feature_candidate where id = #{id}
	</select>
	  	
	<!-- 总数-->
	<select id="selectCountByConditions"
		resultType="java.lang.Integer"
		parameterType="com.youxin.risk.fs.model.FeatureCandidate">
		select count(1) from rm_feature_candidate
		<include refid="whereConditions"/>
	</select>

	<!-- 同组下未测试完成候选feature总数-->
	<select id="countUnFinished" resultType="java.lang.Integer">
		select count(1) from rm_feature_candidate where call_back_id = #{callBackId} and is_finished = 0 and run_status = 0
	</select>
	
	<!-- 根据callBackId查询 -->
	<select id="getFeatureByCallbackId" resultMap="resultMap">
		select
		<include refid="columnList"/>
		from rm_feature_candidate where call_back_id = #{callBackId}
	</select>
	  	
	<!-- 条件查询 -->
	<select id="selectByConditions" resultMap="resultMap"
		parameterType="com.youxin.risk.fs.model.FeatureCandidate">
		select
		<include refid="columnList"/>
		from rm_feature_candidate
		<include refid="whereConditions"/>
	</select>
	
	
	<!-- map入参查询总数-->
	<select id="selectCountByMap" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		select count(1) from rm_feature_candidate
		<include refid="whereConditions"/>
	</select>
	  	
	<!-- map入参查询 -->
	<select id="selectByMap" resultMap="resultMap"
		parameterType="java.util.Map">
		select
		<include refid="columnList"/>
		from rm_feature_candidate
		<include refid="whereConditions"/>
	</select>

	<!-- 获取特征 -->
	<select id="getFeatureByStep" resultMap="resultMap">
		SELECT * FROM rm_feature_candidate  T
		WHERE source_system = #{sourceSystem} AND apply_step = #{step}  AND del_flag=0 AND is_finished=0 AND run_status=0
		AND NOT EXISTS (
		SELECT 1 FROM rm_feature_candidate WHERE source_system = T.source_system AND
		apply_step = T.apply_step AND feature_name = T.feature_name AND id > T.id
		)
	</select>

</mapper>   
