package com.youxin.risk.fs.hbase;

import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public interface HbaseTable extends Serializable{

    String getTableName();
    
    Put buildPut();
    
    void readValue(Result value);

    HbaseTable create();

    void readSimpleValue(Result value);
    
    Get buildGet(String rowkey);

    Scan buildScan(String rowkeyPrefix);
}
