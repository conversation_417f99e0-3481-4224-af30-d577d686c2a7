package com.youxin.risk.fs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.CompareDiffTypeEnums;
import com.youxin.risk.commons.constants.CompareTypeEnum;
import com.youxin.risk.commons.constants.ErrorReasonEnum;
import com.youxin.risk.commons.dao.admin.AdminRuleScoreCandidateMapper;
import com.youxin.risk.commons.dao.admin.AdminRuleScoreVarMapper;
import com.youxin.risk.commons.dao.admin.NodeConfigMapper;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.AdminRuleScoreCandidate;
import com.youxin.risk.commons.model.AdminRuleScoreNodeMirror;
import com.youxin.risk.commons.model.AdminRuleScoreVar;
import com.youxin.risk.commons.model.DatasourceConfigDO;
import com.youxin.risk.commons.mongo.RuleScoreMirrorVarVoDao;
import com.youxin.risk.commons.mongo.RuleScoreResultMongoDao;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JsonDiffComparatorNew;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.ExperimentCompareResultVo;
import com.youxin.risk.commons.vo.RuleScoreMirrorResultVo;
import com.youxin.risk.commons.vo.RuleScoreMirrorVarVo;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.fs.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 规则分实验
 */
@Service
@Slf4j
public class RuleScoreExpServiceImpl {
    private static final Logger LOGGER = LoggerFactory.getLogger(RuleScoreExpServiceImpl.class);

    private static final String RULE_EXPERIMENT_RESULT_STATUS_FAILED = "失败";
    private static final String RULE_EXPERIMENT_RESULT_STATUS_NO_DIFF = "无差异";
    private static final String RULE_EXPERIMENT_RESULT_STATUS_DIFF = "有差异";

    @Value("${system.pythonframe.baseurlBatchPython3}")
    private String baseBatchURLPython3;
    @Value("${variable.center.service.url:http://risk-variable-gateway.weicai.com.cn}")
    private String variableCenterServiceUrl;
    @Autowired
    private AdminRuleScoreCandidateMapper adminRuleScoreCandidateMapper;
    @Autowired
    private RuleScoreResultMongoDao ruleScoreResultMongoDao;
    @Autowired
    private AdminRuleScoreVarMapper adminRuleScoreVarMapper;
    @Resource
    private NodeConfigMapper nodeConfigMapper;
    @Resource
    private RuleScoreMirrorVarVoDao ruleScoreMirrorVarVoDao;
    @Resource(name = "ruleScoreMirrorResultSender")
    private KafkaSyncSender ruleScoreMirrorResultSender;
    @Autowired
    private EventService eventService;


    public void runRuleScoreExperiments(StrategyVo strategyVo){
        /** 首先通过步骤加载规则分实验,加载缓存的时候进行了拍平 **/
        String step = strategyVo.getStep();
        List<AdminRuleScoreNodeMirror> ruleScoreNodeMirrors = CacheApi.selectRuleScoreExperiments(step);
        if (CollectionUtils.isEmpty(ruleScoreNodeMirrors)){
            LoggerProxy.info(LOGGER, "there is no ruleScore_experiment for the step, step={}",strategyVo.getStep());
            return;
        }
        /** 处理所有的镜像 **/
        for (AdminRuleScoreNodeMirror adminRuleScoreNodeMirror : ruleScoreNodeMirrors){
            /** 根据分流比例 **/
            Integer ruleMirrorRate = Optional.ofNullable(adminRuleScoreNodeMirror.getRuleMirrorRate()).orElse(0);
            if (ruleMirrorRate < 100) {
                if (RandomUtils.nextInt(0, 101) > ruleMirrorRate) {
                    LoggerProxy.info(LOGGER, "ruleScore_experiment not hit experiment flow expCode = {}", adminRuleScoreNodeMirror.getRuleMirrorCode());
                    continue;
                }
            }
            /** 时间条件 **/
            Date now = new Date();
            if (adminRuleScoreNodeMirror.getStartTime().after(now) || adminRuleScoreNodeMirror.getEndTime().before(now)){
                LoggerProxy.info(LOGGER, "ruleScore_experiment not hit time expCode = {}",adminRuleScoreNodeMirror.getRuleMirrorCode());
                continue;
            }

            /**
             * 获取镜像版本绑定的变量
             */
            String ruleKey = adminRuleScoreNodeMirror.getRuleKey();
            Integer version = adminRuleScoreNodeMirror.getRuleVersion();
            List<AdminRuleScoreVar> ruleScoreVarList = adminRuleScoreVarMapper.findRuleScoreVar(ruleKey, version);

            //需要新请求变量中心的变量集合
            List<String> prefetchVariableList =
                    ruleScoreVarList.stream().map(AdminRuleScoreVar::getVariableCode).collect(Collectors.toList());
            String variableMapStr = strategyVo.getVariableMapStr();
            if (StringUtils.isNotEmpty(variableMapStr)){
                JSONObject variableJson = JSONObject.parseObject(variableMapStr);
                prefetchVariableList.removeAll(variableJson.keySet());
            }
            log.info("ruleScore_experiment prefetchVariableList = {}",prefetchVariableList);
            /** 调用变量中心,等待回调设置 **/
            if (!prefetchVariableList.isEmpty()){
                Map dataInput = strategyVo.getDataInput();
                String loanKey =
                        strategyVo.getLoanKey() + "_mirror" + "_" + adminRuleScoreNodeMirror.getNode() + "_" + adminRuleScoreNodeMirror.getId();
                String userKey = strategyVo.getUserKey();
                String eventCode = strategyVo.getEventCode() + "_mirror";
                String requestId = strategyVo.getLoanKey() +"_mirror_"+System.currentTimeMillis();

                /** mongo变量流程表：记录adminRuleScoreNodeMirror和strategyVo **/
                RuleScoreMirrorVarVo ruleScoreMirrorVarVo = new RuleScoreMirrorVarVo();
                ruleScoreMirrorVarVo.setLoanKey(loanKey);
                ruleScoreMirrorVarVo.setUserKey(userKey);
                ruleScoreMirrorVarVo.setRequestId(requestId);
                ruleScoreMirrorVarVo.setPrefetchVariableList(prefetchVariableList);
                ruleScoreMirrorVarVo.setStrategyVo(strategyVo);
                ruleScoreMirrorVarVo.setAdminRuleScoreNodeMirror(adminRuleScoreNodeMirror);
                ruleScoreMirrorVarVoDao.insert(ruleScoreMirrorVarVo);

                Map<String, Object> requestParams = buildRequestVariableCenterParams(loanKey,userKey,eventCode,step,requestId,dataInput,prefetchVariableList);
                requestVariableCenter(requestParams);
            }else {
                if (ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.FS_SPACE, "log.detail.model", false)) {
                    LoggerProxy.info(LOGGER, "ruleScore_experiment runMirror adminRuleScoreNodeMirror= {}", JSON.toJSONString(adminRuleScoreNodeMirror), JSON.toJSONString(strategyVo));
                }
                runMirror(adminRuleScoreNodeMirror, strategyVo);
            }
        }
    }

    /**
     * 请求变量中心
     * @param loanKey
     * @param userKey
     * @param eventCode
     * @param strategyNodeId
     * @param requestId
     * @param dataInput
     * @param prefetchVariableList
     * @return
     */
    private Map<String, Object> buildRequestVariableCenterParams(String loanKey,String userKey,String eventCode,String strategyNodeId,String requestId,Map dataInput,List<String> prefetchVariableList) {
        Map<String, Object> datasourceConfig = buildDatasourceConfig(strategyNodeId);
        Map<String, Object> requestParams = new HashMap<>(16);
        requestParams.put("loanKey", loanKey);
        requestParams.put("userKey", userKey);
        requestParams.put("eventCode", eventCode);
        requestParams.put("strategyId", strategyNodeId);
        requestParams.put("eventName", eventCode);
        requestParams.put("step", strategyNodeId);
        requestParams.put("requestId", requestId);
        requestParams.put("isMirror", true);
        requestParams.put("callbackSystem","RISK_FS_MIRROR");

        Map<String, Object> baseParams = new HashMap<>(16);
        baseParams.put("strategyId", strategyNodeId);
        baseParams.putAll(dataInput);

        requestParams.put("baseParams", baseParams);
        requestParams.put("varCodes", prefetchVariableList);
        requestParams.put("cachedVarCodes", new ArrayList<>());
        requestParams.put("dataSourceParamMap", datasourceConfig);
        requestParams.put("dataInput",dataInput);
        requestParams.put("businessLine", "RIS");
        return requestParams;
    }

    public boolean requestVariableCenter(Map<String, Object> requestParams) {

        String logQueryFlag = String.format("requestVariableCenter params=%s",JSON.toJSONString(requestParams));
        String url = variableCenterServiceUrl + "/api/variable/process/v2/" + requestParams.get("eventCode");
        String responseStr = null;
        try {
            responseStr = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(requestParams),10000);
        } catch (Exception e) {
            LoggerProxy.error("变量节点执行", LOGGER, "请求变量中心：{}，异常=", logQueryFlag, e);
            return false;
        }
        if (com.youxin.risk.commons.utils.StringUtils.isBlank(responseStr)) {
            LoggerProxy.error("变量节点执行", LOGGER, "请求变量中心：{}，返回空数据", logQueryFlag);
            return false;
        }
        JSONObject responseJSON = JSON.parseObject(responseStr);
        Integer status = responseJSON.getInteger("status");
        if (0 != status) {
            LoggerProxy.error("变量节点执行", LOGGER, "请求变量中心：{}，返回异常数据={}", logQueryFlag, responseStr);
            return false;
        }
        LoggerProxy.info("变量节点执行", LOGGER, "请求变量中心：{}，请求地址={},返回结果={}", logQueryFlag, url, responseStr);
        return true;
    }

    private Map<String, Object> buildDatasourceConfig(String strategyNodeId){
        //根据节点id 获取数据源配置信息
        List<DatasourceConfigDO> datasourceConfigList =
                nodeConfigMapper.selectDatasourceConfigByStrategyNode(strategyNodeId);

        Map<String, Object> result = new HashMap<>(datasourceConfigList.size());
        for (DatasourceConfigDO datasourceConfigDO : datasourceConfigList) {
            String datasourceCode = datasourceConfigDO.getDatasourceCode();
            result.put(datasourceCode, datasourceConfigDO.getVariableCenterId());
        }
        return result;
    }

    /**
     * 运行镜像
     * @param adminRuleScoreNodeMirror
     * @param strategyVo
     */
    public void runMirror(AdminRuleScoreNodeMirror adminRuleScoreNodeMirror,StrategyVo strategyVo) {
        AdminRuleScoreCandidate adminRuleScoreCandidate = adminRuleScoreCandidateMapper
                .findRuleCandidateScoreByRuleKeyVersion(adminRuleScoreNodeMirror.getRuleKey()
                        , adminRuleScoreNodeMirror.getRuleVersion());
        if (adminRuleScoreCandidate == null){
            return;
        }
        RuleScoreMirrorResultVo ruleScoreMirrorResultVo = new RuleScoreMirrorResultVo();
        /** 线上的策略结果规则分变量 **/
        String result = null;
        JSONObject ruleResults = JSONObject.parseObject(strategyVo.getRuleSetMapStr());
        if (ruleResults != null){
            result = ruleResults.getString(adminRuleScoreCandidate.getRuleKey());
        }
        /** 镜像结果 **/
        String mirrorResult = calMirrorResult(adminRuleScoreCandidate, strategyVo, ruleScoreMirrorResultVo);
        if (StringUtils.isEmpty(mirrorResult)){
            return;
        }
        LoggerProxy.info(LOGGER, "ruleScore_experiment mirrorResult = {}",mirrorResult);
        /** 写入mongo **/
        ruleScoreMirrorResultVo.setNode(adminRuleScoreNodeMirror.getNode());
        ruleScoreMirrorResultVo.setStrategyCodeType(strategyVo.getType());
        ruleScoreMirrorResultVo.setMirrorInput(buildInput(strategyVo));
        ruleScoreMirrorResultVo.setOnlineOutput(result);
        ruleScoreMirrorResultVo.setMirrorOutput(mirrorResult);
        ruleScoreMirrorResultVo.setUserKey(strategyVo.getUserKey());
        ruleScoreMirrorResultVo.setLoanKey(strategyVo.getLoanKey());
        ruleScoreMirrorResultVo.setExpCode(adminRuleScoreNodeMirror.getRuleMirrorCode());
        ruleScoreMirrorResultVo.setCreateTime(new Date());
        // 差异对比
        diffCompare(adminRuleScoreNodeMirror, ruleScoreMirrorResultVo);
        ruleScoreResultMongoDao.insert(ruleScoreMirrorResultVo);

        // 规则集镜像结果发送到数仓进行消费
        try {
            eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, ruleScoreMirrorResultSender,
                    JSON.toJSONString(ruleScoreMirrorResultVo));
        } catch (Exception e) {
            LoggerProxy.warn("sendRuleScoreMirrorResultToKafKaError", LOGGER, "", e);
        }
    }

    private String calMirrorResult(AdminRuleScoreCandidate adminRuleScoreCandidate,StrategyVo strategyVo,RuleScoreMirrorResultVo ruleScoreMirrorResultVo){
        String url = baseBatchURLPython3 + "/run_policy_async_rules";
        try {
            JSONObject reqVo =  new JSONObject();
            reqVo.put("variableMap", JSONObject.parseObject(strategyVo.getVariableMapStr()));
            reqVo.put("ruleSetMap",JSONObject.parseObject(strategyVo.getRuleSetMapStr()));
            reqVo.put("loanKey",strategyVo.getLoanKey());
            reqVo.put("step",strategyVo.getStep());
            reqVo.put("eventCode",strategyVo.getEventCode());
            reqVo.put("type",strategyVo.getType());
            reqVo.put("ruleKey",adminRuleScoreCandidate.getRuleKey());
            reqVo.put("ruleVersion",adminRuleScoreCandidate.getRuleVersion());
            /** 调用计算接口 **/
            HttpResult httpResult = HttpUtil.post(url, JsonUtils.toJson(reqVo));
            if (httpResult.getStatus() != HttpStatus.SC_OK){
                /** 如果是因为网络问题异常，则略过 **/
                LoggerProxy.error("规则分镜像执行报错", LOGGER, "网络问题异常，本次镜像忽略，不计入统计，expCode={}, errMsg=",
                        ruleScoreMirrorResultVo.getExpCode(), httpResult.getMessage());
                return null;
            }
            JSONObject json = JSONObject.parseObject(httpResult.getMessage());
            // 判断status是否为0 0:成功  1:失败 失败获取message信息
            if (json.getInteger("status") == 0){
                ruleScoreMirrorResultVo.setStatus(RULE_EXPERIMENT_RESULT_STATUS_NO_DIFF);
                return json.getString("result");
            }
            // 失败信息保存到镜像结果，方便策略人员查看
            ruleScoreMirrorResultVo.setStatus(RULE_EXPERIMENT_RESULT_STATUS_FAILED);
            return json.getString("message");
        } catch (Exception e) {
            //  todo 理论上不会走到这里
            /** 如果是因为网络问题异常，则略过 **/
            LoggerProxy.error("规则分镜像执行报错", LOGGER, "网络问题异常，本次镜像忽略，不计入统计，expCode={}, errMsg=",
                    ruleScoreMirrorResultVo.getExpCode(), e);
            return null;
        }
    }

    /**
     * 构建规则分输入strategyVo.getVariableMapStr()、strategyVo.getRuleSetMapStr()组装成json
     * {
     *     "vars": JSON.parseObject(variableMapStr),
     *     "rules": JSON.parseObject(ruleSetMapStr)
     * }
     */
    private String buildInput(StrategyVo strategyVo) {
        JSONObject input = new JSONObject();
        input.put("vars", JSONObject.parseObject(strategyVo.getVariableMapStr()));
        input.put("rules", JSONObject.parseObject(strategyVo.getRuleSetMapStr()));
        return JSON.toJSONString(input);
    }

    /**
     * 差异对比方法
     */
    private void diffCompare(AdminRuleScoreNodeMirror adminRuleScoreNodeMirror, RuleScoreMirrorResultVo ruleScoreMirrorResultVo) {
        try{
            JsonDiffComparatorNew jsonDiffComparator = JsonDiffComparatorNew
                    .build(ruleScoreMirrorResultVo.getOnlineOutput(), ruleScoreMirrorResultVo.getMirrorOutput());
            // 判断是否要全部对比
            if (adminRuleScoreNodeMirror.getRuleCompareType().equals(CompareTypeEnum.BY_ALL.getCode())) {
                jsonDiffComparator.doBuildDiff(Collections.emptySet(), Collections.emptySet(), true);
            }else {
                // 包含字段
                Set<String> containFieldSet = JsonDiffComparatorNew.getCmpFieldSet(adminRuleScoreNodeMirror.getContainField());
                // 排除字段
                Set<String> excludeFieldSet = JsonDiffComparatorNew.getCmpFieldSet(adminRuleScoreNodeMirror.getExcludeField());
                jsonDiffComparator.doBuildDiff(containFieldSet, excludeFieldSet, false);
            }
            ruleScoreMirrorResultVo.setDiffResultList(jsonDiffComparator.getDiffResult());
            // 判断DiffResultList中是否有非NO_DIFF的数据 如果有则为有差异 否则为无差异
            List<ExperimentCompareResultVo> diffResultList = ruleScoreMirrorResultVo.getDiffResultList();
            boolean hasDifference = diffResultList.stream()
                    .anyMatch(result -> !result.getDiffType().equals(CompareDiffTypeEnums.NO_DIFF.name()));
            ruleScoreMirrorResultVo.setStatus(hasDifference
                    ? RULE_EXPERIMENT_RESULT_STATUS_DIFF : RULE_EXPERIMENT_RESULT_STATUS_NO_DIFF);
        }catch (Exception e){
            LoggerProxy.error("规则分镜像对比异常",LOGGER,"错误信息为：{}", e);
            // 无法镜像对比的 代表策略执行异常 置为失败
            ruleScoreMirrorResultVo.setStatus(RULE_EXPERIMENT_RESULT_STATUS_FAILED);
        }
    }
}
