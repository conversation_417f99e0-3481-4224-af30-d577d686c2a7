package com.youxin.risk.fs.model;
import com.youxin.risk.fs.vo.DataSubmitVo;

/**
 * <AUTHOR>
 * @date   2018年05月14日下午15:46:37
 */
public class FeatureDataVo {
	
	 private Feature feature;
	 private DataSubmitVo dataSubmitVo;
	 private String miniDataVo;
	public Feature getFeature() {
		return feature;
	}
	public void setFeature(Feature feature) {
		this.feature = feature;
	}
	public DataSubmitVo getDataSubmitVo() {
		return dataSubmitVo;
	}
	public void setDataSubmitVo(DataSubmitVo dataSubmitVo) {
		this.dataSubmitVo = dataSubmitVo;
	}
	public String getMiniDataVo() {
		return miniDataVo;
	}
	public void setMiniDataVo(String miniDataVo) {
		this.miniDataVo = miniDataVo;
	}
}
