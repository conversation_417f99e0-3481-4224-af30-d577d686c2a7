package com.youxin.risk.fs.model;
import java.util.Date;

/**
 * <AUTHOR>
 * @date   2018年05月25日下午13:26:27
 */
public class FeatureCandidate {


	//自增主键
	private Long id;

	//系统
	private String sourceSystem;

	//特征名称
	private String featureName;

	//特征版本
	private String featureVer;

	//路径
	private String nodePath;

	//步骤
	private String applyStep;

	//用户邮箱
	private String developer;

	//特征组名
	private String groupName;

	//特征组版本
	private String groupVer;
	
	//特征回调ID
	private Integer callBackId;

	//代码ID
	private Long featureCodeId;

	//运行次数
	private Long runCount;

	//是否完成运行次数
	private Boolean isFinished;

	//运行结果
	private Integer runStatus;

	//错误信息
	private String errorMsg;

	//是否已邮件通知
	private Boolean isNotified;

	//创建时间
	private Date createTime;

	//更新时间
	private Date updateTime;

	//删除标识
	private Boolean delFlag;

	//版本
	private Integer version;
	
	public Integer getCallBackId() {
        return callBackId;
    }

    public void setCallBackId(Integer callBackId) {
        this.callBackId = callBackId;
    }

    public Long getId() {
	    return this.id;
	}

	public void setId(Long id) {
	    this.id=id;
	}

	public String getSourceSystem() {
	    return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
	    this.sourceSystem=sourceSystem;
	}

	public String getFeatureName() {
	    return this.featureName;
	}

	public void setFeatureName(String featureName) {
	    this.featureName=featureName;
	}

	public String getFeatureVer() {
	    return this.featureVer;
	}

	public void setFeatureVer(String featureVer) {
	    this.featureVer=featureVer;
	}

	public String getNodePath() {
	    return this.nodePath;
	}

	public void setNodePath(String nodePath) {
	    this.nodePath=nodePath;
	}

	public String getApplyStep() {
	    return this.applyStep;
	}

	public void setApplyStep(String applyStep) {
	    this.applyStep=applyStep;
	}

	public String getDeveloper() {
	    return this.developer;
	}

	public void setDeveloper(String developer) {
	    this.developer=developer;
	}

	public String getGroupName() {
	    return this.groupName;
	}

	public void setGroupName(String groupName) {
	    this.groupName=groupName;
	}

	public String getGroupVer() {
	    return this.groupVer;
	}

	public void setGroupVer(String groupVer) {
	    this.groupVer=groupVer;
	}

	public Long getFeatureCodeId() {
	    return this.featureCodeId;
	}

	public void setFeatureCodeId(Long featureCodeId) {
	    this.featureCodeId=featureCodeId;
	}

	public Long getRunCount() {
	    return this.runCount;
	}

	public void setRunCount(Long runCount) {
	    this.runCount=runCount;
	}

    public Boolean getIsFinished() {
        return this.isFinished;
    }

    public void setIsFinished(Boolean isFinished) {
        this.isFinished = isFinished;
    }

    public Integer getRunStatus() {
        return this.runStatus;
    }

    public void setRunStatus(Integer runStatus) {
        this.runStatus = runStatus;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Boolean getIsNotified() {
        return this.isNotified;
    }

    public void setIsNotified(Boolean isNotified) {
        this.isNotified = isNotified;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}
