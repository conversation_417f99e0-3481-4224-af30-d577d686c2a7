package com.youxin.risk.fs.service.handler;

import com.alibaba.fastjson.JSONObject;

/**
 * @InterfaceName TransDataSubmitHandler
 * @Description 返回数据
 * <AUTHOR>
 * @Date 2022/1/11 10:47 上午
 **/
public interface FillDataSubmitHandler {
    /**
     * 将数据的jobId填充具体数据
     * @param thirdPartyData
     * @param vulcanData
     * @param key
     */
    void setVulcanData(String thirdPartyData, JSONObject vulcanData, String key);

    /**
     * 数仓会对返回的第三方数据:特殊处理
     * @param thirdPartyData
     */
    String thirdPartVulcanData(String thirdPartyData);
}
