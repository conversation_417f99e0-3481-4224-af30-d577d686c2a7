package com.youxin.risk.fs.experiment.feature.collector;

import com.youxin.risk.fs.vo.PreDataSubmitVo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public class SampleCollectionContext {
    @Setter
    private Object sampleData;
    private String sampleKey;
    private final PreDataSubmitVo requestVo;
    private final Map<String, Object> extendPrams = new HashMap<>();

    public SampleCollectionContext(PreDataSubmitVo requestVo) {
        this.requestVo = requestVo;
        this.sampleKey = requestVo.getFeatureName() + requestVo.getStep();
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> valueType) {
        return (T) extendPrams.get(key);
    }

    public void put(String key, Object value) {
        extendPrams.put(key, value);
    }

    public String getFeatureName() {
        return this.requestVo.getFeatureName();
    }

    public String getStep() {
        return this.requestVo.getStep();
    }

    public long getCurrentSampleSize() {
        if (Objects.isNull(requestVo.getData())) {
            return 0L;
        }
        return requestVo.getData().getBytes().length;
    }
}
