package com.youxin.risk.fs.experiment.feature.dao;

import com.mongodb.BasicDBObjectBuilder;
import com.mongodb.DBCollection;
import com.youxin.risk.fs.experiment.feature.HistoryDataQueryHelper;
import com.youxin.risk.fs.experiment.feature.collector.sample.BigRangeFeatureSample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 特征样本Dao
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BigRangeFeatureSampleMongoDao extends BaseExpMongoDao {

    public BigRangeFeatureSampleMongoDao() {
        collectionName = BigRangeFeatureSample.TABLE_NAME;
    }

    @PostConstruct
    public void init() {
        if(!template.collectionExists(BigRangeFeatureSample.TABLE_NAME)){
            DBCollection collection = template.createCollection(BigRangeFeatureSample.TABLE_NAME);
            collection.createIndex(BasicDBObjectBuilder.start("featureName",1)
                    .add("step", 1).add("createTime", -1).get());
        }
    }

    public List<BigRangeFeatureSample> getSampleData(HistoryDataQueryHelper.QueryParams queryParams) {
        Criteria criteria = new Criteria();
        criteria.and("featureName").is(queryParams.getFeatureName());
        criteria.and("step").is(queryParams.getStep());
        Query query = new Query(criteria);
        query.limit(queryParams.getLimit());
        query.skip(queryParams.getSkip());
        query.with(new Sort(new Sort.Order(Sort.Direction.ASC,"createTime")));
        log.info("BigRangeFeatureSampleMongoDao_query={}", queryParams);
        return template.find(query, BigRangeFeatureSample.class);
    }

    public void delSample(BigRangeFeatureSample illegalSample) {
        Criteria criteria = new Criteria();
        criteria.and("_id").is(illegalSample.getId());
        Query query = new Query(criteria);
        template.remove(query, collectionName);
    }
}
