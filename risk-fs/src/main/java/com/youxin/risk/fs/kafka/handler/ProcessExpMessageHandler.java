package com.youxin.risk.fs.kafka.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.handler.impl.BaseKafKaMessageHandler;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.fs.kafka.sender.ProcessExperimentMessageSender;
import com.youxin.risk.fs.service.EventProcessExpService;
import com.youxin.risk.fs.vo.DataSubmitVo;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.fs.service.EventProcessExpService.buildLogId;

/**
 * 流程镜像消息处理类
 *
 * <AUTHOR>
 * @see ProcessExperimentMessageSender
 */
public class ProcessExpMessageHandler extends BaseKafKaMessageHandler {

    public static final String FEATURE_EXP_MESSAGE_TAG = "process_feature";
    public static final String STRATEGY_EXP_MESSAGE_TAG = "process_strategy";

    @Resource
    private EventProcessExpService eventProcessExpService;

    @Override
    protected void handler0(KafkaContext context) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        LoggerProxy.info(logger, "test tag={}", context.getTag());
        try {
            if (FEATURE_EXP_MESSAGE_TAG.equals(context.getTag())) {
                // 组装DataSubmitVo
                DataSubmitVo dataSubmitVo = JSON.parseObject(context.getMessage(), DataSubmitVo.class);
                handleFeatureExpMessage(context, dataSubmitVo, stopwatch);
            } else if (STRATEGY_EXP_MESSAGE_TAG.equals(context.getTag())) {
                // 组装StrategyVo
                StrategyVo strategyVo = JSON.parseObject(context.getMessage(), StrategyVo.class);
                handleStrategyExpMessage(context, strategyVo, stopwatch);
            } else {
                throw new IllegalArgumentException("[" + context.getTag() + "] message tag not support");
            }
        } catch (Exception e) {
            LoggerProxy.error("parseExpMessageError", logger, "", e);
        }
    }

    private void handleFeatureExpMessage(KafkaContext context, DataSubmitVo dataSubmitVo, Stopwatch stopwatch) {
        // 执行实验特征
        LogUtil.bindLogId(buildLogId(dataSubmitVo.getLoanKey()));
        LoggerProxy.info(logger, "received feature process experiment message, sourceSystem={}, step={}, userKey={}, loanKey={}", dataSubmitVo.getSourceSystem(), dataSubmitVo.getStep(), dataSubmitVo.getUserKey(), dataSubmitVo.getLoanKey());
        try {
            eventProcessExpService.runFeatureProcessExperiment(dataSubmitVo, true);
        } catch (Exception e) {
            LoggerProxy.error("dealFeatureProcessExpMessageError", logger, "step={}, userKey={}, loanKey={}",
                    dataSubmitVo.getStep(), dataSubmitVo.getUserKey(), dataSubmitVo.getLoanKey(), e);
        } finally {
            long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            LoggerProxy.info("finishedFeatureProcessExpMessage", logger, "sourceSystem={}, userKey={}, loanKey={}, step={}, cost={}",
                    dataSubmitVo.getSourceSystem(), dataSubmitVo.getUserKey(), dataSubmitVo.getLoanKey(), dataSubmitVo.getStep(), costTime);
            context.setRetCode(RetCodeEnum.SUCCESS);
            LogUtil.unbindLogId();
        }
    }

    private void handleStrategyExpMessage(KafkaContext context, StrategyVo strategyVo, Stopwatch stopwatch) {
        // 执行实验策略
        LogUtil.bindLogId(buildLogId(strategyVo.getLoanKey()));
        LoggerProxy.info(logger, "received strategy process experiment message, sourceSystem={}, step={}, userKey={}, loanKey={}", strategyVo.getSourceSystem(), strategyVo.getStep(), strategyVo.getUserKey(), strategyVo.getLoanKey());
        try {
            eventProcessExpService.runStrategyProcessExperiment(strategyVo, true);
        } catch (Exception e) {
            LoggerProxy.error("dealStrategyProcessExpMessageError", logger, "sourceSystem={}, type={}, userKey={}, loanKey={}",
                    strategyVo.getSourceSystem(), strategyVo.getType(), strategyVo.getUserKey(), strategyVo.getLoanKey(), e);
        } finally {
            long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            LoggerProxy.info("finishedStrategyExperimentMessage", logger, "userKey={}, loanKey={}, step={}, type={}, cost={}",
                    strategyVo.getUserKey(), strategyVo.getLoanKey(), strategyVo.getStep(), strategyVo.getType(), costTime);
            context.setRetCode(RetCodeEnum.SUCCESS);
            LogUtil.unbindLogId();
        }
    }
}
