package com.youxin.risk.fs.service.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.fs.controller.DiServiceController;
import com.youxin.risk.fs.dao.rm.RmDataSubmitIndexMapper;
import com.youxin.risk.fs.es.IndexService;
import com.youxin.risk.fs.hbase.DataSubmitHB;
import com.youxin.risk.fs.hbase.HbaseService;
import com.youxin.risk.fs.hbase.utils.HbaseUtil;
import com.youxin.risk.fs.model.ESDataSubmit;
import com.youxin.risk.fs.model.EsDataSubmitVo;
import com.youxin.risk.fs.model.RmDataSubmitIndex;
import com.youxin.risk.fs.service.DataSubmitService;
import com.youxin.risk.fs.utils.StringUtils;
import com.youxin.risk.fs.vo.DataSubmitVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class DataSubmitServiceImpl implements DataSubmitService {

    private static final Logger logger = LoggerFactory.getLogger(DiServiceController.class);

    @Resource
    private RmDataSubmitIndexMapper rmDataSubmitIndexMapper;

    @Autowired
    private HbaseService hbaseService;

    @Autowired
    private IndexService indexService;

    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @Override
    public void saveDataSubmitToHbase(DataSubmitVo dataSubmitVo) {
        //将数据保存至hbase
        String rowKey = HbaseUtil.getRowKey();
        DataSubmitHB dataSubmit = new DataSubmitHB();
        BeanUtils.copyProperties(dataSubmitVo,dataSubmit);
        dataSubmit.setRowKey(rowKey);
        hbaseService.put(dataSubmit);
        //将索引保存至mysql数据库
        RmDataSubmitIndex rmDataSubmitIndex = new RmDataSubmitIndex();
        BeanUtils.copyProperties(dataSubmitVo,rmDataSubmitIndex);
        rmDataSubmitIndex.setRowKey(rowKey);
        if (rmDataSubmitIndex.getEngineEventCreateTime() == null){
            rmDataSubmitIndex.setEngineEventCreateTime(new Date());
        }
        rmDataSubmitIndexMapper.insert(rmDataSubmitIndex);
    }

    @Override
    public void saveDataSubmitToES(DataSubmitVo dataSubmitVo) {
        String rowKey = HbaseUtil.getRowKey();
        ESDataSubmit esDataSubmit = new ESDataSubmit();
        esDataSubmit.setId(rowKey);
        esDataSubmit.setSourceSystem(dataSubmitVo.getSourceSystem());
        esDataSubmit.setUserKey(dataSubmitVo.getUserKey());
        esDataSubmit.setLoanKey(dataSubmitVo.getLoanKey());
        esDataSubmit.setStep(dataSubmitVo.getStep());
        esDataSubmit.setEventCode(dataSubmitVo.getEventCode());

        Date date = new Date();
        String strDateFormat = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
        esDataSubmit.setCreateTime(sdf.format(date));
        esDataSubmit.setEngineEventCreateTime(sdf.format(date));
        esDataSubmit.setSearchCreateTime(date.getTime());
        esDataSubmit.setSearchEngineEventCreateTime(date.getTime());
        esDataSubmit.setData(dataSubmitVo.getData());

        try {
            indexService.addDataSubmit(esDataSubmit,rowKey);
        } catch (Exception e) {
            LoggerProxy.error("saveDataSubmitToES",logger,"error",e);
        }
    }

    @Override
    public Object getDataSubmit(String rowKey,boolean batchEvent) {
        logger.info("getDataSubmit rowkey:{},batchEvent:{}",rowKey,batchEvent);
        if(batchEvent){
            return indexService.searchDataSubmitById(rowKey);
        }else {
            DataSubmitHB dataSubmit = new DataSubmitHB();
            dataSubmit.setRowKey(rowKey);
            hbaseService.queryByRowkey(rowKey,dataSubmit);
            DataSubmitVo dataSubmitVo = new DataSubmitVo();
            BeanUtils.copyProperties(dataSubmit,dataSubmitVo);
            return dataSubmitVo;
        }
    }


    @Override
    public Object getDataSubmitByES(String loanKey,String userKey,String eventCode,String sourceSystem,String step) {
        logger.info("getDataSubmitByES loanKey:{},userKey:{},eventCode:{},sourceSystem:{},step:{}",loanKey, userKey, eventCode, sourceSystem, step);
        EsDataSubmitVo vo = null;
        try {
            List<EsDataSubmitVo> voList = indexService.searchDataSubmit(loanKey, userKey, eventCode, sourceSystem, step, 1, null, null);
            if(CollectionUtils.isNotEmpty(voList)){
                vo = voList.get(0);
            }
        }catch (Exception e){
            logger.error("getDataSubmitByES searchError: ", e);
        }
        return vo;
    }



    /**
     *  获取 DataSubmit 的简略数据 不包含 data 和 xml
     */
    public DataSubmitVo getSimpleDataSubmit(String rowKey) {
        DataSubmitHB dataSubmit = new DataSubmitHB();
        dataSubmit.setRowKey(rowKey);
        hbaseService.querySimpleByRowkey(rowKey,dataSubmit);
        DataSubmitVo dataSubmitVo = new DataSubmitVo();
        BeanUtils.copyProperties(dataSubmit,dataSubmitVo);
        return dataSubmitVo;
    }

    public Date str2Date(String strDate) {
        ParsePosition pos = new ParsePosition(0);
        return formatter.parse(strDate, pos);
    }

    @Override
    public Object getMergedDataSubmitList(String userKey, String eventCode, String sourceSystem,
                                                      List<String> loanKeyList, String step, Integer limit,
                                                      String createTimeStart, String createTimeEnd,boolean batchEvent) {
        if(batchEvent){
            return this.getMergedDataSubmitListFromEs(userKey,eventCode,sourceSystem,loanKeyList,step,limit,createTimeStart,createTimeEnd);
        }else {
            return this.getMergedDataSubmitListFromHbase(userKey,eventCode,sourceSystem,loanKeyList,step,limit,createTimeStart,createTimeEnd);
        }
    }


    private List<EsDataSubmitVo> getMergedDataSubmitListFromEs(String userKey, String eventCode, String sourceSystem,
                                                             List<String> loanKeyList, String step, Integer limit,
                                                             String createTimeStart, String createTimeEnd){
        logger.info("getMergedDataSubmitList params, userKey:{},eventCode:{},sourceSystem:{},loanKeyList:{},step:{}," +
                "limit:{},createTimeStart:{},createTimeEnd:{}",
                userKey, eventCode, sourceSystem, JSON.toJSON(loanKeyList), step, limit, createTimeStart, createTimeEnd);
        List<EsDataSubmitVo> dataSubmitList = new ArrayList<>();
        String loanKeys = StringUtils.join(loanKeyList, ",");
        try {
            dataSubmitList = indexService.searchDataSubmit(loanKeys, userKey, eventCode, sourceSystem, step, limit, createTimeStart, createTimeEnd);
            dataSubmitList.forEach(dataSubmitVo -> {
                dataSubmitVo.setRowKey(String.valueOf(dataSubmitVo.getId()));
            });
        } catch (Exception e) {
            LoggerProxy.error("getMergedDataSubmitList",logger,"searchDataSubmit error",e);
        }
        return dataSubmitList;
    }


    private List<DataSubmitVo> getMergedDataSubmitListFromHbase(String userKey, String eventCode, String sourceSystem,
                                                                List<String> loanKeyList, String step, Integer limit,
                                                                String createTimeStart, String createTimeEnd){
        //从mysql索引表获取最近的limit条rowkey
        List<DataSubmitVo> dataSubmitList = new ArrayList<>();
        logger.info("getMergedDataSubmitList params, userKey:{},eventCode:{},sourceSystem:{},loanKeyList:{},step:{}," +
                        "limit:{},createTimeStart:{},createTimeEnd:{}",
                userKey, eventCode, sourceSystem, JSON.toJSON(loanKeyList), step, limit, createTimeStart, createTimeEnd);
        Date createTimeStartDate = StringUtils.isBlank(createTimeStart) ? null : str2Date(createTimeStart);
        Date createTimeEndDate = StringUtils.isBlank(createTimeEnd) ? null : str2Date(createTimeEnd);
        List<String> rowKeys = rmDataSubmitIndexMapper.getRowKeysByConditions(loanKeyList, userKey, eventCode,
                sourceSystem, step, limit, createTimeStartDate, createTimeEndDate);
        logger.info("getMergedDataSubmitList rowKeys:{}", JSON.toJSON(rowKeys));
        //如果laonKey在索引表中能查出rowKey说明该条数据在hbase中
        if (CollectionUtils.isNotEmpty(rowKeys)) {

            // 超过10条使用并行流查询，降低响应时间
            Stream<String> rowKeyStream = rowKeys.size() >= 10 ? rowKeys.parallelStream() : rowKeys.stream();
            //根据 rowKey查询hbase

            // 并行流的正确使用方式
            dataSubmitList = rowKeyStream.collect(ArrayList::new, (arrayList, rowKey) -> {
                DataSubmitVo dataSubmitVo = getSimpleDataSubmit(rowKey);
                if (StringUtils.isNotEmpty(dataSubmitVo.getUserKey())) {
                    arrayList.add(dataSubmitVo);
                }
            }, List::addAll);
        }
        return dataSubmitList;
    }
}
