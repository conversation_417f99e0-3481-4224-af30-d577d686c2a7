package com.youxin.risk.fs.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.utils.DataVoUtils;
import com.youxin.risk.commons.vo.Tuple;
import com.youxin.risk.fs.dao.rm.RmDataSubmitIndexMapper;
import com.youxin.risk.fs.hbase.DataSubmitHB;
import com.youxin.risk.fs.hbase.HbaseService;
import com.youxin.risk.fs.hbase.HbaseTable;
import com.youxin.risk.fs.service.impl.FeatureServiceImpl;
import com.youxin.risk.fs.service.impl.TransDataSubmitManager;
import com.youxin.risk.fs.utils.EventUtils;
import com.youxin.risk.fs.vo.DataSubmitVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.FS_SPACE;

/**
 * 数仓dataVo补数
 * <AUTHOR>
 *
 * @see FeatureServiceImpl#getFeatureXml(com.youxin.risk.fs.vo.DataSubmitVo)
 */
@Slf4j
@Service
public class DataSubmitVoCompensationTool {
    @Autowired
    private TransDataSubmitManager transDataSubmitManager;
    @Autowired
    private EventService eventService;
    @Resource(name = "dataSubmitSender")
    private KafkaSyncSender dataSubmitSender;
//    @Resource(name = "dataSubmitBatchSender")
//    private KafkaSyncSender dataSubmitBatchSender;
    @Resource
    private RmDataSubmitIndexMapper rmDataSubmitIndexMapper;
    @Autowired
    private HbaseService hbaseService;

    @XxlJob("reSendDataMessageToVulan")
    public ReturnT execute(String params) {
        if (!isEnable()) {
            return ReturnT.SUCCESS;
        }
        try {
            getDataAndSendMessage();
        } catch (Exception e) {
            log.info("reSendDataGetDataAndSendMessageError", e);
        }
        return ReturnT.SUCCESS;
    }

    private void getDataAndSendMessage() {
        LocalDateTime startLocalDt = LocalDateTime.of(2022, 9, 13, 18, 0);
        LocalDateTime endLocalDt = LocalDateTime.of(2022, 9, 13, 22, 0);
        while (true) {
            if (startLocalDt.isAfter(endLocalDt)) {
                return;
            }
            // 每次取一分钟的数据量
            LocalDateTime tempEndDt = startLocalDt.plusSeconds(60L);
            Date endTime = Date.from(tempEndDt.atZone(ZoneId.systemDefault()).toInstant());
            Date startTime = Date.from(startLocalDt.atZone(ZoneId.systemDefault()).toInstant());
            List<String> rowKeys = rmDataSubmitIndexMapper.getRowKeysByConditions(null, null, null, null, null, null, startTime, endTime);
            if (CollectionUtils.isNotEmpty(rowKeys)) {
                rowKeys.forEach(this::handleData);
            } else {
                log.info("getDataIsEmpty");
            }
            startLocalDt = tempEndDt;
        }
    }

    private void handleData(String rowKey) {
        if (!isEnable()) {
            return;
        }

        try {
            // 默认睡10分钟
            Long sleepTime = ApolloClientAdapter.getLongConfig(FS_SPACE, "thread.sleep.time", 10 * 60L);
            TimeUnit.SECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            log.info("resendDataMessageInterrupted");
            return;
        }

        DataSubmitHB dataSubmit = new DataSubmitHB();
        HbaseTable hbaseTable = hbaseService.queryByRowkey(rowKey, dataSubmit);
        if (hbaseTable == null) {
            log.info("queryByRowkeyIsNull, rowKey={}", rowKey);
            return;
        }

        DataSubmitVo vo = new DataSubmitVo();
        BeanUtils.copyProperties(dataSubmit, vo);
        //save VO
        String data = vo.getData();
        try {
            transDataSubmitManager.checkJobIdData(vo.getLoanKey(),vo);
        }catch (Exception e){
            log.info("reSendMessageCheckJobIdDataError, loanKey={}", vo.getLoanKey(), e);
        }
        // 将event中的dataVo数据处理
        Tuple<String, String> transData = transDataSubmitManager.transformData(vo.getLoanKey(),data);

        // 用于数仓侧的dataVo
        DataSubmitVo vulcanDataSubmitVo = new DataSubmitVo();
        BeanUtils.copyProperties(vo,vulcanDataSubmitVo);
        vulcanDataSubmitVo.setData(transData.getV());
        // 移除指定的key数据
        this.removeSpecifyData(vulcanDataSubmitVo);
        this.reSendMessage(vulcanDataSubmitVo);
        log.info("reSendDataVoMessageSuccess, loanKey={},step={}", vo.getLoanKey(), vo.getStep());
    }

    private void removeSpecifyData(DataSubmitVo vo){
        try{
            String removeDataKeys = CacheApi.getDictSysConfig("removeDataKeys", "sensorsData", ApolloNamespace.fsSpace);
            JSONObject dataVo = JSON.parseObject(vo.getData());
            JSONObject thirdPartyData = dataVo.getJSONObject(DataVoUtils.getThirdPartyDataKey(vo.getSourceSystem()));
            if(null != thirdPartyData){
                for (String key : removeDataKeys.split(",")) {
                    thirdPartyData.remove(key);
                }
                vo.setData(JSON.toJSONString(dataVo));
            }
        }catch (Exception ex){
            log.info("reSendMessageRemoveSpecifyDataError", ex);
        }
    }

    private void reSendMessage(DataSubmitVo vo) {
        try {
            String batchEventCodes = CacheApi.getDictSysConfig("batchEventCodes", "hhPreLoanAudit", ApolloNamespace.fsSpace);
            if(vo.getEventCode() != null
                    && Arrays.asList(batchEventCodes.split(",")).contains(vo.getEventCode())){
                // do nothing 数仓那边说不需要补
                //eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, dataSubmitBatchSender, JSON.toJSONString(vo));
            }else{
                eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, dataSubmitSender, JSON.toJSONString(vo));
            }
        } catch (Exception e) {
            log.info("reSendSubmitDataToVulcan, loanKey={}, data={}, e={}", vo.getLoanKey(), vo.getData(), e);
        }finally {
            log.info("reSendSubmitDataToVulcan, loanKey={} step={} eventCode={}", vo.getLoanKey(), vo.getStep(), vo.getEventCode());
        }
    }

    private boolean isEnable() {
        return ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.FS_SPACE, "enable.data.send.message", false);
    }
}
