package com.youxin.risk.fs.utils;

import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.constants.CompareDiffTypeEnums;
import com.youxin.risk.commons.constants.StrategyDiffTypeEnums;
import com.youxin.risk.commons.constants.StrategyExpDiffTypeEnum;
import com.youxin.risk.commons.model.StrategyExp;
import com.youxin.risk.commons.strategy.StrategyFinalStepEvaluator;
import com.youxin.risk.commons.utils.JsonDiffComparatorNew;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.ExperimentCompareResultVo;
import com.youxin.risk.commons.vo.StrategyExperimentResultVo;
import com.youxin.risk.commons.vo.StrategyVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 策略实验结果构建器
 * 使用构建者模式创建StrategyExperimentResultVo对象
 *
 * <AUTHOR>
 */
public class StrategyExperimentResultVoBuilder {
    private static final Logger LOGGER = LoggerFactory.getLogger(StrategyExperimentResultVoBuilder.class);
    
    private final StrategyExperimentResultVo resultVo;
    private final StrategyExp experiment;
    private final StrategyVo strategyVo;
    private String mirrorResult;
    private String onlineResult;
    private StrategyDiffTypeEnums status;

    /**
     * 私有构造函数，通过静态工厂方法创建实例
     *
     * @param experiment 策略实验信息
     * @param strategyVo 策略值对象
     */
    private StrategyExperimentResultVoBuilder(StrategyExp experiment, StrategyVo strategyVo) {
        this.resultVo = new StrategyExperimentResultVo();
        this.experiment = experiment;
        this.strategyVo = strategyVo;
        this.mirrorResult = strategyVo.getMirrorResult();
        this.onlineResult = strategyVo.getResult();
    }
    
    /**
     * 创建构建器实例
     *
     * @param experiment 策略实验信息
     * @param strategyVo 策略值对象
     * @return 构建器实例
     */
    public static StrategyExperimentResultVoBuilder create(StrategyExp experiment, StrategyVo strategyVo) {
        return new StrategyExperimentResultVoBuilder(experiment, strategyVo);
    }
    
    /**
     * 构建基础信息
     *
     * @return 构建器实例
     */
    public StrategyExperimentResultVoBuilder buildBasicInfo() {
        resultVo.setSourceSystem(strategyVo.getSourceSystem());
        resultVo.setExpCode(experiment.getExpCode());
        resultVo.setUserKey(strategyVo.getUserKey());
        resultVo.setLoanKey(strategyVo.getLoanKey());
        resultVo.setStep(strategyVo.getStep());
        resultVo.setCreateTime(new Date());
        resultVo.setResponse(mirrorResult);
        resultVo.setProductionResponse(onlineResult);

        if (resultVo.isErrorResponse()) {
            status = StrategyDiffTypeEnums.FAILED;
            LoggerProxy.warn("镜像策略结果异常", LOGGER, "loanKey={}, step={},errMsg={}", resultVo.getLoanKey(),
                    resultVo.getStep(), resultVo.getResponse());
        } else if (resultVo.isEmptyResponse()) {
            status = StrategyDiffTypeEnums.MIRROR_DELETE;
            LoggerProxy.warn("镜像策略步骤缺失", LOGGER, "loanKey={}, step={}", resultVo.getLoanKey(),
                    resultVo.getStep());
        } else if (resultVo.isEmptyProductionResponse()) {
            status = StrategyDiffTypeEnums.MIRROR_ADD;
            LoggerProxy.warn("镜像策略步骤新增", LOGGER, "loanKey={}, step={}", resultVo.getLoanKey(),
                    resultVo.getStep());
        }
        return this;
    }
    
    /**
     * 设置请求信息
     *
     * @param request 请求信息
     * @return 构建器实例
     */
    public StrategyExperimentResultVoBuilder withRequest(String request) {
        if(StringUtils.isBlank(request)) {
            request = "{}" ;
        }
        resultVo.setRequest(request);
        return this;
    }
    
    /**
     * 设置响应信息
     *
     * @return 构建器实例
     */
    public StrategyExperimentResultVoBuilder buildResponseInfo() {


        if (!resultVo.isInValidResponse()) {
            // 设置是否为该事件最终步骤
            resultVo.setFinal(StrategyFinalStepEvaluator.setIsFinal(
                    mirrorResult, strategyVo.getStep(), ApolloNamespace.fsSpace));
            //设置镜像通过状态
            resultVo.setPassedExp(JsonUtils.getIsPassed(mirrorResult));
        }

        if(!resultVo.isEmptyProductionResponse()){
            //设置线上通过状态
            resultVo.setPassedOnline(JsonUtils.getIsPassed(onlineResult));
        }

        return this;
    }

    
    /**
     * 构建差异比较结果
     *
     * @return 构建器实例
     */
    public StrategyExperimentResultVoBuilder buildDiffResult() {
        if (resultVo.isInValidResponseOrProductionResponse()) {
            resultVo.setStatus(status.getCode());
            return this;
        }

        try {
            JsonDiffComparatorNew jsonDiffComparator = JsonDiffComparatorNew
                    .build(onlineResult, mirrorResult);
            
            // 根据比较类型决定比较策略
            if (experiment.getCmpType().equals(StrategyExpDiffTypeEnum.DEFAULT.getCode())) {
                // 全部对比
                jsonDiffComparator.doBuildDiff(Collections.emptySet(), Collections.emptySet(), true);
            } else {
                // 按指定字段对比
                Set<String> containFieldSet = JsonDiffComparatorNew.getCmpFieldSet(experiment.getContainField());
                Set<String> excludeFieldSet = JsonDiffComparatorNew.getCmpFieldSet(experiment.getExcludeField());
                jsonDiffComparator.doBuildDiff(containFieldSet, excludeFieldSet, false);
            }
            
            // 设置差异结果
            List<ExperimentCompareResultVo> diffResultList = jsonDiffComparator.getDiffResult();
            resultVo.setDiffResultList(diffResultList);

            boolean hasDifference = diffResultList.stream()
                    .anyMatch(item -> !item.getDiffType().equals(CompareDiffTypeEnums.NO_DIFF.name()));
            status = hasDifference ? StrategyDiffTypeEnums.DIFF : StrategyDiffTypeEnums.NO_DIFF;

        } catch (Exception e) {
            LoggerProxy.error("镜像对比异常", LOGGER, "错误信息=", e);
            // 无法镜像对比的代表策略执行异常，置为失败
            status = StrategyDiffTypeEnums.FAILED;
        }
        resultVo.setStatus(status.getCode());
        return this;
    }
    
    /**
     * 构建最终结果对象
     *
     * @return 策略实验结果对象
     */
    public StrategyExperimentResultVo build() {
        return resultVo;
    }
}