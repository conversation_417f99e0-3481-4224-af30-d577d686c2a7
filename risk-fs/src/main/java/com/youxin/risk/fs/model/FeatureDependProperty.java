package com.youxin.risk.fs.model;

public class FeatureDependProperty {
    public static class DataType {
        public static final String thirdData = "thirdData";
        public static final String innerData = "innerData";
        public static final String offlineData = "offlineData";
    }
    private Integer id;

    private Long submitDetailId;

    private String dataType;

    private String dataName;

    private String dataCode;

    private String dataJoinPath;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getSubmitDetailId() {
        return submitDetailId;
    }

    public void setSubmitDetailId(Long submitDetailId) {
        this.submitDetailId = submitDetailId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType == null ? null : dataType.trim();
    }

    public String getDataName() {
        return dataName;
    }

    public void setDataName(String dataName) {
        this.dataName = dataName == null ? null : dataName.trim();
    }

    public String getDataCode() {
        return dataCode;
    }

    public void setDataCode(String dataCode) {
        this.dataCode = dataCode == null ? null : dataCode.trim();
    }

    public String getDataJoinPath() {
        return dataJoinPath;
    }

    public void setDataJoinPath(String dataJoinPath) {
        this.dataJoinPath = dataJoinPath == null ? null : dataJoinPath.trim();
    }
}