package com.youxin.risk.fs.service.impl;

import com.mongodb.WriteResult;
import com.youxin.risk.commons.mongo.StrategyExperimentResultMongoDao;
import com.youxin.risk.fs.service.StrategyExperimentResultService;
import com.youxin.risk.fs.utils.TaskUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class StrategyExperimentResultServiceImpl implements StrategyExperimentResultService {

    private Logger LOG = LoggerFactory.getLogger(StrategyExperimentResultServiceImpl.class);

    @Autowired
    private StrategyExperimentResultMongoDao dao;

    @Autowired
    private TaskUtil taskUtil;

    private long oneHour = 1000*60*60L;

    private int keepDays;

    private String keepDaysConfig = "StrategyExperimentResult_keep_days";


    @Override
    public void cleanWork() {
        keepDays = getKeepDays();
        Date minTime = dao.queryMinCreateTime();
        LOG.info("StrategyExperimentResult 清理参数minTime：{}",minTime);
        if(minTime == null){
            return;
        }
        if(System.currentTimeMillis() - oneHour * 24 * keepDays < minTime.getTime()){
            return;
        }
        Date cleanParam = new Date(minTime.getTime() + oneHour*24);
        WriteResult result = dao.deleteByTime(cleanParam,10000);
        if(result != null){
            LOG.info("StrategyExperimentResult 清理数据影响行数：{}",result.getN());
        }
    }


    private int getKeepDays(){
        int keepDays = taskUtil.queryConfigParam(keepDaysConfig);
        if(keepDays < 1){
            return 14;
        }
        return keepDays;
    }
}
