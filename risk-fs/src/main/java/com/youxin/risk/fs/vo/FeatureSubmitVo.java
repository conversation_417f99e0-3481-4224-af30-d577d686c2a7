package com.youxin.risk.fs.vo;

import java.util.List;

/**
 * 特征提交VO
 *
 * <AUTHOR>
 * @version 创建时间：2018年5月14日 下午3:50:21
 */
public class FeatureSubmitVo {

    private String sourceSystem;

    private String groupName;

    private String groupVer;

    private String applyStep;

    private String developer;

    private Integer callBackId;

    List<FeatureVo> featureList;

    public Integer getCallBackId() {
        return callBackId;
    }

    public void setCallBackId(Integer callBackId) {
        this.callBackId = callBackId;
    }

    public String getSourceSystem() {
        return this.sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getGroupName() {
        return this.groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }


    public String getGroupVer() {
        return this.groupVer;
    }

    public void setGroupVer(String groupVer) {
        this.groupVer = groupVer;
    }

    public String getApplyStep() {
        return this.applyStep;
    }

    public void setApplyStep(String applyStep) {
        this.applyStep = applyStep;
    }

    public String getDeveloper() {
        return this.developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }

    public List<FeatureVo> getFeatureList() {
        return this.featureList;
    }

    public void setFeatureList(List<FeatureVo> featureList) {
        this.featureList = featureList;
    }


}
