package com.youxin.risk.fs.service.impl;

import com.youxin.risk.commons.apollo.ApolloAdapter;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.model.ProcessExperiment;
import com.youxin.risk.commons.mongo.FeatureExperimentResultMongoDao;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.FeatureExperimentResultVo;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.fs.service.StrategyProcessCandidateService;
import com.youxin.risk.fs.service.StrategyProcessExpService;
import com.youxin.risk.fs.web.JsonResultVo;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/1/13 14:24
 */
@Service
public class StrategyProcessExpServiceImpl implements StrategyProcessExpService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StrategyProcessExpServiceImpl.class);

    @Resource
    private StrategyProcessCandidateService strategyProcessCandidateService;
    @Resource
    private FeatureExperimentResultMongoDao featureExperimentResultMongoDao;

    Map<String, String> strategyExpPoolConfig = ApolloAdapter.getMapConfig(ApolloNamespace.fsSpace, "strategyExpPool",
            "{\"corePoolSize\":\"10\",\"maximumPoolSize\":\"30\",\"queueSize\":\"1\"}");
    private final ThreadPoolExecutor pool = new ThreadPoolExecutor(Integer.parseInt(strategyExpPoolConfig.get("corePoolSize")),
            Integer.parseInt(strategyExpPoolConfig.get("maximumPoolSize"))
            , 60 * 1000L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(Integer.parseInt(strategyExpPoolConfig.get("queueSize"))),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void runExperiments(StrategyVo strategyVo) {
        List<ProcessExperiment> experiments = CacheApi.selectProcessExperiments(strategyVo.getSourceSystem(), strategyVo.getEventCode());
        if (CollectionUtils.isEmpty(experiments)) {
            LoggerProxy.info(LOGGER, "there is no experiment for the type, type={}", strategyVo.getType());
            return;
        }
        for (ProcessExperiment experiment : experiments) {
            Date now = new Date();
            if (experiment.getBeginTime().after(now) || experiment.getEndTime().before(now)) {
                continue;
            }

            // 如果没有运行特征镜像，那也不运行策略镜像
            if (!isRunFeatureProcessExp(strategyVo,experiment.getExpCode())) {
                LoggerProxy.info(LOGGER, "notNeedRunStrategyExp, loanKey={}, eventCode={}, step={}", strategyVo.getLoanKey(), strategyVo.getEventCode(), strategyVo.getStep());
                return;
            }
            LoggerProxy.info(LOGGER, "executeProcessExp, loanKey={}", strategyVo.getLoanKey());

            long startTime = System.currentTimeMillis();
            try {
                // 根据步骤判断是否是借款前 区分贷超 借款前异步处理
                if ("HH_PRELOAN".equals(strategyVo.getStep())) {
                    pool.submit(new Thread(() -> {
                        strategyProcessCandidateService.runStrategy(experiment, strategyVo);
                    }));
                } else {
                    strategyProcessCandidateService.runStrategy(experiment, strategyVo);
                }
                pointStrategyExp(JsonResultVo.SUCCESS, strategyVo, experiment, System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                LoggerProxy.error("runStrategyCandidateError", LOGGER, "type=" + strategyVo.getType() + ", expType=" + experiment.getExpCode(), e);
                pointStrategyExp(JsonResultVo.ERROR, strategyVo, experiment, System.currentTimeMillis() - startTime);
            }
        }
    }

    private boolean isRunFeatureProcessExp(StrategyVo strategyVo, String expCode) {
        try {
            FeatureExperimentResultVo byLoanKeyAndStep = featureExperimentResultMongoDao.getByLoanKeyAndStep(expCode,strategyVo.getLoanKey(), strategyVo.getStep());
            return byLoanKeyAndStep != null;
        } catch (Exception e) {
            LoggerProxy.error(LOGGER, "queryFeatureExperimentResultError", e);
            return false;
        }
    }

    /**
     * 埋点监控
     *
     * @param experiment StrategyExperiment
     * @param retCode int
     * @param vo StrategyVo
     */
    private void pointStrategyExp(int retCode, StrategyVo vo, ProcessExperiment experiment, long costTime) {
        if (null == vo) {
            return;
        }
        String sourceSystem = StringUtils.isBlank(vo.getSourceSystem()) ? "NONE" : vo.getSourceSystem();
        String step = StringUtils.isBlank(vo.getStep()) ? "NONE" : vo.getStep();
        String eventCode = StringUtils.isBlank(vo.getEventCode()) ? "NONE" : vo.getEventCode();
        String type = getType(vo.getStrategyType(), vo.getType(), sourceSystem);

        Map<String, String> tags = new HashMap<>();
        tags.put("type", type);
        tags.put("sourceSystem", sourceSystem);
        tags.put("step", step);
        tags.put("eventCode", eventCode);
        tags.put("retCode", "" + retCode);
        tags.put("expCode", experiment.getExpCode());
        tags.put("expType", experiment.getExpType());
        MetricsAPI.point(PointConstant.RM_POINT_RUN_STRATEGY_EXP, tags, costTime, true, MetricsOpType.timer);
    }

    /**
     * 目前未传递策略大类，所以临时处理
     *
     * @param strategyType String
     * @param type String
     * @param sourceSystem String
     * @return String
     */
    private static String getType(String strategyType, String type, String sourceSystem) {
        // 如果策略大类型有效，直接打埋点
        if (StringUtils.isNotBlank(strategyType)) {
            return strategyType;
        }
        // 现阶段人人贷策略大类就是type，好还业务需要特殊处理
        String tmpType = StringUtils.isBlank(type) ? "NONE" : type;
        if (!"HAO_HUAN".equals(sourceSystem)) {
            // 非好还业务，直接返回type
            return tmpType;
        }
        // 好还业务，去掉最后一个_后面的内容就是策略大类型
        int last = type.lastIndexOf("_");
        if (1 <= last) {
            tmpType = type.substring(0, last);
        }
        return tmpType;
    }
}
