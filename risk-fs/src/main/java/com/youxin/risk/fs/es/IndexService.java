package com.youxin.risk.fs.es;

import com.youxin.risk.fs.model.EsDataSubmitVo;
import com.youxin.risk.fs.model.EsStrategyResultVo;

import java.util.List;

/**
 * @InterfaceName IndexSerivce
 * @Description 索引
 * <AUTHOR>
 * @Date 2021/12/15 4:36 下午
 **/
public interface IndexService {
    /**
     * 创建索引
     * @param indexName
     */
    public void createIndex(String indexName);

    /**
     * 删除索引
     * @param indexName
     */
    public void deleteIndex(String indexName);

    /**
     * 判断索引是否存在
     * @param indexName
     * @return
     */
    public boolean indexExists(String indexName);

    /**
     * 插入esdatasubmit
     * @param data 要增加的数据:
     * @param id 数据ID:data里的id
     * @throws Exception
     */
    public void addDataSubmit(Object data,String id) throws Exception;

    /**
     * 插入esstrategyresult
     * @param data
     * @param id
     * @throws Exception
     */
    public void addStrategyResult(Object data,String id) throws Exception;

    /**
     * 查询
     * @param loanKey
     * @param userKey
     * @param eventCode
     * @param sourceSystem
     * @param step
     * @param limit
     * @param createTimeStart
     * @param createTimeEnd
     * @throws Exception
     */
    public List<EsDataSubmitVo> searchDataSubmit(String loanKey, String userKey, String eventCode,
                                                 String sourceSystem, String step, int limit,
                                                 String createTimeStart, String createTimeEnd) throws Exception;

    public List<EsStrategyResultVo> searchStrategy(String loanKey, String userKey, String eventCode, String sourceSystem, String step, int limit, String createTimeStart, String createTimeEnd) throws Exception;


    EsDataSubmitVo searchDataSubmitById(String id);

    EsStrategyResultVo searchStrategyById(String id);
}
