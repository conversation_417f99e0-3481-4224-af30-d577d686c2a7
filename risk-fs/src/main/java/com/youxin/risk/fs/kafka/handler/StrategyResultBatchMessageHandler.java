package com.youxin.risk.fs.kafka.handler;

import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.handler.impl.BaseKafKaMessageHandler;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.fs.service.DistinctEventFeatureService;
import com.youxin.risk.fs.service.StrategyResultService;

import javax.annotation.Resource;

import static com.youxin.risk.fs.utils.FeatureItemMonitorUtil.isInsertEs;

/**
 * 消费fs发来的策略结果并保存至hbase
 * <AUTHOR>
 */
public class StrategyResultBatchMessageHandler extends BaseKafKaMessageHandler {

    @Resource
    private StrategyResultService strategyResultService;
    @Resource
    private DistinctEventFeatureService distinctEventFeatureService;

    @Override
    protected void handler0(KafkaContext context) {
        StrategyVo strategyVo;
        try {
            strategyVo = context.getMessageObject(StrategyVo.class);
        } catch (Exception e) {
            LoggerProxy.error("parseBatchMessageError", logger, "parse message to strategyVo error, message=" + context.getMessage(), e);
            context.setTerminated(true);
            return;
        }
        try {
            /*strategyResultService.saveStrategyResultToHbase(strategyVo);*/
            // 移除跨事件保存逻辑，跨事件结果目前存特征数据查询对应hbase获取
            // distinctEventFeatureService.saveDistinctEventFeatureResultToHbase(strategyVo);
            if(isInsertEs(strategyVo.getUserKey())){
                strategyResultService.saveStrategyResultToES(strategyVo);
            }

        } catch (Exception e) {
            LoggerProxy.error(logger, "save strategy result to hbase es, batch, userKey={}, loanKey={}, step={}", strategyVo.getUserKey(), strategyVo.getLoanKey(), strategyVo.getStep());
        } finally {
            context.setRetCode(RetCodeEnum.SUCCESS);
        }
    }
}
