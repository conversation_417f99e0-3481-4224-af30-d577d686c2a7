package com.youxin.risk.admin.handler;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.vo.QueryAdminDataVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:spring/spring-config.xml"
})
public class PuDaoQueryDataHandlerTest {

    @Resource
    private PuDaoQueryDataHandler puDaoQueryDataHandlerUnderTest;

    @Test
    public void testGetQueryAdminDataVoByKey() {
        String queryAdminString = "{\"postData\":{\"sysParams\":{\"argumentList\":[{\"argueClass\":\"java.util.Date\",\"argueValue\":1680542618000}],\"hasArgument\":true,\"methodName\":\"selectByCollectCodesInMaster\",\"clazz\":\"com.youxin.risk.commons.dao.admin.AlertPolicyMapper\"}}}";

        QueryAdminDataVo queryAdminDataVo = JSON.parseObject(queryAdminString, QueryAdminDataVo.class);

        // Run the test
        final QueryAdminDataVo result = puDaoQueryDataHandlerUnderTest.getQueryAdminDataVoByKey(queryAdminDataVo);

        // Verify the results
        System.out.println(result);
    }
}
