package com.youxin.risk.admin.dao.admin;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.VariableConfigDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:spring/spring-config.xml"
})
public class AdminVariableConfigMapperTest {
    @Autowired
    private AdminVariableConfigMapper adminVariableConfigMapper;

    @Test
    public void selectNodesByVariables() {
        JSONObject params = new JSONObject();
        params.put("variables", new ArrayList<>(Arrays.asList("fw_last_fix_origin_login_tencent_exp",
                "fahai_cpwsPageNum")));

        params.put("isUsed", false);

        List<VariableConfigDO> variableConfigDOS = adminVariableConfigMapper.selectNodesByVariables(params);
        System.out.println(variableConfigDOS);
    }
}