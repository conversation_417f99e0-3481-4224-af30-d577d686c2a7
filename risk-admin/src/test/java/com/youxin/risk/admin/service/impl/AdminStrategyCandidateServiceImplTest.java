package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:spring/spring-config.xml"
})
@ActiveProfiles("test")
public class AdminStrategyCandidateServiceImplTest {

    @Autowired
    private AdminStrategyCandidateServiceImpl adminStrategyCandidateService;

    @Test
    public void submitNew() {
        String str = "{\"pythonVersion\":\"python3\",\"strategyVersion\":\"5073\",\"sourceSystem\":\"HAO_HUAN\"," +
                "\"developer\":\"hexiaobing\",\"remark\":\"测试\",\"list\":[{\"variableList\":[]," +
                "\"strategyCode\":\"import time\\nimport xmltodict\\nimport json\\n\\nvars = dict()\\n\\ndef getVar" +
                "(var_name):\\n    global vars\\n    return vars[var_name]\\n\\ndef getFeature(feature_name):\\n    " +
                "global vars\\n    return vars.get(\\\"standard\\\", dict())[feature_name]\\n\\ndef _FUN_copyValue" +
                "(left, key, info, da):\\n    if (da is not None):\\n        if (info.get('type') == 'FreeMap'):\\n  " +
                "          if(left.get(key) is None):\\n                left[key]=dict()\\n            else:\\n      " +
                "          left[key] = dict(left[key], **da)\\n        elif (isinstance(da, dict) or isinstance(da, " +
                "list)):\\n            if (info.get('children')):\\n                if (info.get('type') == 'Map')" +
                ":\\n                    tem = dict()\\n                    for p in info.get('children'):\\n        " +
                "                _FUN_copyValue(tem, p.get(\\\"name\\\"), p, da.get(p.get(\\\"name\\\")))\\n         " +
                "           if(left.get(key) is None):\\n                        left[key] = tem\\n                  " +
                "  else:\\n                        left[key] = dict(left[key], **tem)\\n                elif (info" +
                ".get('type') == 'List'):\\n                    thislist = left.get(key)\\n                    if " +
                "(thislist is None):\\n                        thislist = list()\\n                        left[key] " +
                "= thislist\\n                    if (isinstance(da, list)):\\n                        for m in " +
                "da:\\n                            tem = dict()\\n                            thislist.append(tem)\\n" +
                "                            for p in info.get('children'):\\n                                " +
                "_FUN_copyValue(tem, p.get(\\\"name\\\"), p, m.get(p.get(\\\"name\\\")))\\n                    " +
                "else:\\n                        tem = dict()\\n                        thislist.append(tem)\\n      " +
                "                  for p in info.get('children'):\\n                            _FUN_copyValue(tem, p" +
                ".get(\\\"name\\\"), p, da.get(p.get(\\\"name\\\")))\\n\\n\\n        elif (info.get('type') == " +
                "'String'):\\n            left[key] = str(da)\\n        elif (info.get('type') == 'Boolean'):\\n     " +
                "       left[key] = True if da is not None and str(da).lower() == 'true' else False\\n        elif " +
                "(info.get('type') == 'Number'):\\n            left[key] = float(da)\\n        elif (info.get('type')" +
                " == 'Integer'):\\n            left[key] = int(da)\\n        elif (info.get('type') == 'Date' and " +
                "isinstance(da, time.struct_time)):\\n            left[key] = da\\n\\ndef _FUN_getBack(back):\\n    " +
                "re = dict()\\n\\n    outInfoS = [{\\\"children\\\":[],\\\"name\\\":\\\"is_pass\\\"," +
                "\\\"type\\\":\\\"Boolean\\\"},{\\\"children\\\":[],\\\"name\\\":\\\"fraudScore\\\"," +
                "\\\"type\\\":\\\"Number\\\"}]\\n    for info in outInfoS:\\n        if info.get('type') == 'Map':\\n" +
                "            _FUN_copyValue(re, info.get(\\\"name\\\"), info,\\n                            back.get" +
                "(info.get(\\\"name\\\"), dict()))\\n        elif info.get('type') == 'FreeMap':\\n            " +
                "re[info.get(\\\"name\\\")]= back.get(info.get(\\\"name\\\"), dict())\\n        elif info.get('type')" +
                " == 'List':\\n            _FUN_copyValue(re, info.get(\\\"name\\\"), info,\\n                       " +
                "     back.get(info.get(\\\"name\\\"), list()))\\n        else:\\n            _FUN_copyValue(re, info" +
                ".get(\\\"name\\\"), info,\\n                            back.get(info.get(\\\"name\\\"), 0))\\n    " +
                "return re\\n\\ndef removeNoValueKey(data):\\n    try:\\n        for k in dict(data).keys():\\n      " +
                "      if data[k] == \\\"\\\":\\n                del data[k]\\n    except Exception as e:\\n        " +
                "print(e)\\n\\ndef analysis(xml): \\n    global vars\\n    if(isXml(xml)): \\n        xmlJson = " +
                "xmltodict.parse(xml)\\n    else:\\n        try:\\n            xmlJson = json.loads(xml)\\n        " +
                "except:\\n            xmlJson = xml\\n    data = xmlJson.get(\\\"feature\\\", dict()) \\n    vars = " +
                "data.get(\\\"vars\\\", dict()) \\n    back = A_002()\\n    if not isinstance(back, dict):\\n        " +
                "raise Exception(\\\"策略返回类型必须为dict类型！\\\")\\n    return _FUN_getBack(back)\\n \\ndef isXml(xml): \\n " +
                "   try: \\n        xmltodict.parse(xml) \\n        return True \\n    except: \\n        return " +
                "False\\n\\ndef A_002():\\n    result = {}\\n    # 代码\\n    \\n    return result\"," +
                "\"strategyNodeId\":\"A_002\"},{\"variableList\":[],\"strategyCode\":\"import time\\nimport " +
                "xmltodict\\nimport json\\n\\nvars = dict()\\n\\ndef getVar(var_name):\\n    global vars\\n    return" +
                " vars[var_name]\\n\\ndef getFeature(feature_name):\\n    global vars\\n    return vars.get" +
                "(\\\"standard\\\", dict())[feature_name]\\n\\ndef _FUN_copyValue(left, key, info, da):\\n    if (da " +
                "is not None):\\n        if (info.get('type') == 'FreeMap'):\\n            if(left.get(key) is None)" +
                ":\\n                left[key]=dict()\\n            else:\\n                left[key] = dict" +
                "(left[key], **da)\\n        elif (isinstance(da, dict) or isinstance(da, list)):\\n            if " +
                "(info.get('children')):\\n                if (info.get('type') == 'Map'):\\n                    tem " +
                "= dict()\\n                    for p in info.get('children'):\\n                        " +
                "_FUN_copyValue(tem, p.get(\\\"name\\\"), p, da.get(p.get(\\\"name\\\")))\\n                    if" +
                "(left.get(key) is None):\\n                        left[key] = tem\\n                    else:\\n   " +
                "                     left[key] = dict(left[key], **tem)\\n                elif (info.get('type') == " +
                "'List'):\\n                    thislist = left.get(key)\\n                    if (thislist is None)" +
                ":\\n                        thislist = list()\\n                        left[key] = thislist\\n     " +
                "               if (isinstance(da, list)):\\n                        for m in da:\\n                 " +
                "           tem = dict()\\n                            thislist.append(tem)\\n                       " +
                "     for p in info.get('children'):\\n                                _FUN_copyValue(tem, p.get" +
                "(\\\"name\\\"), p, m.get(p.get(\\\"name\\\")))\\n                    else:\\n                       " +
                " tem = dict()\\n                        thislist.append(tem)\\n                        for p in info" +
                ".get('children'):\\n                            _FUN_copyValue(tem, p.get(\\\"name\\\"), p, da.get(p" +
                ".get(\\\"name\\\")))\\n\\n\\n        elif (info.get('type') == 'String'):\\n            left[key] = " +
                "str(da)\\n        elif (info.get('type') == 'Boolean'):\\n            left[key] = True if da is not " +
                "None and str(da).lower() == 'true' else False\\n        elif (info.get('type') == 'Number'):\\n     " +
                "       left[key] = float(da)\\n        elif (info.get('type') == 'Integer'):\\n            left[key]" +
                " = int(da)\\n        elif (info.get('type') == 'Date' and isinstance(da, time.struct_time)):\\n     " +
                "       left[key] = da\\n\\ndef _FUN_getBack(back):\\n    re = dict()\\n\\n    outInfoS = " +
                "[{\\\"children\\\":[],\\\"name\\\":\\\"is_pass\\\",\\\"type\\\":\\\"Boolean\\\"}," +
                "{\\\"children\\\":[],\\\"name\\\":\\\"fraudScore\\\",\\\"type\\\":\\\"Number\\\"}]\\n    for info in" +
                " outInfoS:\\n        if info.get('type') == 'Map':\\n            _FUN_copyValue(re, info.get" +
                "(\\\"name\\\"), info,\\n                            back.get(info.get(\\\"name\\\"), dict()))\\n    " +
                "    elif info.get('type') == 'FreeMap':\\n            re[info.get(\\\"name\\\")]= back.get(info.get" +
                "(\\\"name\\\"), dict())\\n        elif info.get('type') == 'List':\\n            _FUN_copyValue(re, " +
                "info.get(\\\"name\\\"), info,\\n                            back.get(info.get(\\\"name\\\"), list())" +
                ")\\n        else:\\n            _FUN_copyValue(re, info.get(\\\"name\\\"), info,\\n                 " +
                "           back.get(info.get(\\\"name\\\"), 0))\\n    return re\\n\\ndef removeNoValueKey(data):\\n " +
                "   try:\\n        for k in dict(data).keys():\\n            if data[k] == \\\"\\\":\\n              " +
                "  del data[k]\\n    except Exception as e:\\n        print(e)\\n\\ndef analysis(xml): \\n    global " +
                "vars\\n    if(isXml(xml)): \\n        xmlJson = xmltodict.parse(xml)\\n    else:\\n        try:\\n  " +
                "          xmlJson = json.loads(xml)\\n        except:\\n            xmlJson = xml\\n    data = " +
                "xmlJson.get(\\\"feature\\\", dict()) \\n    vars = data.get(\\\"vars\\\", dict()) \\n    back = " +
                "A_001()\\n    if not isinstance(back, dict):\\n        raise Exception(\\\"策略返回类型必须为dict类型！\\\")\\n " +
                "   return _FUN_getBack(back)\\n \\ndef isXml(xml): \\n    try: \\n        xmltodict.parse(xml) \\n  " +
                "      return True \\n    except: \\n        return False\\n\\ndef A_001():\\n    result = {}\\n    #" +
                " 代码\\n    import random\\n    \\n    lis1 = [1, 2, 3]\\n    lis2 = [random.random(), random.randint" +
                "(0, 5), random.randrange(1, 16)]\\n    \\n    result['fraudScore'] = dict(zip(lis1, lis2)).get" +
                "(random.randint(1, 3))\\n    return result\",\"strategyNodeId\":\"A_001\"}],\"projectId\":4," +
                "\"strategyType\":\"-999\"}";

        adminStrategyCandidateService.submitNew(JSON.parseObject(str));
    }
}