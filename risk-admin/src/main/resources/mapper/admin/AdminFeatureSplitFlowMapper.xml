<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminFeatureSplitFlowMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminFeatureSplitFlow">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="split_flow_code" property="splitFlowCode" jdbcType="VARCHAR"/>
        <result column="split_flow_name" property="splitFlowName" jdbcType="VARCHAR"/>
        <result column="feature_submit_code" property="featureSubmitCode" jdbcType="VARCHAR"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="step" property="step" jdbcType="VARCHAR"/>
        <result column="split_flow_percent" property="splitFlowPercent" jdbcType="INTEGER"/>
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
    id,split_flow_code,split_flow_name,feature_submit_code,source_system,step,split_flow_percent,begin_time,end_time,status,operator,create_time,update_time
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminFeatureSplitFlow">
    insert into admin_feature_split_flow (
      split_flow_code,
      split_flow_name,
      feature_submit_code,
      source_system,
      step,
      split_flow_percent,
      begin_time,
      end_time,
      status,
      operator
    ) values (
      #{splitFlowCode},
      #{splitFlowName},
      #{featureSubmitCode},
      #{sourceSystem},
      #{step},
      #{splitFlowPercent},
      #{beginTime},
      #{endTime},
      #{status},
      #{operator}
    )
  </insert>

    <update id="update" parameterType="com.youxin.risk.admin.model.AdminFeatureSplitFlow">
        update admin_feature_split_flow
        <set>
            <if test="splitFlowName != null">
                split_flow_name = #{splitFlowName},
            </if>
            <if test="featureSubmitCode != null">
                feature_submit_code = #{featureSubmitCode},
            </if>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem},
            </if>
            <if test="step != null">
                step = #{step},
            </if>
            <if test="splitFlowPercent != null">
                split_flow_percent = #{splitFlowPercent},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        delete from admin_feature_split_flow
        where id = #{id}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from admin_feature_split_flow
        where id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_feature_split_flow
        order by id desc
    </select>

    <select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
          count(1)
        from admin_feature_split_flow
        <where>
            <if test="featureSubmitCode != null">
                and feature_submit_code = #{featureSubmitCode}
            </if>
            <if test="sourceSystem != null">
                and source_system = #{sourceSystem}
            </if>
            <if test="step != null">
                and step = #{step}
            </if>
            <if test="splitFlowCode != null">
                and split_flow_code = #{splitFlowCode}
            </if>
            <if test="splitFlowName != null">
                and split_flow_name like CONCAT('%',#{splitFlowName},'%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="operator != null">
                and operator like CONCAT('%',#{operator},'%')
            </if>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from admin_feature_split_flow
        <where>
            <if test="featureSubmitCode != null">
                and feature_submit_code = #{featureSubmitCode}
            </if>
            <if test="sourceSystem != null">
                and source_system = #{sourceSystem}
            </if>
            <if test="step != null">
                and step = #{step}
            </if>
            <if test="splitFlowCode != null">
                and split_flow_code = #{splitFlowCode}
            </if>
            <if test="splitFlowName != null">
                and split_flow_name like CONCAT('%',#{splitFlowName},'%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="operator != null">
                and operator like CONCAT('%',#{operator},'%')
            </if>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <select id="selectForEnable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from admin_feature_split_flow
        where status = "DISABLE"
        and begin_time &lt;= now()
        and end_time &gt; now()
    </select>

    <select id="selectForDisable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from admin_feature_split_flow
        where status = "ENABLE"
        and end_time &lt;= now()
    </select>
    
    <update id="batchUpdateStatus">
        update admin_feature_split_flow
        set status = #{status}
        where id in
        <foreach collection="idList" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>