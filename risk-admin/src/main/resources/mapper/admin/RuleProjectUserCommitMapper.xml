<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.RuleProjectUserCommitMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.ruleEngine.RuleProjectUserCommit">
        <!--@Table admin_strategy_user_commit-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="historyCurrentId" column="history_current_id" jdbcType="INTEGER"/>
        <result property="historyMasterId" column="history_master_id" jdbcType="INTEGER"/>
        <result property="userName" column="username" jdbcType="VARCHAR"/>
        <result property="isDel" column="is_del" jdbcType="INTEGER"/>
        <result property="isUpdate" column="is_update" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
        id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="historyCurrentId != null">
                and history_current_id = #{historyCurrentId}
            </if>
            <if test="historyMasterId != null">
                and history_master_id = #{historyMasterId}
            </if>
            <if test="userName != null and userName != ''">
                and userName = #{userName}
            </if>
            <if test="isDel != null">
                and is_del = #{isDel}
            </if>
            <if test="isUpdate != null">
                and is_update = #{isUpdate}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>
    
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from admin_strategy_user_commit
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="historyCurrentId != null">
                and history_current_id = #{historyCurrentId}
            </if>
            <if test="historyMasterId != null">
                and history_master_id = #{historyMasterId}
            </if>
            <if test="userName != null and userName != ''">
                and userName = #{userName}
            </if>
            <if test="isDel != null">
                and is_del = #{isDel}
            </if>
            <if test="isUpdate != null">
                and is_update = #{isUpdate}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into admin_strategy_user_commit(project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time)
        values (#{projectId},#{projectName},#{fileName},#{historyCurrentId},#{historyMasterId},#{userName},#{isDel},#{isUpdate},#{updateTime},#{createTime})
    </insert>

    <!-- 插入带有主键的数据 -->
    <insert id="insertWithId" keyProperty="id" useGeneratedKeys="true">
        insert into admin_strategy_user_commit(id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time)
        values (#{id},#{projectId},#{projectName},#{fileName},#{historyCurrentId},#{historyMasterId},#{userName},#{isDel},#{isUpdate},#{updateTime},#{createTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        admin_strategy_user_commit(project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectId},#{entity.projectName},#{entity.fileName},#{entity.historyCurrentId},#{entity.historyMasterId},#{entity.userName},#{entity.isDel},#{entity.isUpdate},#{entity.updateTime},#{entity.createTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        admin_strategy_user_commit(project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectId},#{entity.projectName},#{entity.fileName},#{entity.historyCurrentId},#{entity.historyMasterId},#{entity.userName},#{entity.isDel},#{entity.isUpdate},#{entity.updateTime},#{entity.createTime})
        </foreach>
        on duplicate key update
        project_id = values(project_id),
        project_name = values(project_name),
        file_name = values(file_name),
        history_current_id = values(history_current_id),
        history_master_id = values(history_master_id),
        username = values(username),
        is_del = values(is_del),
        is_update = values(is_update),
        update_time = values(update_time),
        create_time = values(create_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update admin_strategy_user_commit
        <set>
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="projectName != null and projectName != ''">
                project_name = #{projectName},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="historyCurrentId != null">
                history_current_id = #{historyCurrentId},
            </if>
            <if test="historyMasterId != null">
                history_master_id = #{historyMasterId},
            </if>
            <if test="userName != null and userName != ''">
                username = #{userName},
            </if>
            <if test="isDel != null">
                is_del = #{isDel},
            </if>
            <if test="isUpdate != null">
                is_update = #{isUpdate},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from admin_strategy_user_commit
        where id = #{id}
    </delete>


    <select id="queryByProjectIdAndUserName" resultMap="BaseResultMap">
        select
        id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        <where>
            is_del = 0
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
            <if test="userName != null and userName != ''">
                and username = #{userName}
            </if>
        </where>
    </select>

    <update id="markAsDeletedByNodeName">
        update admin_strategy_user_commit
        set is_del = 1
        where file_name = #{fileName} and is_del = 0
    </update>

    <select id="queryByProjectIdAndUserNameAndFileName" resultMap="BaseResultMap">
        select
            id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        where project_id = #{projectId} and file_name = #{fileName} and username = #{userName} and is_del = 0
        order by update_time desc
        limit 1
    </select>

    <select id="queryByProjectIdAndFileName" resultMap="BaseResultMap">
        select
            id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        where project_id = #{projectId} and file_name = #{fileName} and username = #{userName} and is_del = 1
        order by update_time desc
        limit 1
    </select>

    <select id="queryByProjectIdAndNodeName" resultMap="BaseResultMap">
        select
        id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        <where>
            is_del = 0
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="queryEarliestByProjectId" resultMap="BaseResultMap">
        select
        id,project_id,project_name,file_name,history_current_id,history_master_id,username,is_del,is_update,update_time,create_time
        from admin_strategy_user_commit
        where is_del = 0 and project_id = #{projectId}
        order by id limit 1
    </select>
</mapper>

