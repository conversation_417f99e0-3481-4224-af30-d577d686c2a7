<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.admin.SubscriptionRecordMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.subscription.SubscriptionRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="strategy_type" property="strategyType" jdbcType="VARCHAR"/>
        <result column="monitor_id" property="monitorId" jdbcType="VARCHAR"/>
        <result column="frequency_minutes" property="frequencyMinutes" jdbcType="INTEGER"/>
        <result column="total_pushes" property="totalPushes" jdbcType="TINYINT"/>
        <result column="sent_pushes" property="sentPushes" jdbcType="TINYINT"/>
        <result column="is_active" property="isActive" jdbcType="TINYINT"/>
        <result column="last_notified_at" property="lastNotifiedAt" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, strategy_type, monitor_id, frequency_minutes, total_pushes, sent_pushes, 
        is_active, last_notified_at, create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List"/>
        FROM t_subscription_record
        WHERE id = #{id}
    </select>

    <select id="findPendingSubscriptions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_subscription_record 
        WHERE is_active = 1 
        AND sent_pushes &lt; total_pushes
        AND (last_notified_at IS NULL OR last_notified_at &lt;= NOW() - INTERVAL frequency_minutes MINUTE)
        ORDER BY create_time ASC
    </select>

    <select id="findByUserAndStrategyAndMonitor" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_subscription_record
        WHERE user_id = #{userId}
        AND strategy_type = #{strategyType}
        AND monitor_id = #{monitorId}
        LIMIT 1
    </select>

    <select id="findByUserAndStrategy" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_subscription_record
        WHERE user_id = #{userId}
        AND strategy_type = #{strategyType}
        AND is_active = 1
        ORDER BY create_time ASC
    </select>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.subscription.SubscriptionRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_subscription_record (
            user_id, strategy_type, monitor_id, frequency_minutes, total_pushes, sent_pushes, 
            is_active, last_notified_at, create_time, update_time
        ) VALUES (
            #{userId}, #{strategyType}, #{monitorId}, #{frequencyMinutes}, #{totalPushes}, #{sentPushes},
            #{isActive}, #{lastNotifiedAt}, #{createTime}, #{updateTime}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_subscription_record (
            user_id, strategy_type, monitor_id, frequency_minutes, total_pushes, sent_pushes, 
            is_active, last_notified_at, create_time, update_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.userId}, #{record.strategyType}, #{record.monitorId}, #{record.frequencyMinutes}, 
             #{record.totalPushes}, #{record.sentPushes}, #{record.isActive}, #{record.lastNotifiedAt}, 
             #{record.createTime}, #{record.updateTime})
        </foreach>
    </insert>

    <update id="update" parameterType="com.youxin.risk.admin.model.subscription.SubscriptionRecord">
        UPDATE t_subscription_record
        SET user_id = #{userId},
            strategy_type = #{strategyType},
            monitor_id = #{monitorId},
            frequency_minutes = #{frequencyMinutes},
            total_pushes = #{totalPushes},
            sent_pushes = #{sentPushes},
            is_active = #{isActive},
            last_notified_at = #{lastNotifiedAt},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <update id="batchUpdatePushStatus">
        UPDATE t_subscription_record
        SET 
            last_notified_at = CURRENT_TIMESTAMP,
            sent_pushes = sent_pushes + 1,
            update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM t_subscription_record WHERE id = #{id}
    </delete>

</mapper>
