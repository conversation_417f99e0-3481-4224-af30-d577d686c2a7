<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.channel.AdminChannelRequestAgencyMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminChannelRequestAgency">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="request_agency_id" jdbcType="VARCHAR" property="requestAgencyId" />
    <result column="out_request_id" jdbcType="VARCHAR" property="outRequestId" />
    <result column="agency_code" jdbcType="VARCHAR" property="agencyCode" />
    <result column="request_agency_message" jdbcType="VARCHAR" property="requestAgencyMessage" />
    <result column="dp_jodid" jdbcType="VARCHAR" property="dpJodid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, request_id, request_agency_id, out_request_id, agency_code, request_agency_message, dp_jodid, create_time, update_time
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from channel_request_agency
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <select id="selectChannelRequestAgencyTotal" parameterType="com.youxin.risk.admin.vo.AdminChannelRequestAgencyVo" resultType="int">
  	select 
  	count(1)
  	from channel_request_agency
  	<where>
  	<if test="requestId != null">
  		and request_id = #{requestId}
  	</if>
  	<if test="requestAgencyId != null">
  		and request_agency_id = #{requestAgencyId}
  	</if>
  	<if test="outRequestId != null">
  		and out_request_id = #{outRequestId}
  	</if>
  	<if test="agencyCode != null">
  		and agency_code = #{agencyCode}
  	</if>
  	<if test="dpJodid != null">
  		and dp_jodid = #{dpJodid}
  	</if>
  	<if test="startTime != null and endTime != null">
  		and create_time &gt; #{startTime} and create_time &lt; #{endTime}
  	</if>
  	</where>
  	order by id desc
  </select>
  <select id="selectAllChannelRequestAgency" parameterType="com.youxin.risk.admin.vo.AdminChannelRequestAgencyVo" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
  	from channel_request_agency
  	<where>
  	<if test="requestId != null">
  		and request_id = #{requestId}
  	</if>
  	<if test="requestAgencyId != null">
  		and request_agency_id = #{requestAgencyId}
  	</if>
  	<if test="outRequestId != null">
  		and out_request_id = #{outRequestId}
  	</if>
  	<if test="agencyCode != null">
  		and agency_code = #{agencyCode}
  	</if>
  	<if test="dpJodid != null">
  		and dp_jodid = #{dpJodid}
  	</if>
  	<if test="startTime != null and endTime != null">
  		and create_time &gt; #{startTime} and create_time &lt; #{endTime}
  	</if>
  	</where>
  	order by id desc
  	limit ${startIndex},${pageSize}
  </select>
</mapper>