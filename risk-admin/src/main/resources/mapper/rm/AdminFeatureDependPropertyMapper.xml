<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.rm.AdminFeatureDependPropertyMapper" >
  <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminFeatureDependProperty" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="submit_detail_id" property="submitDetailId" jdbcType="BIGINT" />
    <result column="data_type" property="dataType" jdbcType="VARCHAR" />
    <result column="data_name" property="dataName" jdbcType="VARCHAR" />
    <result column="data_code" property="dataCode" jdbcType="VARCHAR" />
    <result column="data_join_path" property="dataJoinPath" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, submit_detail_id, data_type, data_name, data_code, data_join_path
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from rm_feature_depend_property
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="queryFeatureDependpropertyBySubmitDetailId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rm_feature_depend_property
    where submit_detail_id
      in
        <foreach collection="submitDetailIdList" item="submitDetailId" index="index" open="(" separator="," close=")">
            #{submitDetailId}
        </foreach>
  </select>
  <select id="queryFeatureDependproperty" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rm_feature_depend_property
    where submit_detail_id in
    (select max(submit_detail_id) from rm_feature  where pre_type=3 and del_flag=0
    	and source_system = #{sourceSystem}
    	and feature_name = #{featureName}
    	)
  </select>

    <select id="getDrDependFeatureList" resultType="java.lang.Long">
        SELECT
        ffd.submit_detail_id AS submitDetailId
        FROM
        rm_feature_submit_detail ff
        INNER JOIN rm_feature_depend_property ffd ON ffd.data_join_path = ff.node_path
        WHERE data_type = 'drFeature' and ff.id in
        <foreach collection="currentParentIdList" item="currentParentId" open="(" close=")" separator=",">
            #{currentParentId}
        </foreach>
        AND ffd.submit_detail_id in (
        SELECT MAX(submit_detail_id)
        FROM rm_feature
        WHERE del_flag = 0 AND submit_detail_id != 0
        AND source_system = #{sourceSystem}
        AND apply_step = #{applyStep}
        GROUP by feature_name,apply_step
        )
    </select>
  <select id="getMaxLevelByFeatureAndStep" resultType="java.lang.Integer">
    SELECT
        MAX(ff.step_level)
        FROM
        rm_feature_submit_detail ff
        INNER JOIN rm_feature_depend_property ffd ON ffd.data_join_path = ff.node_path
        WHERE ffd.data_type = 'drFeature' and ffd.submit_detail_id = #{submitDetailId}
        AND ff.id in (
        SELECT MAX(submit_detail_id)
        FROM rm_feature
        WHERE del_flag = 0 AND submit_detail_id != 0
        AND source_system = #{sourceSystem}
        AND apply_step = #{applyStep}
        AND feature_name != #{featureName}
        GROUP by feature_name,apply_step
      )
  </select>
  <select id="getMaxLevelByFeatureAndStepForAdd" resultType="java.lang.Integer">
    SELECT
      MAX(ff.step_level)
    FROM
      rm_feature_submit_detail ff
        INNER JOIN rm_feature_depend_property ffd ON ffd.data_join_path = ff.node_path
    WHERE ffd.data_type = 'drFeature' and ffd.submit_detail_id = #{submitDetailId}
      AND ff.id in (
      SELECT MAX(submit_detail_id)
      FROM rm_feature
      WHERE del_flag = 0 AND submit_detail_id != 0
      AND source_system = #{sourceSystem}
      AND apply_step = #{applyStep}
        GROUP by feature_name,apply_step
      )
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from rm_feature_depend_property
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminFeatureDependProperty" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rm_feature_depend_property (submit_detail_id, data_type, data_name,
      data_code, data_join_path)
    values (#{submitDetailId,jdbcType=BIGINT}, #{dataType,jdbcType=VARCHAR}, #{dataName,jdbcType=VARCHAR},
      #{dataCode,jdbcType=VARCHAR}, #{dataJoinPath,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.youxin.risk.admin.model.AdminFeatureDependProperty" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rm_feature_depend_property
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="submitDetailId != null" >
        submit_detail_id,
      </if>
      <if test="dataType != null" >
        data_type,
      </if>
      <if test="dataName != null" >
        data_name,
      </if>
      <if test="dataCode != null" >
        data_code,
      </if>
      <if test="dataJoinPath != null" >
        data_join_path,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="submitDetailId != null" >
        #{submitDetailId,jdbcType=BIGINT},
      </if>
      <if test="dataType != null" >
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="dataName != null" >
        #{dataName,jdbcType=VARCHAR},
      </if>
      <if test="dataCode != null" >
        #{dataCode,jdbcType=VARCHAR},
      </if>
      <if test="dataJoinPath != null" >
        #{dataJoinPath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youxin.risk.admin.model.AdminFeatureDependProperty" >
    update rm_feature_depend_property
    <set >
      <if test="submitDetailId != null" >
        submit_detail_id = #{submitDetailId,jdbcType=BIGINT},
      </if>
      <if test="dataType != null" >
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="dataName != null" >
        data_name = #{dataName,jdbcType=VARCHAR},
      </if>
      <if test="dataCode != null" >
        data_code = #{dataCode,jdbcType=VARCHAR},
      </if>
      <if test="dataJoinPath != null" >
        data_join_path = #{dataJoinPath,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youxin.risk.admin.model.AdminFeatureDependProperty" >
    update rm_feature_depend_property
    set submit_detail_id = #{submitDetailId,jdbcType=BIGINT},
      data_type = #{dataType,jdbcType=VARCHAR},
      data_name = #{dataName,jdbcType=VARCHAR},
      data_code = #{dataCode,jdbcType=VARCHAR},
      data_join_path = #{dataJoinPath,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteBySubmitDetailId" parameterType="java.lang.Integer">
    DELETE FROM rm_feature_depend_property
    WHERE submit_detail_id = #{submitDetailId}
  </delete>
</mapper>