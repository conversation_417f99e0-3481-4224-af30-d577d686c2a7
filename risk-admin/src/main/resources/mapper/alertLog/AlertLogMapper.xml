<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.alertLog.AlertLogMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.alert.AlertLog" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="alert_id" property="alertId" jdbcType="VARCHAR" />
        <result column="application_name" property="applicationName" jdbcType="VARCHAR" />
        <result column="policy_name" property="policyName" jdbcType="VARCHAR" />
        <result column="title" property="title" jdbcType="VARCHAR" />
        <result column="issue_type" property="issueType" jdbcType="VARCHAR" />
        <result column="issue_desc" property="issueDesc" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="message" property="message" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, alert_id, application_name, policy_name, title, issue_type, issue_desc, status, 
        message, create_time, update_time, update_user
    </sql>

    <select id="selectList"  resultMap="BaseResultMap" parameterType="com.youxin.risk.admin.model.alert.AlertLog">
        select
        <include refid="Base_Column_List" />
        from alert_log
        <where>
            <if test="applicationName != null" >
                and application_name like CONCAT('%',#{applicationName},'%')
            </if>
            <if test="title != null" >
                and title like CONCAT('%',#{title},'%')
            </if>
            <if test="status != null" >
                and status = #{status}
            </if>
            <if test="startTime != null and endTime != null" >
                and create_time &gt; #{startTime} and create_time &lt; #{endTime}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectByExample"  resultMap="BaseResultMap" parameterType="com.youxin.risk.admin.model.alert.AlertLog">
        select
        <include refid="Base_Column_List" />
        from alert_log
        <where>
            <if test="id != null" >
                and id = #{id}
            </if><if test="alertId != null" >
                and alert_id = #{alertId}
            </if>
            <if test="applicationName != null" >
                and application_name = #{applicationName}
            </if>
            <if test="title != null" >
                and title like = #{title}
            </if>
            <if test="status != null" >
                and status = #{status}
            </if>
            <if test="startTime != null and endTime != null" >
                and create_time &gt; #{startTime} and create_time &lt; #{endTime}
            </if>
        </where>
        order by id desc
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from alert_log
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
        delete from alert_log
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.alert.AlertLog" >
        insert into alert_log (id, alert_id, application_name, 
            policy_name, title, issue_type, 
            issue_desc, status, message, 
            create_time, update_time, update_user
            )
        values (#{id,jdbcType=BIGINT}, #{alertId,jdbcType=VARCHAR}, #{applicationName,jdbcType=VARCHAR}, 
            #{policyName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{issueType,jdbcType=VARCHAR}, 
            #{issueDesc,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR}, 
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertSelective" parameterType="com.youxin.risk.admin.model.alert.AlertLog" >
        insert into alert_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="alertId != null" >
                alert_id,
            </if>
            <if test="applicationName != null" >
                application_name,
            </if>
            <if test="policyName != null" >
                policy_name,
            </if>
            <if test="title != null" >
                title,
            </if>
            <if test="issueType != null" >
                issue_type,
            </if>
            <if test="issueDesc != null" >
                issue_desc,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="message != null" >
                message,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="updateUser != null" >
                update_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="alertId != null" >
                #{alertId,jdbcType=VARCHAR},
            </if>
            <if test="applicationName != null" >
                #{applicationName,jdbcType=VARCHAR},
            </if>
            <if test="policyName != null" >
                #{policyName,jdbcType=VARCHAR},
            </if>
            <if test="title != null" >
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="issueType != null" >
                #{issueType,jdbcType=VARCHAR},
            </if>
            <if test="issueDesc != null" >
                #{issueDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="message != null" >
                #{message,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null" >
                #{updateUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.youxin.risk.admin.model.alert.AlertLog" >
        update alert_log
        <set >
            <if test="alertId != null" >
                alert_id = #{alertId,jdbcType=VARCHAR},
            </if>
            <if test="applicationName != null" >
                application_name = #{applicationName,jdbcType=VARCHAR},
            </if>
            <if test="policyName != null" >
                policy_name = #{policyName,jdbcType=VARCHAR},
            </if>
            <if test="title != null" >
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="issueType != null" >
                issue_type = #{issueType,jdbcType=VARCHAR},
            </if>
            <if test="issueDesc != null" >
                issue_desc = #{issueDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="message != null" >
                message = #{message,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <!--<if test="updateTime != null" >-->
                <!--update_time = #{updateTime,jdbcType=TIMESTAMP},-->
            <!--</if>-->
            <if test="updateUser != null" >
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.youxin.risk.admin.model.alert.AlertLog" >
        update alert_log
        set alert_id = #{alertId,jdbcType=VARCHAR},
            application_name = #{applicationName,jdbcType=VARCHAR},
            policy_name = #{policyName,jdbcType=VARCHAR},
            title = #{title,jdbcType=VARCHAR},
            issue_type = #{issueType,jdbcType=VARCHAR},
            issue_desc = #{issueDesc,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            message = #{message,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
--             ,
--             update_user = #{updateUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>