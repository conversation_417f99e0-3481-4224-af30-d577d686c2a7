<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:cache="http://www.springframework.org/schema/cache" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/aop
     http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd 
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
     http://www.springframework.org/schema/cache
     http://www.springframework.org/schema/cache/spring-cache-4.0.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.youxin" use-default-filters="false">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Repository"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Component"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:exclude-filter type="assignable" expression="com.youxin.risk.admin.controller.SwaggerConfig"/>
    </context:component-scan>

    <util:constant id="appName" static-field="com.youxin.risk.commons.constants.AppName.risk_admin"/>
    <util:constant id="kafkaAppName" static-field="com.youxin.risk.kafka.constants.AppName.risk_admin"/>

    <bean class="com.youxin.risk.commons.utils.GlobalUtil">
        <property name="appName" ref="appName" />
    </bean>

    <bean class="com.youxin.risk.commons.utils.ContextUtil"/>

    <util:properties id="configProperties" location="classpath:conf/*.properties"/>
    <context:property-placeholder location="classpath:conf/*.properties"/>

    <import resource="spring-aop.xml"/>
    <import resource="spring-datasource.xml"/>
    <import resource="spring-service.xml"/>
    <import resource="spring-redis.xml"/>
    <import resource="spring-mongo.xml"/>
    <import resource="spring-scheduler.xml"/>
    <import resource="spring-kafka.xml"/>
    <import resource="spring-xxl-job.xml"/>
</beans>
