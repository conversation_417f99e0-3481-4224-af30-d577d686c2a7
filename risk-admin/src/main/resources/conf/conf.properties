spring.profiles.active=@activeEnv@
server.port=8081
#登录域验证相关配置
admin_login_domain=${admin.login.domain}
admin_login_url=${admin.login.url}

#登录SSO配置
admin_login_sso_server=${admin.login.sso.server}
admin_login_self_server=${admin.login.self.server}

admin_login_sso_private_key=${admin.login.sso.private.key}
#SSO JWT进行签名校验和内容解析 配置
admin_login_sso_encryption_key=${admin.login.sso.encryption.key}
admin_login_sso_signing_key=${admin.login.sso.signing.key}


admin_token_expire_time_seconds=${admin.token.expire.time.seconds}
alert.collect.config.url=${alert.collect.config.url}
#alert告警
alert.base.url=${alert.base.url}

#获取权限、角色配置
admin_permission_host=${admin.permission.host}
perm_public_list=${perm.public.list}
role_public_list=${role.public.list}

cp_host=${cp.host}
cp_auth_host=${cp.auth.host}

verify.manage.baseurl=${verify.manage.baseurl}

dp.trigger.callback.url=${dp.trigger.callback.url}
ra.base.url=${ra.base.url}
rrd.service.url=${rrd.service.url}
gw.service.url=${gw.service.url}
gateway.url=${gateway.url}

pudao.admin.url=${pudao.url}
heika.admin.url=${heika.url}
rongdan.admin.url=${rongdan.url}
weicai.admin.url=${weicai.url}

mongo.host=${mongo.host}
mongo.username=${mongo.username}
mongo.password=${mongo.password}
mongo.database=${mongo.database}

mongo.risk.host=${mongo.risk.host}
mongo.risk.username=${mongo.risk.username}
mongo.risk.password=${mongo.risk.password}
mongo.risk.database=${mongo.risk.database}

mongo.sharding.host=${mongo.sharding.host}
mongo.sharding.username=${mongo.sharding.username}
mongo.sharding.password=${mongo.sharding.password}
mongo.sharding.database=${mongo.sharding.database}

admin.black.server.host=${admin.black.server.host}
admin.login.sso.server.excute.host=${admin.login.sso.server.excute.host}
admin.special.check.url=${admin.special.check.url}

cp.datasource.username=${cp.datasource.username}
cp.datasource.url=${cp.datasource.url}
cp.datasource.pwd=${cp.datasource.pwd}

mail.from=${mail.from}
mail.default-encoding=UTF-8
mail.host=${mail.host}
mail.port=${mail.port}
mail.username=${mail.username}
mail.password=${mail.password}
mail.properties.mail.smtp.auth=${mail.properties.mail.smtp.auth}
mail.properties.mail.smtp.timeout=${mail.properties.mail.smtp.timeout}
mail.properties.mail.debug=${mail.properties.mail.debug}

fs.service.url=${fs.service.url}
heika.fs.service.url=${heika.fs.service.url}
pudao.fs.service.url=${pudao.fs.service.url}
rongdan.fs.service.url=${rongdan.fs.service.url}
process.engine=${process.engine}
pudao.engine.url=${pudao.engine.url}
heika.engine.url=${heika.engine.url}
rongdan.engine.url=${rongdan.engine.url}
mongo.riskExp.credentials=${mongo.riskExp.credentials}
mongo.riskExp.database=${mongo.riskExp.database}
mongo.riskExp.host=${mongo.riskExp.host}
mongo.riskExp.username=${mongo.riskExp.username}
mongo.riskExp.password=${mongo.riskExp.password}

risk.assistant.url=${risk.assistant.url}

channel.forward.url=${channel.forward.url}

risk.verify.url=${risk.verify.url}

cd.url=${cd.url}

risk.dc.url=${risk.dc.url}

domain.url=${domain.url}
risk.approve.service.url=${risk.approve.service.url}

risk.dc.inside.url=${risk.dc.inside.url}

risk.admin.url=${risk.admin.url}

cp.util.getUserParent=${cp.util.getUserParent}

xxl.job.admin.addresses=${xxl.job.admin.addresses}
xxl.job.accessToken=${xxl.job.accessToken}
xxl.job.executor.appname=${xxl.job.executor.appname}
xxl.job.executor.address=${xxl.job.executor.address}
xxl.job.executor.ip=${xxl.job.executor.ip}
xxl.job.executor.port=${xxl.job.executor.port}
xxl.job.executor.logpath=${xxl.job.executor.logpath}
xxl.job.executor.logretentiondays=${xxl.job.executor.logretentiondays}


influx.url=${influx.url}
influx.user=${influx.user}
influx.password=${influx.password}
influx.database=${influx.database}
influx.database.engine=${influx.database.engine}
influx.antifraud.database.risk=${influx.antifraud.database.risk}
influx.antifraud.database.risk.rm=${influx.antifraud.database.risk.rm}

wechat.credit.warn.url=${wechat.credit.warn.url}
wechat.robot.url=${wechat.robot.url}
feature.wechat.bot.key=${feature.wechat.bot.key}
wechat.base.url=${wechat.base.url}

wechat.risk.plat.aesKey=${wechat.risk.plat.aesKey}
wechat.risk.plat.token=${wechat.risk.plat.token}
wechat.risk.plat.corpId=${wechat.risk.plat.corpId}

youxin.environment.type=${youxin.environment.type}

weicai.pudao.gw.url=${weicai_pudao_gw_url}
weicai.heika.gw.url=${weicai_heika_gw_url}
weicai.rongdan.gw.url=${weicai_rongdan_gw_url}

pudao.weicai.gw.url=${pudao_weicai_gw_url}

heika.weicai.gw.url=${heika_weicai_gw_url}
heika.rongdan.gw.url=${heika_rongdan_gw_url}

rongdan.weicai.gw.url=${rongdan_weicai_gw_url}
rongdan.heika.gw.url=${rongdan_heika_gw_url}

system.check.robotKey=${system.check.robotKey}
variable.url=${variable.url}
risk.admin.heika.url=${risk.admin.heika.url}
risk.admin.rongdan.url=${risk.admin.rongdan.url}
cp.url=${cp.url}
fs.url=${fs.url}
workerURLPy3=${workerURLPy3}

block.qw.op.url=${block.qw.op.url}
block.qw.robot.key=${block.qw.robot.key}

dw.service.url=${dw.service.url}
approve.url=${approve.url}
risk.alert.send.url=${risk.alert.send.url}
transfer.mongo.credentials=${transfer.mongo.credentials}
variable.manager.url=${variable.manager.url}

block.qw.ext.op.url=${block.qw.ext.op.url}
block.qw.ext.robot.key=${block.qw.ext.robot.key}
