package com.youxin.risk.admin.model.ruleEngine;

import java.util.Date;

public class StrategyCode {
    private Integer id;

    private String sourceSystem;

    private Date createTime;

    private Date updateTime;

    private String code;

    public static StrategyCode buildForInsert(String sourceSystem, String code, Date now) {
        StrategyCode strategyCode = new StrategyCode();
        strategyCode.setSourceSystem(sourceSystem);
        strategyCode.setCode(code);
        strategyCode.setCreateTime(now);
        strategyCode.setUpdateTime(now);
        return strategyCode;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }
}