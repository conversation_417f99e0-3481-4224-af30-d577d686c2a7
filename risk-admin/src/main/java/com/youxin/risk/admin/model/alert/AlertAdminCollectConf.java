package com.youxin.risk.admin.model.alert;

import com.youxin.risk.commons.model.BaseModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 报警采集表
 * 
 * <AUTHOR> 
 * @date 2018-12-26
 */
@Setter
@Getter
public class AlertAdminCollectConf extends BaseModel {

    /**
     * 采集规则编码
     */
    private String collectCode;

    /**
     * 采集规则描述
     */
    private String collectDesc;

    /**
     * 数据源
     */
    private String datasourceName;

    /**
     * 采集数据表
     */
    private String tableName;

    /**
     * 聚合函数count、max、mean、sum
     */
    private String function;

    /**
     * 聚合字段
     */
    private String functionField;

    /**
     * 采集单位时间段
     */
    private Long collectMinutes;

    /**
     * 分组属性，多个以英文逗号（,）分隔
     */
    private String tags;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private List<AlertAdminCollectCondition> alertAdminCollectConditions;


}