package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/15 10:22
 */
public class AdminStrategySplitFlow extends BaseModel {
    private static final long serialVersionUID = -3152160615666108840L;

    private String sourceSystem;
    private String splitFlowCode;
    private String splitFlowName;
    private String onlineStrategyCode;
    private String splitStrategyCode;
    private Integer splitFlowPercent;
    private Date beginTime;
    private Date endTime;
    private String status;
    private String operator;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getSplitFlowCode() {
        return splitFlowCode;
    }

    public void setSplitFlowCode(String splitFlowCode) {
        this.splitFlowCode = splitFlowCode;
    }

    public String getSplitFlowName() {
        return splitFlowName;
    }

    public void setSplitFlowName(String splitFlowName) {
        this.splitFlowName = splitFlowName;
    }

    public String getOnlineStrategyCode() {
        return onlineStrategyCode;
    }

    public void setOnlineStrategyCode(String onlineStrategyCode) {
        this.onlineStrategyCode = onlineStrategyCode;
    }

    public String getSplitStrategyCode() {
        return splitStrategyCode;
    }

    public void setSplitStrategyCode(String splitStrategyCode) {
        this.splitStrategyCode = splitStrategyCode;
    }

    public Integer getSplitFlowPercent() {
        return splitFlowPercent;
    }

    public void setSplitFlowPercent(Integer splitFlowPercent) {
        this.splitFlowPercent = splitFlowPercent;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
