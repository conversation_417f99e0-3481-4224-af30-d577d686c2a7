package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.admin.dao.admin.AdminEventInfoMapper;
import com.youxin.risk.admin.dao.admin.AdminProcessExperimentMapper;
import com.youxin.risk.admin.model.AdminEventInfo;
import com.youxin.risk.admin.model.AdminProcessExperiment;
import com.youxin.risk.admin.service.AdminProcessExperimentService;
import com.youxin.risk.commons.mongo.FeatureExperimentResultMongoDao;
import com.youxin.risk.commons.mongo.StrategyExperimentResultMongoDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022-1-20
 */
@Service
public class AdminProcessExperimentServiceImpl extends BaseServiceImpl<AdminProcessExperiment> implements AdminProcessExperimentService {
    @Resource
    private AdminProcessExperimentMapper adminProcessExperimentMapper;
    @Resource
    private StrategyExperimentResultMongoDao strategyExperimentResultMongoDao;
    @Resource
    private FeatureExperimentResultMongoDao featureExperimentResultMongoDao;
    @Resource
    private AdminEventInfoMapper adminEventInfoMapper;

    @Override
    protected void init() {
        tableName = "admin_process_experiment";
        baseMapper = adminProcessExperimentMapper;
    }

    @Override
    public Boolean checkExists(String expCode) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("expCode", expCode);
        int count = adminProcessExperimentMapper.selectCount(params);
        return count > 0;
    }

    @Override
    public void delete(Long id) {
//        AdminProcessExperiment adminProcessExperiment = adminProcessExperimentMapper.get(id);
        super.delete(id);
//        featureExperimentResultMongoDao.removeByExpCode(adminProcessExperiment.getExpCode());
//        strategyExperimentResultMongoDao.removeByExpCode(adminProcessExperiment.getExpCode());
    }

    @Override
    public String getEventCodeByProcessDefId(String processDefId) {
        List<AdminEventInfo> adminEventInfos = adminEventInfoMapper.selectByProcessDefId(processDefId);
        if (CollectionUtils.isNotEmpty(adminEventInfos)) {
            return adminEventInfos.get(0).getEventCode();
        }
        return null;
    }

    @Override
    public void updateStatus(List<Long> idList, String status) {
        adminProcessExperimentMapper.batchUpdateStatus(idList, status);
        updateSysDbUpdateTime(false);
    }

    @Override
    public List<AdminProcessExperiment> selectForDisable() {
        return adminProcessExperimentMapper.selectForDisable();
    }

    @Override
    public void dealWithAdminProcessExperiment(AdminProcessExperiment adminProcessExperiment) {
        if (adminProcessExperiment.getFeatureSubmitCode() == null) {
            //前端要求不能返回null
            Set<String> featureSubmitCode = new HashSet<>();
            adminProcessExperiment.setFeatureSubmitCode(featureSubmitCode);
        }
        if (adminProcessExperiment.getNodeMappingList() != null && !adminProcessExperiment.getNodeMappingList().isEmpty()) {
            adminProcessExperiment.setNodeMapping(adminProcessExperiment.getNodeMappingList().toJSONString());
        }
        if (adminProcessExperiment.getNodeMapping() != null) {
            adminProcessExperiment.setNodeMappingList(JSON.parseArray(adminProcessExperiment.getNodeMapping()));
        } else {
            //前端要求不能返回null
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("processNode", "");
            jsonObject.put("mappingNode", "");
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(jsonObject);
            adminProcessExperiment.setNodeMappingList(jsonArray);
        }
    }

    @Override
    public List<AdminProcessExperiment> selectList(Map<String, Object> params) {
        return adminProcessExperimentMapper.selectList(params);
    }
}