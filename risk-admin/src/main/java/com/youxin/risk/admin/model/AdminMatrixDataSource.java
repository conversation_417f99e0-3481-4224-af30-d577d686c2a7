/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.model;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.google.common.collect.Lists;
import com.youxin.risk.commons.constants.DataGroupTypeEnum;
import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.GroupDataSource;
import com.youxin.risk.commons.model.NodeData;

public class AdminMatrixDataSource extends BaseModel {

    private String groupCode;

    private String groupName;

    private String nodeCode;

    private List<AdminGroupDataSource> candidateGroups;

    private AdminGroupDataSource finalGroup;

    private Boolean deleted;

    private String originalNodeCode;

    public void addGroupDataSource(AdminGroupDataSource adminGroupDataSource) {
        if (DataGroupTypeEnum.REQUIREDED.name().equals(adminGroupDataSource.getGroupType())) {
            finalGroup = adminGroupDataSource;
        } else if (DataGroupTypeEnum.CANDIDATE.name().equals(adminGroupDataSource.getGroupType())) {
            if (CollectionUtils.isEmpty(candidateGroups)) {
                candidateGroups = Lists.newArrayList();
            }
            candidateGroups.add(adminGroupDataSource);
        }
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<AdminGroupDataSource> getCandidateGroups() {
        return candidateGroups;
    }

    public void setCandidateGroups(List<AdminGroupDataSource> candidateGroups) {
        this.candidateGroups = candidateGroups;
    }

    public AdminGroupDataSource getFinalGroup() {
        return finalGroup;
    }

    public void setFinalGroup(AdminGroupDataSource finalGroup) {
        this.finalGroup = finalGroup;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getOriginalNodeCode() {
        return originalNodeCode;
    }

    public void setOriginalNodeCode(String originalNodeCode) {
        this.originalNodeCode = originalNodeCode;
    }
}
