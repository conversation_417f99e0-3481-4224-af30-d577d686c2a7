package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.youxin.risk.admin.dao.admin.*;
import com.youxin.risk.admin.dao.rm.RmStrategyResultIndexMapper;
import com.youxin.risk.admin.dto.JsonMap;
import com.youxin.risk.admin.dto.JsonResult;
import com.youxin.risk.admin.dto.PageInfo;
import com.youxin.risk.admin.dto.rulescore.*;
import com.youxin.risk.admin.model.rulescore.*;
import com.youxin.risk.admin.service.rulescore.RuleScoreService;
import com.youxin.risk.admin.utils.HttpUtil;
import com.youxin.risk.admin.utils.RuleSetCodeHandler;
import com.youxin.risk.admin.utils.StringUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.youxin.risk.admin.model.rulescore.RuleCandidateScore.ruleCancel;
import static com.youxin.risk.admin.model.rulescore.RuleCandidateScore.ruleOffline;

@RestController
@RequestMapping("/ruleScore")
@Slf4j
public class RuleScoreController {

        @Autowired
        private RuleScoreService ruleScoreService;
        @Autowired
        private RuleScoreMapper ruleScoreMapper;
        @Autowired
        private RuleScoreStrategyMapper ruleScoreStrategyMapper;
        @Autowired
        private RuleCandidateScoreMapper ruleCandidateScoreMapper;
        @Autowired
        private RmStrategyResultIndexMapper rmStrategyResultIndexMapper;
        @Autowired
        private RuleScoreRelationMapper ruleScoreRelationMapper;
        @Resource
        private RuleSetCodeHandler ruleSetCodeHandler;
        @Autowired
        private RuleScoreVarMapper ruleScoreVarMapper;
//        @Autowired
//        private RuleCandidateScoreMapper ruleCandidateScoreMapper;


        @RequestMapping(value = "/ruleScoreVar", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult ruleScoreVar(@RequestBody JSONObject jsonObject) {
            String variableCode = jsonObject.getString("variableCode");
            List<RuleScoreVar> ruleScoreByVar = ruleScoreVarMapper.findRuleScoreByVar(variableCode);
            //过滤下线和废弃的规则分
            Set<String>  ruleScoreSet = new HashSet<>();
            for (RuleScoreVar ruleScoreVar : ruleScoreByVar){
                String ruleKey = ruleScoreVar.getRuleKey();
                Integer version = ruleScoreVar.getRuleVersion();
                RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, version);
                if(ruleCandidateScore.getRuleStatus().intValue() != ruleCancel && ruleCandidateScore.getRuleStatus().intValue() != ruleOffline){
                    ruleScoreSet.add(ruleKey);
                }
            }
            List<String> ruleScoreList = ruleScoreSet.stream().collect(Collectors.toList());
            return JsonResult.success(ruleScoreList);
        }

        /**
         * 规则分保存
         */
        @RequestMapping(value = "/save", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult save(@RequestBody RuleScoreDTO ruleScoreDTO,@RequestHeader(value = "X-UserName",required = false) String userKey) {
            ruleScoreService.save(ruleScoreDTO,userKey);
            return JsonResult.success();
        }

        /**
         * 获取变量的集合
         * @param jsonObject
         * @param userKey
         * @return
         */
        @RequestMapping(value = "/getVar",method = RequestMethod.POST)
        @ResponseBody
        public JsonResult getVar(@RequestBody JSONObject jsonObject,@RequestHeader(value = "X-UserName",required = false) String userKey) {
            List<RuleScoreVar> ruleScoreVar = ruleScoreVarMapper.findRuleScoreVar(jsonObject.getString("ruleKey"), jsonObject.getInteger("ruleVersion"));
            List<String> varCodes = ruleScoreVar.stream().map(r -> r.getVariableCode()).collect(Collectors.toList());
            return JsonResult.success(varCodes);
        }

        /**
         *  规则分列表
         */
        @RequestMapping(value = "/page", method = RequestMethod.POST)
        @ResponseBody
        public String page(@RequestBody RuleScoreQuery ruleScoreQuery){
            PageHelper.startPage(ruleScoreQuery.getPageNum(), ruleScoreQuery.getPageSize(), true);
            Page<RuleScorePageDTO> ruleScorePage = ruleScoreMapper.findRuleScorePage(ruleScoreQuery);
            for (RuleScorePageDTO page : ruleScorePage){
                String ruleKey = page.getRuleKey();
                Integer ruleVersion = page.getRuleVersion();

                List<RuleScoreStrategy> ruleScoreStrategyList = ruleScoreStrategyMapper.findRuleScoreStrategyList(ruleKey, ruleVersion);
                if (!CollectionUtils.isEmpty(ruleScoreStrategyList)) {
                    page.setRuleScoreStrategies(ruleScoreStrategyList);
                }
            }
            PageInfo<RuleScorePageDTO> tagGroupPOPageInfo = new PageInfo<>(ruleScorePage);
            return JsonMap.addPageInfo(tagGroupPOPageInfo);
        }

        /**
         * 规则分明细
         */
        @RequestMapping(value = "/detail", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult detail(@RequestBody JSONObject jsonObject){
            String ruleKey = jsonObject.getString("ruleKey");
            Integer ruleVersion = jsonObject.getInteger("ruleVersion");
            List<RuleCandidateScore> ruleCandidateScoreList = ruleCandidateScoreMapper.findRuleCandidateScoreList(ruleKey);
            if (ruleVersion == -1){
                /** 说明没有上线的时候, 可能之前编辑了很多次,使用最后一次的编辑版本,否则就使用当前线上版本 **/
                ruleVersion = ruleCandidateScoreList.size();
            }

            RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, ruleVersion);
            List<RuleScoreStrategy> ruleScoreStrategyList = ruleScoreStrategyMapper.findRuleScoreStrategyList(ruleKey, ruleVersion);
            if (!CollectionUtils.isEmpty(ruleCandidateScoreList)){
                ruleCandidateScore.setRuleScoreStrategies(ruleScoreStrategyList);
            }
            List<RuleScoreRelation> ruleScoreRelationList = ruleScoreRelationMapper.findRuleScoreRelationList(ruleKey, ruleVersion);
            if (!CollectionUtils.isEmpty(ruleScoreRelationList)){
                List<String> relationRuleKeyList = ruleScoreRelationList.stream().map(ruleScoreRelation -> ruleScoreRelation.getRelationRuleKey()).collect(Collectors.toList());

                ruleCandidateScore.setRuleScoreRelations(relationRuleKeyList);
            }
            return JsonResult.success(ruleCandidateScore);
        }

        /**
         * 历史版本
         * @param userKey
         * @return
         */
        @RequestMapping(value = "/history", method = RequestMethod.POST)
        @ResponseBody
        public String history(@RequestBody RuleCandidateScoreQuery ruleCandidateScoreQuery, @RequestHeader(value = "X-UserName",required = false) String userKey) {
            PageHelper.startPage(ruleCandidateScoreQuery.getPageNum(), ruleCandidateScoreQuery.getPageSize(), true);
            Page<RuleCandidateScore> ruleCandidateScorePage = ruleCandidateScoreMapper.findRuleCandidateScoreHistory(ruleCandidateScoreQuery);
            PageInfo<RuleCandidateScore> pageInfo = new PageInfo<>(ruleCandidateScorePage);
            return JsonMap.addPageInfo(pageInfo);
        }

        /**
         * 节点绑定
         * @param jsonObject
         * @param userKey
         * @return
         */
        @RequestMapping(value = "/binding", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult binding(@RequestBody JSONObject jsonObject, @RequestHeader(value = "X-UserName",required = false) String userKey) throws ParseException {
            if(StringUtils.isNotEmpty(userKey)){
                UserInfoUtil.setUsername(userKey);
            }
            String ruleKey = jsonObject.getString("ruleKey");
            Integer version = jsonObject.getInteger("ruleVersion");
            JSONArray jsonArray = jsonObject.getJSONArray("nodeStrategy");

            ruleScoreService.binding(ruleKey, version, jsonArray,false);
            return JsonResult.success();
        }

        @RequestMapping(value = "/bindingForce", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult bindingForce(@RequestBody JSONObject jsonObject, @RequestHeader(value = "X-UserName",required = false) String userKey) throws ParseException {
            if(StringUtils.isNotEmpty(userKey)){
                UserInfoUtil.setUsername(userKey);
            }
            String ruleKey = jsonObject.getString("ruleKey");
            Integer version = jsonObject.getInteger("ruleVersion");
            JSONArray jsonArray = jsonObject.getJSONArray("nodeStrategy");
            ruleScoreService.binding(ruleKey, version, jsonArray,true);
            return JsonResult.success();
        }

        @RequestMapping(value = "/compare", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult compare(@RequestBody JSONObject jsonObject, @RequestHeader(value = "X-UserName",required = false) String userKey) {
            JSONObject original = jsonObject.getJSONObject("original");
            String ruleKey = original.getString("ruleKey");
            Integer ruleVersion = original.getInteger("ruleVersion");
            RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, ruleVersion);

            JSONObject modified = jsonObject.getJSONObject("modified");
            String ruleKeyModified = modified.getString("ruleKey");
            Integer ruleVersionModified = modified.getInteger("ruleVersion");
            RuleCandidateScore ruleCandidateScoreModified = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKeyModified, ruleVersionModified);

            JSONObject json = new JSONObject();
            json.put("original",ruleCandidateScore);
            json.put("modified",ruleCandidateScoreModified);
            return JsonResult.success(json);
        }

        @Value("${cp.url}")
        private String cpUrl;

        @RequestMapping(value = "/randomSample", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult randomSample(@RequestBody SampleDTO sampleDTO,@RequestHeader(value = "X-UserName",required = false) String userKey) {

            String ruleKey = sampleDTO.getRuleKey();
            Integer ruleVersion = sampleDTO.getRuleVersion();
            String ruleRunCode = sampleDTO.getRuleRunCode();
            String node = sampleDTO.getNode();
            List<RuleScoreStrategy> ruleScoreStrategyList = ruleScoreStrategyMapper.findRuleScoreStrategyList(ruleKey, ruleVersion);
            if (CollectionUtils.isEmpty(ruleScoreStrategyList) && StringUtils.isEmpty(node)){
                throw new RiskRuntimeException("没有绑定策略类型,并且没有选择节点");
            }
            /** 随机样本: 选择的节点和绑定的节点互斥 **/
            List<String> nodes = new ArrayList<>();
            if (StringUtils.isNotEmpty(node)) {
                nodes.add(node);
            }else {
                for (RuleScoreStrategy ruleScoreStrategy : ruleScoreStrategyList){
                    nodes.add(ruleScoreStrategy.getNode());
                }
            }
            log.info("randomSample nodes= {}",JSON.toJSONString(nodes));
            List<RmStrategyResultIndexDTO> rmStrategyResultIndexDTOList = rmStrategyResultIndexMapper.getRowKeysByNode(nodes);
            if (CollectionUtils.isEmpty(rmStrategyResultIndexDTOList)){
                throw new RiskRuntimeException("没有样本数据");
            }
            RmStrategyResultIndexDTO rmStrategyResultIndexDTO = rmStrategyResultIndexDTOList.stream().skip(new Random().nextInt(rmStrategyResultIndexDTOList.size())).findFirst().orElse(new RmStrategyResultIndexDTO());
            log.info("randomSample all= {} random={}",JSON.toJSONString(rmStrategyResultIndexDTOList),JSON.toJSONString(rmStrategyResultIndexDTO));

            String rowKey = rmStrategyResultIndexDTO.getRowKey();
            String step = rmStrategyResultIndexDTO.getStep();
            String loanKey = rmStrategyResultIndexDTO.getLoanKey();

            JSONObject localParam = getXmlStrByRowKey(rowKey);

            JSONObject data = sampleTestApi(localParam, sampleDTO.getRuleKey(), sampleDTO.getRuleVersion(),ruleRunCode);
            data.put("node",step);
            data.put("rowKey",rowKey);
            data.put("loanKey",loanKey);
            return JsonResult.success(data);
        }

        @RequestMapping(value = "/choiceSample", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult choiceSample(@RequestBody SampleDTO sampleDTO,@RequestHeader(value = "X-UserName",required = false) String userKey) {
            String loanKey = sampleDTO.getLoanKey();
            String ruleRunCode = sampleDTO.getRuleRunCode();
            if (StringUtils.isEmpty(loanKey)){
                throw new RiskRuntimeException("loanKey是必输项");
            }
            List<String> stepList = new ArrayList<>();
            if (!StringUtils.isEmpty(sampleDTO.getNode())){
                String node = sampleDTO.getNode();
                stepList.add(node);
            }
            RmStrategyResultIndexDTO rowKeysByConditions = rmStrategyResultIndexMapper.getRowKeysByConditions(stepList, loanKey);
            if (rowKeysByConditions == null){
                throw new RiskRuntimeException("没有样本数据");
            }
            String rowKey = rowKeysByConditions.getRowKey();
            String step = rowKeysByConditions.getStep();

            JSONObject localParam = getXmlStrByRowKey(rowKey);
            JSONObject data = sampleTestApi(localParam, sampleDTO.getRuleKey(), sampleDTO.getRuleVersion(),ruleRunCode);
            data.put("node",step);
            data.put("rowKey",rowKey);
            return JsonResult.success(data);
        }


        @RequestMapping(value = "/localSample", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult localSample(@RequestBody SampleDTO sampleDTO, @RequestHeader(value = "X-UserName",required = false) String userKey) {
            String ruleKey = sampleDTO.getRuleKey();
            Integer ruleVersion = sampleDTO.getRuleVersion();
//            JSONObject localParam = sampleDTO.getLocalParam();
            JSONObject localParam = JSONObject.parseObject(sampleDTO.getLocalParam());
            String ruleRunCode = sampleDTO.getRuleRunCode();
            JSONObject result = sampleTestApi(localParam,ruleKey,ruleVersion,ruleRunCode);
            return JsonResult.success(result);
        }

        @Value("${fs.url}")
        private String fsUrl;

        @RequestMapping(value = "/getXmlStrByRowKeyTest", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult getXmlStrByRowKeyTest(@RequestBody JSONObject jsonObject){
            String rowKey = jsonObject.getString("rowKey");
            return JsonResult.success(getXmlStrByRowKey(rowKey));
        }

        /**
         * 查看HBase的结果: 获取样本数据
         * @param rowKey
         * @return
         */
        public JSONObject getXmlStrByRowKey(String rowKey){
            StringBuilder url = new StringBuilder(fsUrl).append("?rowKey=").append(rowKey);
            String response = HttpUtil.getHttpClient().get(url.toString());
            JSONObject param = new JSONObject();
            if (!StringUtils.isEmpty(response)) {
                JSONObject variableMap = JSONObject.parseObject(response).getJSONObject("variableMapStr");
                if (variableMap == null) {
                    variableMap = new JSONObject();
                }
                param.put("vars", variableMap);
                JSONObject ruleSetMap = JSONObject.parseObject(response).getJSONObject("ruleSetMapStr");
                if(ruleSetMap == null){
                    ruleSetMap = new JSONObject();
                }
                param.put("rules", ruleSetMap);
            }
            return param;
        }

        /**
         * 样本测试
         * @param localParam
         * @param ruleKey
         * @param ruleVersion
         * @return
         */
        private JSONObject sampleTestApi(JSONObject localParam, String ruleKey,Integer ruleVersion,String code) {
            String generate = ruleSetCodeHandler.generate(ruleKey, ruleVersion, code);
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            JSONObject json = new JSONObject();
            json.put("type","RuleScoreTest");
            json.put("code",generate);
            json.put("input",localParam);
            String response = HttpUtil.getHttpClient().post(cpUrl,headers,json.toJSONString());
            JSONObject jsonObject = JSONObject.parseObject(response);
            JSONObject data = jsonObject.getJSONObject("data");
            String output = data.getString("output");
            String printOutput = data.getString("print_output");
            JSONObject result = new JSONObject();
            result.put("output",output);
            result.put("printOutput",printOutput);
            /** 补充一个回显字段 **/
            result.put("localParam",localParam);
            return result;
        }

        /**
         * 查询上线的规则分
         */
        @RequestMapping(value = "/findOnlineRuleKeyList", method = RequestMethod.GET)
        @ResponseBody
        public JsonResult findBindRuleKeyList() {
            List<RuleScore> ruleScoreList = ruleScoreService.findBindRuleKeyList();
            return JsonResult.success(ruleScoreList);
        }

        /**
         * 根据策略类型、节点名称查询现在已经绑定的规则分key
         */
        @RequestMapping(value = "/findBindRuleKeyListByNode", method = RequestMethod.GET)
        @ResponseBody
        public JsonResult findBindRuleKeyListByNode(@RequestParam String node, @RequestParam String strategy) {
            List<RuleCandidateScore> ruleScoreList = ruleScoreService.findBindRuleKeyListByNode(strategy, node);
            return JsonResult.success(ruleScoreList);
        }

        /**
         * 根据策略类型、节点名称、规则分key查询最新绑定的线上规则分版本信息
         */
        @RequestMapping(value = "/findBindRuleKeyList", method = RequestMethod.GET)
        @ResponseBody
        public String findBindRuleKeyList(@RequestParam String strategy, @RequestParam String node, @RequestParam String ruleKey, @RequestParam Integer pageNum, @RequestParam Integer pageSize) {
            PageInfo<RuleCandidateScore> ruleScoreList = ruleScoreService.findBindRuleKeyList(
                    strategy, node, ruleKey, pageNum, pageSize);
            return JsonMap.addPageInfo(ruleScoreList);
        }

        /**
         * 根据规则分key、规则分版本号 查询规则分代码
         */
        @RequestMapping(value = "/findRuleCode", method = RequestMethod.GET)
        @ResponseBody
        public JsonResult findRuleCode(@RequestParam String ruleKey, @RequestParam Integer ruleVersion) {
            RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, ruleVersion);
            String generateCode = ruleSetCodeHandler.generate(ruleKey, ruleVersion, ruleCandidateScore.getRuleRunCode());
            ruleCandidateScore.setRuleRunCode(generateCode);
            return JsonResult.success(ruleCandidateScore);
        }

        /**
         * 查询规则分依赖的哪些规则分
         */
        @RequestMapping(value = "/findRuleScoreRelationList", method = RequestMethod.GET)
        @ResponseBody
        public JsonResult findRuleScoreRelationList(@RequestParam String ruleKey) {
            List<RuleScoreRelationOnline> ruleScoreRelationList = ruleScoreService.findRuleScoreRelationList(ruleKey);
            return JsonResult.success(ruleScoreRelationList);
        }

        /**
         * 规则分解绑
         */
        @RequestMapping(value = "/unbind", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult unbind(@RequestBody JSONObject jsonObject, @RequestHeader(value = "X-UserName",required = false) String userKey) {
            log.info("unbind data = {} ", JSON.toJSONString(jsonObject));
            String ruleKey = jsonObject.getString("ruleKey");
            Integer ruleVersion = jsonObject.getInteger("ruleVersion");
            String strategy = jsonObject.getString("strategy");
            String node = jsonObject.getString("node");
            ruleScoreService.unbindRuleScore(ruleKey, ruleVersion, strategy, node);
            return JsonResult.success();
        }

        @RequestMapping(value = "/queryOnlineRuleKeyList", method = RequestMethod.POST)
        @ResponseBody
        public JsonResult queryOnlineRuleKeyList(@RequestBody JSONObject jsonObject){
            List<String> ruleKeys = jsonObject.getJSONArray("ruleKeys").toJavaList(String.class);
            if(CollectionUtils.isEmpty(ruleKeys)){
                return JsonResult.success();
            }
            return JsonResult.success(ruleCandidateScoreMapper.selectOnlineRuleKeyList(ruleKeys));
        }


    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult delete(@RequestBody JSONObject jsonObject, @RequestHeader(value = "X-UserName",required = false) String userKey) {
        log.info("delete data = {} ", JSON.toJSONString(jsonObject));
        String ruleKey = jsonObject.getString("ruleKey");
        Integer ruleVersion = jsonObject.getInteger("ruleVersion");
//        String strategy = jsonObject.getString("strategy");
//        String node = jsonObject.getString("node");
        ruleScoreService.delete(ruleKey, ruleVersion,userKey);
        return JsonResult.success();
    }

    /**
     * 查询所有规则分
     */
    @RequestMapping(value = "/allRuleScores", method = RequestMethod.GET)
    @ResponseBody
    public JsonResult getAllRuleScores() {
        List<String> ruleScores = ruleScoreMapper.findAllRuleKeys();
        // 增加无 用于前端筛选
        ruleScores.add("无");
        return JsonResult.success(ruleScores);
    }

    /**
     * 根据策略类型、节点名称查询现在已经绑定的规则分key
     */
    @RequestMapping(value = "/findRuleKeysByNode", method = RequestMethod.GET)
    @ResponseBody
    public JsonResult findRuleKeysByNode(@RequestParam String node, @RequestParam String strategyType) {
        Assert.hasText(node, "节点名称(node)不能为空");
        Assert.hasText(strategyType, "策略类型(strategyType)不能为空");
        List<RuleCandidateScore> ruleScoreList = ruleScoreService.findBindRuleKeyListByNode(strategyType, node);
        List<String> ruleKeys = ruleScoreList.stream().map(RuleCandidateScore::getRuleKey).collect(Collectors.toList());
        // 增加无 用于前端筛选
        ruleKeys.add("无");
        return JsonResult.success(ruleKeys);
    }
}