package com.youxin.risk.admin.service.approveImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.approve.AdminApproveSubmit;
import com.youxin.risk.admin.service.AdminApproveService;
import com.youxin.risk.admin.service.ManualThirdConfigService;
import com.youxin.risk.admin.vo.AdminManualNodeDataVo;
import com.youxin.risk.admin.vo.AdminManualThirdVo;
import com.youxin.risk.commons.utils.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class AdminApproveSaveManulThirdImpl extends AdminApproveService {

    @Resource
    ManualThirdConfigService manualThirdConfigService;

    @Override
    protected String handleRequest(AdminApproveSubmit submit){
        try{
            AdminManualThirdVo service = JSONObject.parseObject(submit.getNewRequest(), AdminManualThirdVo.class);
            // check是否包含重复步骤记录
            List<AdminManualNodeDataVo> datas = service.getDatas();
            if (CollectionUtils.isEmpty(datas)) {
                throw new Exception("findSaveMsgError");
            }
            Set<String> stepSet = new HashSet<>();
            for (AdminManualNodeDataVo record: datas) {
                if (!stepSet.add(record.getNodeCode()+record.getStep())){
                    throw new Exception("存在相同步骤，请重新录入!");
                }
            }
            JSONObject result = (JSONObject)manualThirdConfigService.updateEventThird(submit.getUser(), service);
            boolean data = result.getBoolean("result");
            if(data){
                return HANDLE_SUCCESS;
            }else{
                throw new Exception("未知错误");
            }
        }catch (Exception e){
            return HANDLE_FAILED + e.getMessage();
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    protected Object getOldVo(Object newObj){
        AdminManualThirdVo newVo = JSONObject.parseObject(JsonUtils.toJson(newObj), AdminManualThirdVo.class);
        JSONObject params = new JSONObject();
        params.put("serviceCode",newVo.getServiceCode());
        Object result = manualThirdConfigService.selectEventThirdDetail(params);
        JSONArray array = (JSONArray)((JSONObject)result).get("thirdList");
        AdminManualThirdVo oldVo = new AdminManualThirdVo();
        oldVo.setServiceCode(newVo.getServiceCode());
        oldVo.setDataSourceMode(newVo.getDataSourceMode());
        oldVo.setDatas(JSONObject.parseObject(JsonUtils.toJson(array), List.class));
        return oldVo;
    }
}
