package com.youxin.risk.admin.service.impl;

import com.youxin.risk.admin.dao.admin.AlertAdminMetadataMapper;
import com.youxin.risk.admin.dao.admin.AlertAdminPolicyMapper;
import com.youxin.risk.admin.model.alert.AlertAdminMetadata;
import com.youxin.risk.admin.model.alert.AlertAdminPolicy;
import com.youxin.risk.admin.service.AdminDiServiceService;
import com.youxin.risk.admin.service.AlertMetadataService;
import com.youxin.risk.admin.service.AlertPolicyService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AlertMetadataServiceImpl implements AlertMetadataService {

    @Autowired
    AlertAdminMetadataMapper alertAdminMetadataMapper;
    @Autowired
    AdminDiServiceService adminDiServiceService;

    @Override
    public int removeItem(AlertAdminMetadata alertAdminMetadata) {
        int ret = alertAdminMetadataMapper.deleteByPrimaryKey(alertAdminMetadata.getId());
        adminDiServiceService.modifySysDbUpdate(ConfigTableEnum.alert_admin_metadata.name(), true);
        return ret;
    }

    @Override
    public List<String> getAllAlertSys(String type) {
        //获取系统定义类型  app alerts alertLevel
        return alertAdminMetadataMapper.getAllAlertSys(type);
    }

    @Override
    public List<AlertAdminMetadata> getList(AlertAdminMetadata item) {
        return alertAdminMetadataMapper.selectList(item);
    }

    @Override
    public AlertAdminMetadata queryItemDetail(AlertAdminMetadata item) {
        return alertAdminMetadataMapper.selectByPrimaryKey(item.getId());
    }

    @Override
    public int modifyItem(AlertAdminMetadata item) {
//        List<AlertAdminMetadata> alertAdminMetadata = alertAdminMetadataMapper.queryDistictItem(item);
//        if(alertAdminMetadata.size() > 0 && alertAdminMetadata.get(0).getId() != item.getId())
//            return -1;
        int ret = alertAdminMetadataMapper.updateByPrimaryKeySelective(item);
        adminDiServiceService.modifySysDbUpdate(ConfigTableEnum.alert_admin_metadata.name(), false);
        return ret;
    }

    @Override
    public int addItem(AlertAdminMetadata item) {
//        List<AlertAdminMetadata> alertAdminMetadata = alertAdminMetadataMapper.queryDistictItem(item);
//        if(alertAdminMetadata.size() > 0 )
//            return -1;
        int ret = alertAdminMetadataMapper.insertSelective(item);
        adminDiServiceService.modifySysDbUpdate(ConfigTableEnum.alert_admin_metadata.name(), false);
        return ret;
    }

    @Override
    public int removeItem(Long id) {
        int ret = alertAdminMetadataMapper.deleteByPrimaryKey(id);
        adminDiServiceService.modifySysDbUpdate(ConfigTableEnum.alert_admin_metadata.name(), true);
        return ret;
    }
}
