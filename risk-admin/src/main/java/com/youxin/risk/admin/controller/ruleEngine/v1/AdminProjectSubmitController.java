package com.youxin.risk.admin.controller.ruleEngine.v1;

import com.youxin.risk.admin.service.ruleEngine.ProjectSubmitService;
import com.youxin.risk.admin.vo.JsonResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/projects_submit/v1")
public class AdminProjectSubmitController {

    private static final Logger logger = LoggerFactory.getLogger(AdminProjectSubmitController.class);

    @Resource
    private ProjectSubmitService projectSubmitService;

    /**
     * 项目历史集成测试列表查询
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public JsonResultVo list(@RequestBody Map map) {
        try {
            return  JsonResultVo.success()
                    .addData("list", projectSubmitService.list(map));
        } catch (Exception e) {
            logger.error("", e);
            return JsonResultVo.error(e.getMessage());
        }
    }

    /**
     * 项目历史集成测试当时提交记录详情查询
     */
    @ResponseBody
    @RequestMapping(value = "/query_by_id", method = RequestMethod.POST)
    public JsonResultVo queryById(@RequestBody Map map) {
        try {
            return  JsonResultVo.success()
                    .addData("list", projectSubmitService.queryById((Integer) map.get("projectId")
                            , (Integer) map.get("id")));
        } catch (Exception e) {
            logger.error("", e);
            return JsonResultVo.error(e.getMessage());
        }
    }
}
