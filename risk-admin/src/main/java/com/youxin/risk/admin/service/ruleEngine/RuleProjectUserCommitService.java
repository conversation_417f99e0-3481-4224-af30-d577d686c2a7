package com.youxin.risk.admin.service.ruleEngine;

import com.youxin.risk.admin.model.ruleEngine.RuleProjectUserCommit;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 新决策引擎项目个人提交记录表(RuleProjectUserCommit)表服务接口
 *
 */
public interface RuleProjectUserCommitService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    RuleProjectUserCommit queryById(Integer id);

    /**
     * 分页查询
     *
     * @param ruleProjectUserCommit 筛选条件
     * @param pageRequest           分页对象
     * @return 查询结果
     */
    Page<RuleProjectUserCommit> queryByPage(RuleProjectUserCommit ruleProjectUserCommit, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param ruleProjectUserCommit 实例对象
     * @return 实例对象
     */
    RuleProjectUserCommit insert(RuleProjectUserCommit ruleProjectUserCommit);

    /**
     * 修改数据
     *
     * @param ruleProjectUserCommit 实例对象
     * @return 实例对象
     */
    RuleProjectUserCommit update(RuleProjectUserCommit ruleProjectUserCommit);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);


    /**
     * 查询当前用户提交的项目记录
     */
    List<RuleProjectUserCommit> queryByProjectIdAndUserName(Integer projectId, String username);

    /**
     * 根据项目ID和节点名称查询提交记录
     */
    List<RuleProjectUserCommit> queryByProjectIdAndNodeName(Integer projectId, String nodeName);

    /**
     * 根据项目id\用户名\文件名查询提交记录
     */
    RuleProjectUserCommit queryByProjectIdAndUserNameAndFileName(Integer projectId, String username, String fileName);

    /**
     * 查询被删除的节点提交记录
     * @param projectId
     * @param username
     * @param fileName
     * @return
     */
    RuleProjectUserCommit queryByProjectIdAndFileName(Integer projectId, String username, String fileName);

    /**
     * 根据项目id\文件名查询最早的提交记录
     */
    RuleProjectUserCommit queryEarliestByProjectId(Integer projectId);

    /**
     * 根据节点名称更新为删除
     */
    int markAsDeletedByNodeName(String nodeName);
}
