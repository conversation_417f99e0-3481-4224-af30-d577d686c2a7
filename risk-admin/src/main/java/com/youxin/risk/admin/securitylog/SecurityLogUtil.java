package com.youxin.risk.admin.securitylog;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.youxin.risk.admin.utils.UserInfoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * 安全日志输出工具类
 */
public class SecurityLogUtil {
    private static final Logger logger = LoggerFactory.getLogger(SecurityLogUtil.class);

    public static void log(Object data,String userKey){
        try {
            boolean idCardExist=JSONPath.contains(data,"$['originInput']['dataInput']['idCardNo']");
            boolean mobileExist=JSONPath.contains(data,"$['originInput']['dataInput']['mobile']");
            boolean nameExist=JSONPath.contains(data,"$['originInput']['dataInput']['name']");
            boolean bankNoExist=JSONPath.contains(data,"$['originInput']['dataInput']['bankNo']");
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("objectUserInfoMobile",mobileExist?1:0);
            jsonObject.put("objectUserInfoBankNumber",bankNoExist?2:0);
            jsonObject.put("objectUserInfoName",nameExist?1:0);
            jsonObject.put("objectUserInfoIdNumber",idCardExist?1:0);
            log(jsonObject,userKey);
        }catch (Exception e){
            logger.info("log error:",e);
        }
    }


    private static void log(JSONObject jsonObject,String userKey){
        jsonObject.put("systemUserId", UserInfoUtil.getUsername());
        jsonObject.put("systemUserName",UserInfoUtil.getUsername());
        String eventTime=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        jsonObject.put("eventTime",eventTime);
        jsonObject.put("businessId","antifraud-risk");
        jsonObject.put("dataReceive","antifraud-risk");
        jsonObject.put("functionPoint","特征镜像结果获取");
        jsonObject.put("userKey", Arrays.asList(userKey));
        logger.info(jsonObject.toJSONString());
    }
}
