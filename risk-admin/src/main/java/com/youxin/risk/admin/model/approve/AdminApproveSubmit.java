package com.youxin.risk.admin.model.approve;

import com.youxin.risk.commons.model.BaseModel;

public class AdminApproveSubmit extends BaseModel {

    private String user;
    private String URL;
    private String requestType;
    private String type;
    private String oldRequest;
    private String newRequest;
    private int handleResult;
    private String response;

    public AdminApproveSubmit(){}

    public AdminApproveSubmit(String type, String user, String oldRequest, String newRequest){
        this.user = user;
        this.type = type;
        this.oldRequest = oldRequest;
        this.newRequest = newRequest;
        this.handleResult = 0;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getURL() {
        return URL;
    }

    public void setURL(String URL) {
        this.URL = URL;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getOldRequest() {
        return oldRequest;
    }

    public void setOldRequest(String oldRequest) {
        this.oldRequest = oldRequest;
    }

    public String getNewRequest() {
        return newRequest;
    }

    public void setNewRequest(String newRequest) {
        this.newRequest = newRequest;
    }

    public int getHandleResult() {
        return handleResult;
    }

    public void setHandleResult(int handleResult) {
        this.handleResult = handleResult;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
