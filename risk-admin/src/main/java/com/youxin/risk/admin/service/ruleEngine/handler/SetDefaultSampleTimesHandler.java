package com.youxin.risk.admin.service.ruleEngine.handler;

import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.utils.DateUtils;
import com.youxin.risk.admin.vo.ruleEngine.StrategyIntegrationTestModel;
import org.springframework.stereotype.Component;

@Component
public class SetDefaultSampleTimesHandler extends AbstractIntegrationHandler {

    @Override
    public void handle(StrategyIntegrationTestModel strategyIntegrationTestModel, AdminCandidateStrategy adminCandidateStrategy) throws Exception {
        if (1 == strategyIntegrationTestModel.getTestSampleType()) {
            strategyIntegrationTestModel.setCreateTimeStart(DateUtils.yesterday());
            strategyIntegrationTestModel.setCreateTimeEnd(DateUtils.today());
        }
        next(strategyIntegrationTestModel, adminCandidateStrategy);
    }
}
