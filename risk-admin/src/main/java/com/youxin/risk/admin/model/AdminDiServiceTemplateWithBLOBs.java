package com.youxin.risk.admin.model;

public class AdminDiServiceTemplateWithBLOBs extends AdminDiServiceTemplate {
    /**
     * 
     */
    private static final long serialVersionUID = 7834674451362896694L;

    private String reqTemplate;

    private String reqHeaderTempate;

    private String resTempate;

    private String resCheckTempate;

    public String getReqTemplate() {
        return reqTemplate;
    }

    public void setReqTemplate(String reqTemplate) {
        this.reqTemplate = reqTemplate == null ? null : reqTemplate.trim();
    }

    public String getReqHeaderTempate() {
        return reqHeaderTempate;
    }

    public void setReqHeaderTempate(String reqHeaderTempate) {
        this.reqHeaderTempate = reqHeaderTempate == null ? null : reqHeaderTempate.trim();
    }

    public String getResTempate() {
        return resTempate;
    }

    public void setResTempate(String resTempate) {
        this.resTempate = resTempate == null ? null : resTempate.trim();
    }

    public String getResCheckTempate() {
        return resCheckTempate;
    }

    public void setResCheckTempate(String resCheckTempate) {
        this.resCheckTempate = resCheckTempate == null ? null : resCheckTempate.trim();
    }
}