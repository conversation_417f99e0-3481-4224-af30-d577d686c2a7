package com.youxin.risk.admin.service.ruleEngine.impl;

import com.youxin.risk.admin.model.ruleEngine.*;
import com.youxin.risk.admin.service.ruleEngine.*;
import com.youxin.risk.admin.service.rulescore.MoreTransaction;
import com.youxin.risk.admin.vo.ruleEngine.StrategyIntegrationTestModel;
import com.youxin.risk.admin.vo.ruleEngine.StrategySingleTestModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ProjectExecuteServiceImpl implements ProjectExecuteService  {
    private static final Logger logger = LoggerFactory.getLogger(ProjectExecuteServiceImpl.class);

    @Autowired
    private SingleExecuteStrategy singleExecuteStrategy;
    @Autowired
    private IntegrationExecuteStrategy integrationExecuteStrategy;

    @Override
    public Map<String, Object> getStrategyInput(Map<String, Object> map) {
        return singleExecuteStrategy.getStrategyInput(map);
    }

    @Override
    public SingleTestResultVo singleExecute(StrategySingleTestModel strategySingleTestModel) {
        return singleExecuteStrategy.execute(strategySingleTestModel);
    }

    @Override
    @MoreTransaction(value = {"adminTransactionManager", "rmTransactionManager"})
    public void integrationExecute(StrategyIntegrationTestModel strategyIntegrationTestModel) {
        integrationExecuteStrategy.execute(strategyIntegrationTestModel);
    }
}