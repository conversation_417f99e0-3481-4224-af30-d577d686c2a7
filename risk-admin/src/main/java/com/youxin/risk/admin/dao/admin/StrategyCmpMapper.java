package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.domain.ruleengine.StrategyCmpExample;
import com.youxin.risk.admin.model.ruleEngine.StrategyCmpNew;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StrategyCmpMapper {
    long countByExample(StrategyCmpExample example);

    int deleteByExample(StrategyCmpExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StrategyCmpNew record);

    int insertWithId(StrategyCmpNew record);

    int insertSelective(StrategyCmpNew record);

    List<StrategyCmpNew> selectByExample(StrategyCmpExample example);

    StrategyCmpNew selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StrategyCmpNew record, @Param("example") StrategyCmpExample example);

    int updateByExample(@Param("record") StrategyCmpNew record, @Param("example") StrategyCmpExample example);

    int updateByPrimaryKeySelective(StrategyCmpNew record);

    int updateByPrimaryKey(StrategyCmpNew record);

    List<StrategyCmpNew> selectRunningAndSubmitDataWarehouse();
}