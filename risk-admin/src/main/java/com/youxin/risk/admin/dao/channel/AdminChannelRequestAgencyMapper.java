package com.youxin.risk.admin.dao.channel;

import java.util.List;

import com.youxin.risk.admin.model.AdminChannelRequestAgency;
import com.youxin.risk.admin.vo.AdminChannelRequestAgencyVo;

public interface AdminChannelRequestAgencyMapper {

    List<AdminChannelRequestAgency> selectAllChannelRequestAgency(AdminChannelRequestAgencyVo request);

    int selectChannelRequestAgencyTotal(AdminChannelRequestAgencyVo request);

    AdminChannelRequestAgency selectChannelRequestAgencyDetail(Long id);

    int deleteByPrimaryKey(Long id);
}
