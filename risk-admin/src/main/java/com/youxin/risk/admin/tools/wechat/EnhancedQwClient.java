package com.youxin.risk.admin.tools.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.tools.wechat.common.QwEnum;
import com.youxin.risk.admin.tools.wechat.model.QwAccessTokenVo;
import com.youxin.risk.admin.tools.wechat.model.QwBaseVo;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 增强版企业微信客户端
 * 重构后的QwClient，提升代码质量和可维护性
 * 

 */
@Component
public class EnhancedQwClient {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedQwClient.class);

    private static final String GET_TOKEN_URL = "/gettoken";
    private static final String SEND_MESSAGE_URL = "/message/send";

    /**
     * 企业微信地址
     */
    @Value(value = "${qw.url:https://qyapi.weixin.qq.com/cgi-bin}")
    private String qwUrl;

    @Autowired
    private RestTemplateService restTemplateService;

    /**
     * 使用线程安全的ConcurrentHashMap存储token
     */
    private final Map<QwEnum, TokenInfo> tokenCache = new ConcurrentHashMap<>();

    /**
     * Token信息内部类
     */
    private static class TokenInfo {
        private final String accessToken;
        private final long expireTime;

        public TokenInfo(String accessToken, int expiresIn) {
            this.accessToken = accessToken;
            // 提前5分钟过期，避免边界情况
            this.expireTime = System.currentTimeMillis() + (expiresIn - 300) * 1000L;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() >= expireTime;
        }
    }

    /**
     * 获取access_token（默认企业环境）
     * 
     * @return access_token
     */
    public String getAccessToken() {
        return getAccessToken(null);
    }

    /**
     * 根据企业环境获取access_token
     * 
     * @param enterpriseEnv 企业环境
     * @return access_token
     */
    public String getAccessToken(String enterpriseEnv) {
        QwEnum qwEnum = QwEnum.getByEnterpriseEnv(enterpriseEnv);
        
        // 检查缓存中的token是否有效
        TokenInfo tokenInfo = tokenCache.get(qwEnum);
        if (tokenInfo != null && !tokenInfo.isExpired()) {
            return tokenInfo.getAccessToken();
        }

        // 获取新的token
        return refreshAccessToken(qwEnum);
    }

    /**
     * 刷新access_token
     * 
     * @param qwEnum 企业微信枚举
     * @return access_token
     */
    private synchronized String refreshAccessToken(QwEnum qwEnum) {
        try {
            // 双重检查锁定，避免重复请求
            TokenInfo tokenInfo = tokenCache.get(qwEnum);
            if (tokenInfo != null && !tokenInfo.isExpired()) {
                return tokenInfo.getAccessToken();
            }

            String getTokenUrl = String.format("%s%s?corpid=%s&corpsecret=%s", 
                    qwUrl, GET_TOKEN_URL, qwEnum.getCorpId(), qwEnum.getCorpSecret());
            
            LoggerProxy.info("refreshAccessToken", logger, "开始获取access_token, url={}", getTokenUrl);
            
            String response = restTemplateService.get(getTokenUrl, String.class);
            
            LoggerProxy.info("refreshAccessToken", logger, "获取access_token响应, response={}", response);
            
            QwAccessTokenVo accessTokenVo = JSON.parseObject(response, QwAccessTokenVo.class);
            
            if (QwBaseVo.SUCCESS != accessTokenVo.getErrCode()) {
                LoggerProxy.error("refreshAccessToken", logger, 
                        "获取access_token失败, errCode={}, errMsg={}", 
                        accessTokenVo.getErrCode(), accessTokenVo.getErrMsg());
                return null;
            }

            // 缓存新的token
            TokenInfo newTokenInfo = new TokenInfo(accessTokenVo.getAccessToken(), accessTokenVo.getExpiresIn());
            tokenCache.put(qwEnum, newTokenInfo);
            
            LoggerProxy.info("refreshAccessToken", logger, "获取access_token成功, expiresIn={}", 
                    accessTokenVo.getExpiresIn());
            
            return accessTokenVo.getAccessToken();

        } catch (Exception e) {
            LoggerProxy.error("refreshAccessToken", logger, "获取access_token异常", e);
            return null;
        }
    }

    /**
     * 发送企业微信消息
     * 
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendMessage(JSONObject message) {
        try {
            String accessToken = getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendMessage", logger, "获取access_token失败");
                return false;
            }

            String sendUrl = String.format("%s%s?access_token=%s", qwUrl, SEND_MESSAGE_URL, accessToken);
            
            LoggerProxy.info("sendMessage", logger, "开始发送企业微信消息, url={}, message={}", 
                    sendUrl, message.toJSONString());
            
            String response = restTemplateService.post(sendUrl, message, String.class);
            
            LoggerProxy.info("sendMessage", logger, "发送企业微信消息响应, response={}", response);
            
            JSONObject responseJson = JSON.parseObject(response);
            boolean success = responseJson.getIntValue("errcode") == 0;
            
            if (!success) {
                LoggerProxy.error("sendMessage", logger, "发送企业微信消息失败, errcode={}, errmsg={}", 
                        responseJson.getIntValue("errcode"), responseJson.getString("errmsg"));
            }
            
            return success;

        } catch (Exception e) {
            LoggerProxy.error("sendMessage", logger, "发送企业微信消息异常", e);
            return false;
        }
    }

    /**
     * 发送Markdown消息
     * 
     * @param toUser 接收用户
     * @param content Markdown内容
     * @param agentId 应用ID
     * @return 是否发送成功
     */
    public boolean sendMarkdownMessage(String toUser, String content, Integer agentId) {
        JSONObject message = new JSONObject();
        message.put("touser", toUser);
        message.put("msgtype", "markdown");
        message.put("agentid", agentId);

        JSONObject markdown = new JSONObject();
        markdown.put("content", content);
        message.put("markdown", markdown);

        return sendMessage(message);
    }

    /**
     * 发送文本消息
     * 
     * @param toUser 接收用户
     * @param content 文本内容
     * @param agentId 应用ID
     * @return 是否发送成功
     */
    public boolean sendTextMessage(String toUser, String content, Integer agentId) {
        JSONObject message = new JSONObject();
        message.put("touser", toUser);
        message.put("msgtype", "text");
        message.put("agentid", agentId);

        JSONObject text = new JSONObject();
        text.put("content", content);
        message.put("text", text);

        return sendMessage(message);
    }

    /**
     * 检查access_token是否有效
     * 
     * @param enterpriseEnv 企业环境
     * @return 是否有效
     */
    public boolean isAccessTokenValid(String enterpriseEnv) {
        QwEnum qwEnum = QwEnum.getByEnterpriseEnv(enterpriseEnv);
        TokenInfo tokenInfo = tokenCache.get(qwEnum);
        return tokenInfo != null && !tokenInfo.isExpired();
    }

    /**
     * 清理过期的token缓存
     * 每10分钟执行一次
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void clearExpiredTokens() {
        LoggerProxy.info("clearExpiredTokens", logger, "开始清理过期token");
        
        int removedCount = 0;
        for (Map.Entry<QwEnum, TokenInfo> entry : tokenCache.entrySet()) {
            if (entry.getValue().isExpired()) {
                tokenCache.remove(entry.getKey());
                removedCount++;
            }
        }
        
        LoggerProxy.info("clearExpiredTokens", logger, "清理过期token完成, removedCount={}, remainingCount={}", 
                removedCount, tokenCache.size());
    }

    /**
     * 获取缓存的token数量（用于监控）
     * 
     * @return token缓存数量
     */
    public int getCachedTokenCount() {
        return tokenCache.size();
    }
}
