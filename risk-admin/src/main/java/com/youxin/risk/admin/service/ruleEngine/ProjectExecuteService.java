package com.youxin.risk.admin.service.ruleEngine;

import com.youxin.risk.admin.model.ruleEngine.SingleTestResultVo;
import com.youxin.risk.admin.vo.ruleEngine.StrategyIntegrationTestModel;
import com.youxin.risk.admin.vo.ruleEngine.StrategySingleTestModel;

import java.util.Map;

public interface ProjectExecuteService {
    /**
     * 单笔测试
     */
    SingleTestResultVo singleExecute(StrategySingleTestModel strategySingleTestModel);

    /**
     * 集成测试
     */
    void integrationExecute(StrategyIntegrationTestModel strategyIntegrationTestModel) throws Exception;

    /**
     * 获取策略入参数据
     */
    Map<String,Object> getStrategyInput(Map<String, Object> map);
}
