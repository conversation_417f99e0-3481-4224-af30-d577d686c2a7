/*
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.Caesar;
import com.yonxin.risk.approve.client.model.ApproveCallbackReq;
import com.yonxin.risk.approve.client.model.enums.ApproveStatus;
import com.youxin.risk.admin.annotation.BusinessIsolation;
import com.youxin.risk.admin.constants.StopTypeEnum;
import com.youxin.risk.admin.dao.rm.AdminFeatureMapper;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminStrategyExperiment;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.model.StrategyExperimentCompareVo;
import com.youxin.risk.admin.model.vo.AdminStrategyExperimentVo;
import com.youxin.risk.admin.service.AdminStrategyCandidateService;
import com.youxin.risk.admin.service.AdminStrategyExperimentService;
import com.youxin.risk.admin.utils.CodecUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.StrategyExperimentResultVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-12-29
 */
@Controller
@RequestMapping("/strategyExperiment")
public class AdminStrategyExperimentController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AdminStrategyExperimentController.class);

    @Resource
    private AdminStrategyExperimentService adminStrategyExperimentService;

    @Resource
    private AdminFeatureMapper adminFeatureMapper;

    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/selectPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        if (!params.containsKey("operator") || StringUtils.isEmpty(params.getString("operator"))) {
            params.put("operator", UserInfoUtil.getUsername());
        } else if ("all".equals(params.getString("operator"))) {
            params.put("operator", null);
        }
        Page<AdminStrategyExperimentVo> page = adminStrategyExperimentService.selectVoPage(params);
        return buildSuccessResponse(page);
    }

    /**
     * save or update
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> save(@RequestBody List<AdminStrategyExperiment> request) {
        String dateFormat = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String userName = UserInfoUtil.getUsername();
        for (AdminStrategyExperiment item : request) {
            // case save
            if (item.getId() == null) {
                String expCode = item.getExpCode() + "_" + item.getOnlineStrategyCode();
                if (adminStrategyExperimentService.checkExists(expCode)) {
                    return buildErrorResponse(expCode + " expCode is exists");
                }
                item.setExpCode(expCode);
                item.setOperator(userName);
                Assert.notNull(item.getStopType(),"停止类型不可为空！");
                if (StopTypeEnum.COUNT == item.getStopType()) {
                    Assert.state(item.getExpNumber() <= 10000 && item.getExpNumber() >= 2000, "镜像数量需在[2000,10000]之间！");
                    long currentTimeMillis = System.currentTimeMillis();
                    item.setBeginTime(new Date(currentTimeMillis));
                    item.setEndTime(new Date(currentTimeMillis + 3 * 24 * 60 * 60 * 1000));
                } else if (StopTypeEnum.TIMER == item.getStopType()) {
                    item.setExpNumber(0);
                }
            }
            item.setGroupName(item.getGroupName() + dateFormat);
//            adminStrategyExperimentService.save(item);
            //状态起停的时候 需要做计算和更新
            adminStrategyExperimentService.saveOrUpdate(item);
        }
        return buildSuccessResponse(1);
    }

    /**
     * 审批回调地址
     */
    @ResponseBody
    @RequestMapping(value = "/approveCallback", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> approveCallback(@RequestBody ApproveCallbackReq callbackReq) {
        logger.info("approveCallback info params={}", JSON.toJSONString(callbackReq));
        // 审批状态不是通过，不处理
        if (ApproveStatus.PASSED != callbackReq.getApproveResult()) {
            return buildSuccessResponse(1);
        }
        String callbackParams = callbackReq.getCallbackParams();
        Assert.hasText(callbackParams,"callbackMsg不可为空");
        adminStrategyExperimentService.saveOrUpdate(JSONObject.parseObject(callbackParams,
                AdminStrategyExperiment.class));
        return buildSuccessResponse(1);
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> delete(@RequestBody Set<Long> ids) {
        for (Long id : ids) {
            AdminStrategyExperiment adminStrategyExperiment = adminStrategyExperimentService.get(id);
            if (adminStrategyExperiment == null) {
                return buildErrorResponse("实验编码不存在");
            }
            adminStrategyExperimentService.delete(id);
        }
        return buildSuccessResponse(1);
    }

    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseEntity<?> get(@RequestBody JSONObject params) {
        Long id = params.getLong("id");
        AdminStrategyExperiment adminStrategyExperiment = adminStrategyExperimentService.get(id);
        return buildSuccessResponse(adminStrategyExperiment);
    }

    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/selectExperimentResultPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectExperimentResultPage(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildErrorResponse("实验编码为空");
        }
        String loanKey = params.getString("loanKey");
        //当loanKey不为空时，如loanKey包含_mirror 则直接查，否则
        if (StringUtils.isNotBlank(loanKey)) {
            if (loanKey.contains("_mirror")) {
                return buildSuccessResponse(adminStrategyExperimentService.selectExperimentResultPage(params));
            }
            Page<StrategyExperimentResultVo> page = adminStrategyExperimentService.selectExperimentResultPage(params);
            if (CollectionUtils.isEmpty(page.getList())) {
                loanKey = loanKey + "_mirror";
                params.put("loanKey", loanKey);
                page = adminStrategyExperimentService.selectExperimentResultPage(params);
            }
            return buildSuccessResponse(page);
        }
        Page<StrategyExperimentResultVo> page = adminStrategyExperimentService.selectExperimentResultPage(params);
        return buildSuccessResponse(page);
    }
    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/getExperimentResult", method = RequestMethod.POST)
    public ResponseEntity<?> getExperimentResult(@RequestBody JSONObject params) {
        String id = params.getString("id");
        StrategyExperimentResultVo vo = adminStrategyExperimentService.getExperimentResult(id);
        return buildSuccessResponse(vo);
    }
    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/getExperimentInfoData", method = RequestMethod.POST)
    public ResponseEntity<?> getExperimentInfoData(@RequestBody JSONObject params) {
        String id = params.getString("id");
        String infoType = params.getString("infoType");
        StrategyExperimentResultVo vo = adminStrategyExperimentService.getExperimentInfoData(id,infoType);
        //脱敏
        if(StringUtils.isNotEmpty(vo.getRequest())){
            vo.setRequest(Caesar.encrypt(vo.getRequest()).replaceAll(System.lineSeparator(),""));
        }
        if(StringUtils.isNotEmpty(vo.getResponse())){
            vo.setResponse(CodecUtils.encodeText(vo.getResponse()));
            vo.setResponse(Caesar.encrypt(vo.getResponse()).replaceAll(System.lineSeparator(),""));
        }
        return buildSuccessResponse(vo);
    }
    @ResponseBody
    @RequestMapping(value = "/getDistinctStepByExpCode", method = RequestMethod.POST)
    public ResponseEntity<?> getDistinctStepByExpCode(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        return buildSuccessResponse(adminStrategyExperimentService.getDistinctStepByExpCode(expCode));
    }


    @ResponseBody
    @RequestMapping(value = "/exportExperimentResult", method = RequestMethod.POST)
    public ResponseEntity<?> exportExperimentResult(@RequestBody JSONObject params, HttpServletResponse response, HttpServletRequest request) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildSuccessResponse("实验编码为空");
        }
        params.put("pageSize",10000);
        List<StrategyExperimentCompareVo> list = adminStrategyExperimentService.exportExperimentResultList(params);

        return buildSuccessResponse(list);
    }

    /**
     * 增加策略集成比对结果导出
     * @param params
     * @param response
     * @param request
     */
    @RequestMapping(value = "/exportExperimentResultNew", method = RequestMethod.POST)
    public void exportExperimentResultNew(@RequestBody JSONObject params, HttpServletResponse response, HttpServletRequest request) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            throw new RuntimeException("实验编码为空");
        }
        response.setContentType("multipart/form-data;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" +  expCode+ ".xlsx");
        try {
            adminStrategyExperimentService.exportExperimentResultNew(params, response.getOutputStream());
        } catch (Exception e) {
            logger.error("exportExperimentResultNew error", e);
            throw new RuntimeException("集成比对结果导出失败: " + e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getAllFeaturePaths", method = RequestMethod.POST)
    public ResponseEntity<?> getAllFeaturePaths() {
        return buildSuccessResponse(adminFeatureMapper.getAllFeaturePaths());
    }

    @ResponseBody
    @RequestMapping(value = "/getStatisticsResult", method = RequestMethod.POST)
    public ResponseEntity<?> getStatisticsResult(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildSuccessResponse("实验编码为空");
        }
        return buildSuccessResponse(adminStrategyExperimentService.getStatisticsResult(params));
    }

    @ResponseBody
    @RequestMapping(value = "/calStatisticsResult", method = RequestMethod.POST)
    public ResponseEntity<?> calStatisticsResult(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildSuccessResponse("实验编码为空");
        }
        LoggerProxy.info("calStatisticsResult", logger, "expCode", expCode);
        AdminStrategyExperiment adminStrategyExperiment = new AdminStrategyExperiment();
        adminStrategyExperiment.setExpCode(expCode);
        this.adminStrategyExperimentService.updateStrategyExperimentStatus(adminStrategyExperiment);
//        new Thread(() -> this.adminStrategyExperimentService.updateStrategyExperimentStatus(adminStrategyExperiment)).start();
        return buildSuccessResponse("data");
    }

}