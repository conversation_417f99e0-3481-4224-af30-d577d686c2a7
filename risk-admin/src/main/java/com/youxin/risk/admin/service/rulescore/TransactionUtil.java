package com.youxin.risk.admin.service.rulescore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.dao.admin.RuleCandidateScoreMapper;
import com.youxin.risk.admin.dao.admin.RuleScoreBackTrackMapper;
import com.youxin.risk.admin.dto.rulescore.RuleScoreDTO;
import com.youxin.risk.admin.dto.rulescore.RuleScoreStrategyDTO;
import com.youxin.risk.admin.model.rulescore.RuleCandidateScore;
import com.youxin.risk.admin.model.rulescore.RuleScoreBackTrack;
import com.youxin.risk.admin.model.rulescore.RuleScoreStrategy;
import com.youxin.risk.admin.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.youxin.risk.admin.model.rulescore.RuleCandidateScore.ruleStatusFail;
import static com.youxin.risk.admin.model.rulescore.RuleScoreBackTrack.BACK_FAIL;
import static com.youxin.risk.admin.utils.DateUtils.YYYY_MM_DD_0;
import static com.youxin.risk.admin.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Component
@Slf4j
/**
 * 事务管理器
 */
public class TransactionUtil {
    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private  RuleCandidateScoreMapper ruleCandidateScoreMapper;
    @Autowired
    private RuleScoreBackTrackMapper ruleScoreBackTrackMapper;

    public void doRuleScoreBackAfterTransAction(String backTrackCode,List<RuleScoreStrategyDTO> strategyDTOList,String userKey) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void suspend() {

            }

            @Override
            public void resume() {

            }

            @Override
            public void flush() {

            }

            @Override
            public void beforeCommit(boolean b) {

            }

            @Override
            public void beforeCompletion() {

            }

            @Override
            public void afterCommit() {
                RuleScoreBackTrack ruleScoreBackTrack = ruleScoreBackTrackMapper.findBackTrack(backTrackCode);
                String ruleKey = ruleScoreBackTrack.getRuleKey();
                Integer ruleVersion = ruleScoreBackTrack.getRuleVersion();
                log.info("doRuleScoreBackAfterTransAction = {}", JSON.toJSONString(ruleScoreBackTrack));
                try {
                    Long id = ruleScoreBackTrack.getId();
                    Integer compareRuleVersion = ruleScoreBackTrack.getCompareRuleVersion();
                    List<String> nodes = strategyDTOList.stream().map(RuleScoreStrategyDTO::getNode)
                            .collect(Collectors.toList());
                    Date backStartTime = ruleScoreBackTrack.getBackStartTime();
                    Date backEndTime = ruleScoreBackTrack.getBackEndTime();
                    String startTime = DateUtils.dateTime(backStartTime);
                    String endTime = DateUtils.dateTime(backEndTime);
                    Integer backTrackCount = ruleScoreBackTrack.getBackTrackCount();
                    String compareField = ruleScoreBackTrack.getCompareField();
                    String createStartTime = DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS,backStartTime);
                    String createEndTime = DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, backEndTime);
                    if (compareRuleVersion == null) {
                        log.info("integrationWork id ={} compareRuleVersion = {}",id,compareRuleVersion);
                        /** 调用集成测试的接口 **/
                        RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, ruleVersion);

                        String response = integrationService.integrationWork(id,ruleKey, ruleVersion
                                , nodes, ruleCandidateScore.getCode(), backTrackCount
                                , startTime, endTime,userKey,createStartTime,createEndTime);
                        JSONObject jsonObject = JSONObject.parseObject(response);
                        JSONObject data = jsonObject.getJSONObject("data");
                        String taskId = data.getString("task_id");
                        String taskUrl = data.getString("development_center_url");
                        String resultTableName = data.getString("result_table_name");
                        ruleScoreBackTrackMapper.updateTask(ruleKey, ruleVersion, backTrackCode,new Date(), taskId, taskUrl, resultTableName, null, null);
                    } else {
                        log.info("backTrackWork id ={} compareRuleVersion ={}",id,compareRuleVersion);

                        String response = integrationService.backTrackWork(id,ruleKey, ruleVersion, compareRuleVersion, nodes, compareField, backTrackCount, startTime, endTime, userKey,createStartTime,createEndTime);

                        log.info("backTrackWork_response ruleKey = {} version = {} response ={}" ,ruleKey,ruleVersion,response);
                        JSONObject jsonObject = JSONObject.parseObject(response);
                        JSONObject data = jsonObject.getJSONObject("data");
                        String taskId = data.getString("task_id");
                        String taskUrl = data.getString("development_center_url");
                        String resultTableName = data.getString("result_table_name");
                        String diffResultTableName = data.getString("diff_result_table_name");
                        ruleScoreBackTrackMapper.updateTask(ruleKey, ruleVersion,backTrackCode,new Date(), taskId, null, resultTableName, taskUrl, diffResultTableName);
                    }
                }catch (Exception e) {
                    ruleScoreBackTrackMapper.updateStatus(ruleKey,ruleVersion,backTrackCode,BACK_FAIL,new Date(),userKey);
                    log.error("backTrackWork error",e);
                }
            }
            @Override
            public void afterCompletion(int i) {

            }
        });
    }

    public void doRuleCandidateTransAction(Integer version, String userKey, RuleScoreDTO ruleScoreDTO, String runCode, List<RuleScoreStrategy> ruleScoreStrategyList) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void suspend() {

            }

            @Override
            public void resume() {

            }

            @Override
            public void flush() {

            }

            @Override
            public void beforeCommit(boolean b) {

            }

            @Override
            public void beforeCompletion() {

            }

            @Override
            public void afterCommit() {
                String ruleKey = ruleScoreDTO.getRuleKey();
                try {
                    List<String> nodes = ruleScoreStrategyList.stream().map(RuleScoreStrategy::getNode)
                            .collect(Collectors.toList());
                    LocalDate now = LocalDate.now();
                    LocalDate yesterdayDate = now.minusDays(1);
                    RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, version);

                    String createStartTime = DateUtils.parseDateToStr(YYYY_MM_DD_0,new Date());
                    Date yesDate = DateUtils.minusOneDay(new Date());
                    String endStartTime = DateUtils.parseDateToStr(YYYY_MM_DD_0,yesDate);

                    String response = integrationService.integrationWork(ruleCandidateScore.getId(),ruleKey, version, nodes, runCode, 1000,yesterdayDate.toString(),yesterdayDate.toString(),userKey,createStartTime,endStartTime);
                    log.info("doRuleCandidateTransAction = {} response ={}", JSON.toJSONString(ruleCandidateScore),response);
                    JSONObject jsonObject = JSONObject.parseObject(response);
                    JSONObject data = jsonObject.getJSONObject("data");
                    String taskId = data.getString("task_id");
                    String taskUrl = data.getString("development_center_url");
                    String resultTableName = data.getString("result_table_name");
                    ruleCandidateScoreMapper.updateRuleCandidateScoreTask(ruleKey,version,new Date(),taskId,taskUrl,resultTableName);
                }catch (Exception e){
                    ruleCandidateScoreMapper.updateStatus(ruleKey,version,ruleStatusFail,new Date(),userKey);
                    log.error("integrationWork error",e);
                }
            }
            @Override
            public void afterCompletion(int i) {

            }
        });
    }
}
