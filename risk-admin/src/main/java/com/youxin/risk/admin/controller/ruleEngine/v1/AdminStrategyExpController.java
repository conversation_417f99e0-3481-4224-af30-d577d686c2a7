package com.youxin.risk.admin.controller.ruleEngine.v1;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.Caesar;
import com.yonxin.risk.approve.client.model.ApproveCallbackReq;
import com.yonxin.risk.approve.client.model.enums.ApproveStatus;
import com.youxin.risk.admin.constants.StrategyExpStatusEnum;
import com.youxin.risk.admin.constants.StrategyExpStopTypeEnum;
import com.youxin.risk.admin.controller.BaseController;
import com.youxin.risk.admin.dao.rm.AdminFeatureMapper;
import com.youxin.risk.admin.dto.JsonResult;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminStrategyExp;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.ruleEngine.AdminStrategyExpService;
import com.youxin.risk.admin.utils.CodecUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.StrategyExperimentResultVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;

@Controller
@RequestMapping("/strategy_exp/v1")
public class AdminStrategyExpController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AdminStrategyExpController.class);

    @Resource
    private AdminStrategyExpService adminStrategyExpService;
    @Resource
    private RedisLock redisLock;
    @Resource
    private AdminFeatureMapper adminFeatureMapper;

    @ResponseBody
    @RequestMapping(value = "/select_page", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        Page<AdminStrategyExp> page = adminStrategyExpService.selectVoPage(params);
        return buildSuccessResponse(page);
    }

    /**
     * save or update
     */
    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> save(@RequestBody AdminStrategyExp item) {
        String userName = UserInfoUtil.getUsername();
        // id为空表示新增
        if (item.getId() == null) {
            if (adminStrategyExpService.checkExists(item.getExpCode())) {
                return buildErrorResponse(item.getExpCode() + " expCode is exists");
            }
            if (StrategyExpStopTypeEnum.COUNT.name().equals(item.getStopType())) {
                Assert.state(item.getExpNumber() <= 10000
                        && item.getExpNumber() >= 2000, "镜像数量需在[2000,10000]之间！");
                long currentTimeMillis = System.currentTimeMillis();
                item.setBeginTime(new Date(currentTimeMillis));
                item.setEndTime(new Date(currentTimeMillis + 3 * 24 * 60 * 60 * 1000));
            } else if (StrategyExpStopTypeEnum.TIMER.name().equals(item.getStopType())) {
                item.setExpNumber(0);
            }
            // 默认启用
            item.setStatus(StrategyExpStatusEnum.ENABLE.name());
            item.setCreateUser(userName);
        }
        item.setModifyUser(userName);
        //状态起停的时候 需要做计算和更新
        adminStrategyExpService.saveOrUpdate(item);
        return buildSuccessResponse(1);
    }

    /**
     * 审批回调地址
     */
    @ResponseBody
    @RequestMapping(value = "/approve_callback", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> approveCallback(@RequestBody ApproveCallbackReq callbackReq) {
        logger.info("approveCallback info params={}", JSON.toJSONString(callbackReq));
        // 审批状态不是通过，不处理
        if (ApproveStatus.PASSED != callbackReq.getApproveResult()) {
            return buildSuccessResponse(1);
        }
        String callbackParams = callbackReq.getCallbackParams();
        Assert.hasText(callbackParams,"callbackMsg不可为空");
        adminStrategyExpService.saveOrUpdate(JSONObject.parseObject(callbackParams,
                AdminStrategyExp.class));
        return buildSuccessResponse(1);
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> delete(@RequestBody JSONObject params) {
        Long id = params.getLong("id");
        AdminStrategyExp adminStrategyExperiment = adminStrategyExpService.get(id);
        if (adminStrategyExperiment == null) {
            return buildErrorResponse("实验编码不存在");
        }
        adminStrategyExpService.delete(id);
        return buildSuccessResponse(1);
    }

    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseEntity<?> get(@RequestBody JSONObject params) {
        Long id = params.getLong("id");
        AdminStrategyExp adminStrategyExperiment = adminStrategyExpService.get(id);
        return buildSuccessResponse(adminStrategyExperiment);
    }

    @ResponseBody
    @RequestMapping(value = "/select_experiment_result_page", method = RequestMethod.POST)
    public ResponseEntity<?> selectExperimentResultPage(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildErrorResponse("实验编码为空");
        }
        String loanKey = params.getString("loanKey");
        //当loanKey不为空时，如loanKey包含_mirror 则直接查，否则
        if (StringUtils.isNotBlank(loanKey)) {
            if (loanKey.contains("_mirror")) {
                return buildSuccessResponse(adminStrategyExpService.selectExperimentResultPage(params));
            }
            Page<StrategyExperimentResultVo> page = adminStrategyExpService.selectExperimentResultPage(params);
            if (CollectionUtils.isEmpty(page.getList())) {
                loanKey = loanKey + "_mirror";
                params.put("loanKey", loanKey);
                page = adminStrategyExpService.selectExperimentResultPage(params);
            }
            return buildSuccessResponse(page);
        }
        Page<StrategyExperimentResultVo> page = adminStrategyExpService.selectExperimentResultPage(params);
        return buildSuccessResponse(page);
    }


    @ResponseBody
    @RequestMapping(value = "/get_experiment_result", method = RequestMethod.POST)
    public ResponseEntity<?> getExperimentResult(@RequestBody JSONObject params) {
        String id = params.getString("id");
        StrategyExperimentResultVo vo = adminStrategyExpService.getExperimentResult(id);
        return buildSuccessResponse(vo);
    }

    @ResponseBody
    @RequestMapping(value = "/get_experiment_info_data", method = RequestMethod.POST)
    public ResponseEntity<?> getExperimentInfoData(@RequestBody JSONObject params) {
        String id = params.getString("id");
        String infoType = params.getString("infoType");
        StrategyExperimentResultVo vo = adminStrategyExpService.getExperimentInfoData(id,infoType);
        //脱敏
        if(StringUtils.isNotEmpty(vo.getRequest())){
            vo.setRequest(Caesar.encrypt(vo.getRequest()).replaceAll(System.lineSeparator(),""));
        }
        if(StringUtils.isNotEmpty(vo.getResponse())){
            vo.setResponse(CodecUtils.encodeText(vo.getResponse()));
            vo.setResponse(Caesar.encrypt(vo.getResponse()).replaceAll(System.lineSeparator(),""));
        }
        return buildSuccessResponse(vo);
    }

    @ResponseBody
    @RequestMapping(value = "/get_distinct_step_by_exp_code", method = RequestMethod.POST)
    public ResponseEntity<?> getDistinctStepByExpCode(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        return buildSuccessResponse(adminStrategyExpService.getDistinctStepByExpCode(expCode));
    }


    @ResponseBody
    @RequestMapping(value = "/export_experiment_result", method = RequestMethod.POST)
    public JsonResult exportExperimentResult(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return JsonResult.error("实验编码为空");
        }
        params.put("pageSize",10000);
        LockResult lockResult = null;
        try {
            lockResult = redisLock.tryLock("exportExperimentResult", 600);
            if (!lockResult.isSuccess()) {
                return JsonResult.error("刚刚多人同时操作，请等待600s后，重新点击导出按钮!");
            }
            String base64EncodedCsv = adminStrategyExpService.exportExperimentResultList(params);
            JSONObject data = new JSONObject();
            data.put("data", base64EncodedCsv);
            data.put("fileName", String.format("策略镜像结果_%s.xlsx", expCode));
            return JsonResult.success(data);
        } catch (Exception e) {
            logger.error("exportExperimentResult error", e);
            return JsonResult.error("策略镜像结果下载失败: " + e.getMessage());
        }finally {
            if (lockResult != null) {
                redisLock.releaseLock("exportExperimentResult", lockResult.getLockId());
            }
        }
    }

    /**
     * 增加策略集成比对结果导出
     * @param params
     */
    @ResponseBody
    @RequestMapping(value = "/export_experiment_result_diff", method = RequestMethod.POST)
    public JsonResult exportExperimentResultDiff(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return JsonResult.error("实验编码为空");
        }

        LockResult result = null;
        try {
            result = redisLock.tryLock("exportExperimentResultDiff", 600);
            if(!result.isSuccess()){
                return JsonResult.error("刚刚多人同时操作，请等待600s后，重新点击导出按钮!");
            }
            String base64EncodedCsv = adminStrategyExpService.exportExperimentResultDiff(params);
            JSONObject data = new JSONObject();
            data.put("data", base64EncodedCsv);
            data.put("fileName", String.format("策略比对镜像结果_%s.xlsx", expCode));
            return JsonResult.success(data);
        } catch (Exception e) {
            logger.error("exportExperimentResultNew error", e);
            return JsonResult.error("集成比对结果导出失败: " + e.getMessage());
        }finally {
            if (result != null && result.isSuccess()){
                redisLock.releaseLock("exportExperimentResultDiff", result.getLockId());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/get_all_feature_paths", method = RequestMethod.POST)
    public ResponseEntity<?> getAllFeaturePaths() {
        return buildSuccessResponse(adminFeatureMapper.getAllFeaturePaths());
    }

    @ResponseBody
    @RequestMapping(value = "/get_statistics_result", method = RequestMethod.POST)
    public ResponseEntity<?> getStatisticsResult(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildSuccessResponse("实验编码为空");
        }
        return buildSuccessResponse(adminStrategyExpService.getStatisticsResult(params));
    }

    @ResponseBody
    @RequestMapping(value = "/cal_statistics_result", method = RequestMethod.POST)
    public ResponseEntity<?> calStatisticsResult(@RequestBody JSONObject params) {
        String expCode = params.getString("expCode");
        if (StringUtils.isBlank(expCode)) {
            return buildSuccessResponse("实验编码为空");
        }
        LoggerProxy.info("calStatisticsResult", logger, "expCode", expCode);
        AdminStrategyExp adminStrategyExp = new AdminStrategyExp();
        adminStrategyExp.setExpCode(expCode);
        this.adminStrategyExpService.updateStrategyExperimentStatus(adminStrategyExp);
        return buildSuccessResponse("data");
    }
}