/*
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.dao.BaseMapper;
import com.youxin.risk.admin.model.AdminStrategyExperiment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AdminStrategyExperimentMapper extends BaseMapper<AdminStrategyExperiment> {
    List<AdminStrategyExperiment> selectForEnable();

    List<AdminStrategyExperiment> selectForDisable();

    void batchUpdateStatus(@Param("idList") List<Long> idList, @Param("status") String status);

    List<AdminStrategyExperiment> selectPageList(Map<String, Object> params);

    long selectPageCount(Map<String, Object> params);

    List<AdminStrategyExperiment> selectByGroup(@Param("group") String group);

    /**
     *
     * @param experimentStrategyCode
     * @return
     */
    List<AdminStrategyExperiment> getAdminStrategyExperiment(@Param("experimentStrategyCode") String experimentStrategyCode);

    AdminStrategyExperiment getAdminStrategyExpByExpCode(@Param("expCode") String expCode);
}
