package com.youxin.risk.admin.service.ruleEngine;

import com.alibaba.fastjson.JSONArray;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.dto.PageInfo;
import com.youxin.risk.admin.model.rulescore.RuleCandidateScore;
import com.youxin.risk.admin.model.rulescore.RuleScore;
import com.youxin.risk.admin.vo.ruleEngine.CommitHistoryModel;
import com.youxin.risk.admin.vo.ruleEngine.StrategyNodeVo;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 策略节点服务
 */
public interface StrategyNodeService {

    /**
     * 策略代码查询
     * @param projectId
     * @param nodeName
     * @return
     */
    String queryCode(Integer projectId, String nodeName);

    /**
     * 查询所有策略代码
     * @param projectId
     * @return
     */
    Map<String, String> queryAllCode(Integer projectId);

    /**
     * 查询最终代码
     * @param projectId
     * @return
     */
    Map<String, String> getFinalCode(Integer projectId);

    /**
     * 集成测试的最终代码
     */
    Map<String, String> getFinalCodeForIntegrationTest(Integer projectId);

    /**
     * 策略回溯的最终代码
     * @param projectId
     * @param adminCandidateStrategy
     * @return
     */
    Map<String, String> getFinalCodeForCmpIntegrationTest(Integer projectId, AdminCandidateStrategy adminCandidateStrategy);

    /**
     * 策略代码更新
     */
    void updateCode(StrategyNodeVo strategyNodeQuery);


    /**
     * 查询策略节点历史提交记录
     */
    List<CommitHistoryModel> queryHistoryCommit(StrategyNodeVo strategyNodeQuery);

    /**
     * 根据提交ID查询策略代码
     */
    String queryCodeByCommitId(Integer commitId);


    /**
     * 根据项目id查询节点list
     */
    List<String> queryNodeList(Integer projectId);

    /**
     * 根据策略节点名称和策略类型查询可以绑定的规则分key
     */
    List<RuleScore> queryBindRuleKeyList(String strategyType, String nodeName);

    /**
     * 查询绑定的规则分key信息
     */
    PageInfo<RuleCandidateScore> queryBindRuleKeyListByNode(String strategyType, String nodeName, String ruleKey, Integer pageNum, Integer pageSize);

    /**
     * 绑定规则分
     */
    void bindRuleKey(String strategyType, String nodeName, JSONArray ruleKeys,Boolean isForce) throws RuntimeException, ParseException;

    /**
     * 规则分解除绑定
     */
    void unbindRuleKey(String strategyType, String nodeName, String ruleKey, Integer ruleVersion) throws RuntimeException;
}
