package com.youxin.risk.admin.constants;

import java.util.Arrays;
import java.util.Objects;

public enum TimeGroupEnum {
    H_1("1h","一小时"),
    H_6("6h","六小时"),
    D_1("1d","一天"),
    D_7("7d","七天"),
    M_1("1m","一分钟");
    private String code;

    private String name;

    TimeGroupEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static TimeGroupEnum getByCode(String code) {
        return Arrays.stream(TimeGroupEnum.values())
                .filter(e-> Objects.equals(e.getCode(), code))
                .findFirst().orElse(null);
    }
}
