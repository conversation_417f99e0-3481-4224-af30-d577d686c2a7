package com.youxin.risk.admin.model.cp;

import com.youxin.risk.commons.model.BaseModel;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-09-26
 * 资源统计
 */
@Data
@Builder
public class FeatureResourceStatistics extends BaseModel {
    /**
     * 特征编码
     */
    private String featureCode;
    /**
     * 事件名
     */
    private String eventCode;
    /**
     *  步骤名
     */
    private String step;
    /**
     * 特征中文名
     */
    private String featureName;
    /**
     * （倒排）tp90（ms）* 上周调用量
     */
    private String tp90LweekTotalTime;
    /**
     * 特征创建人
     */
    private String creator;
    /**
     * 所属部门
     */
    private String dept;
    /**
     * 昨日调用量
     */
    private String ydayCals;
    /**
     * 上周调用量
     */
    private String lweekCals;
    /**
     * 平均耗时（ms）
     */
    private String meanTime;
    /**
     * 上周总耗时(调用量*平均耗时)
     */
    private String lweekTotalTime;
    /**
     * 最大耗时（ms）
     */
    private String maxTime;
    /**
     * 最小耗时（ms）
     */
    private String minTime;
    /**
     * tp99（ms）
     */
    private String tp99;
    /**
     * tp90（ms）
     */
    private String tp90;
    /**
     * 0分位平均值（ms）
     */
    private String tp70;
    /**
     * 数据源大小分布TP90
     */
    private String datasourceTP90;
    /**
     * 数据源大小分布TP99
     */
    private String datasourceTP99;
    /**
     * 数据源大小分布MAX
     */
    private String datasourceMax;
    /**
     * 特征最后一次上线时间
     */
    private String lastOnlineTime;
    /**
     * 资源占比
     */
    private BigDecimal resourcePercent;
    
}
