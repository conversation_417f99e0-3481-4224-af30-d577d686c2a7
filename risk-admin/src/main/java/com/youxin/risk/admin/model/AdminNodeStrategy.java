package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

/**
 * <AUTHOR>
 * @date 2018/11/14 20:35
 */
public class AdminNodeStrategy extends BaseModel {
    private static final long serialVersionUID = 592276379236124149L;

    private String nodeCode;
    private String strategyCode;
    private String type;

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getStrategyCode() {
        return strategyCode;
    }

    public void setStrategyCode(String strategyCode) {
        this.strategyCode = strategyCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
