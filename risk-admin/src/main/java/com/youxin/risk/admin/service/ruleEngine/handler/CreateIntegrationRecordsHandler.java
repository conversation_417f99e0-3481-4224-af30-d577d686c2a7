package com.youxin.risk.admin.service.ruleEngine.handler;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.admin.constants.CandidateStrategyOpeEnum;
import com.youxin.risk.admin.constants.CandidateStrategyStatusEnum;
import com.youxin.risk.admin.dao.admin.RuleProjectMapper;
import com.youxin.risk.admin.dao.admin.StrategyTypeMapper;
import com.youxin.risk.admin.dao.rm.AdminStrategyCodeMapper;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.model.AdminStrategyCode;
import com.youxin.risk.admin.model.ruleEngine.RuleProject;
import com.youxin.risk.admin.model.ruleEngine.RuleProjectCommitHistory;
import com.youxin.risk.admin.model.ruleEngine.RuleProjectUserCommit;
import com.youxin.risk.admin.model.rulescore.RuleCandidateScore;
import com.youxin.risk.admin.model.rulescore.RuleScoreRelationOnline;
import com.youxin.risk.admin.service.ruleEngine.*;
import com.youxin.risk.admin.service.rulescore.RuleScoreService;
import com.youxin.risk.admin.tools.ruleEngine.StrategyCodeHandler;
import com.youxin.risk.admin.tools.ruleScore.RuleScoreHandler;
import com.youxin.risk.admin.utils.DateUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.admin.vo.ruleEngine.StrategyIntegrationTestModel;
import com.youxin.risk.commons.dao.fs.StrategyNodeRelationalMapper;
import com.youxin.risk.commons.model.StrategyNodeRelationalDO;
import com.youxin.risk.commons.model.StrategyTypeDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CreateIntegrationRecordsHandler extends AbstractIntegrationHandler {
    private static final Logger logger = LoggerFactory.getLogger(CreateIntegrationRecordsHandler.class);
    @Autowired
    private AdminCandidateStrategyService adminCandidateStrategyService;
    @Resource
    private RuleProjectCommitHistoryService ruleProjectCommitHistoryService;
    @Resource
    private RuleProjectUserCommitService ruleProjectUserCommitService;
    @Autowired
    private UserOperateStrategyLogService userOperateStrategyLogService;
    @Autowired
    private StrategyNodeService strategyNodeService;
    @Autowired
    private AdminStrategyCodeMapper adminStrategyCodeMapper;
    @Resource
    private StrategyNodeRelationalMapper strategyNodeRelationalMapper;
    @Resource
    private RuleProjectMapper ruleProjectMapper;
    @Resource
    private StrategyTypeMapper strategyTypeMapper;
    @Resource
    private StrategyCodeHandler strategyCodeHandler;
    @Resource
    private RuleScoreService ruleScoreService;
    @Resource
    private RuleScoreHandler ruleScoreHandler;

    @Override
    public void handle(StrategyIntegrationTestModel strategyIntegrationTestModel, AdminCandidateStrategy adminCandidateStrategy) throws Exception {
        Map<String, String> allNodeCode = strategyNodeService.getFinalCode(strategyIntegrationTestModel.getProjectId());
        // 插入策略代码记录
        Map<String, Long> nodeCodeIdMap = insertStrategyCodeRecords(allNodeCode);
        // 插入策略集成记录
        createStrategyIntegrationRecord(adminCandidateStrategy, strategyIntegrationTestModel, ruleProjectMapper.queryById(strategyIntegrationTestModel.getProjectId()), nodeCodeIdMap);
        // 增加策略与节点依赖关系记录
        createStrategyNodeRelation(adminCandidateStrategy, nodeCodeIdMap);
        userOperateStrategyLogService.logUserOperation(adminCandidateStrategy.getId(), CandidateStrategyOpeEnum.CREATE_SUCCESS);
        next(strategyIntegrationTestModel, adminCandidateStrategy);
    }

    private Map<String, Long> insertStrategyCodeRecords(Map<String, String> allNodeCode) {
        Map<String, Long> nodeCodeIdMap = new HashMap<>();
        for (Map.Entry<String, String> entry : allNodeCode.entrySet()) {
            AdminStrategyCode adminStrategyCode = new AdminStrategyCode();
            adminStrategyCode.setCreateTime(new Date());
            adminStrategyCode.setUpdateTime(new Date());
            adminStrategyCode.setStrategyCode(entry.getValue());
            adminStrategyCode.setVersion(1);
            adminStrategyCodeMapper.insertStrategyCode(adminStrategyCode);
            nodeCodeIdMap.put(entry.getKey(), adminStrategyCode.getId());
        }
        return nodeCodeIdMap;
    }

    private void createStrategyIntegrationRecord(AdminCandidateStrategy adminCandidateStrategy
            , StrategyIntegrationTestModel submit
            , RuleProject ruleProject, Map<String, Long> nodeCodeIdMap) {
        Map<String, Integer> commitIdMap = buildCommitIdMap(submit);
        adminCandidateStrategy.setGroupName(UUID.randomUUID().toString());
        adminCandidateStrategy.setProjectId(ruleProject.getId());
        adminCandidateStrategy.setProjectName(ruleProject.getProjectName());
        adminCandidateStrategy.setStrategyType(ruleProject.getStrategyType());
        List<StrategyTypeDO> strategyTypeDOS = strategyTypeMapper.listByStrategyTypeCode(ruleProject.getStrategyType());
        adminCandidateStrategy.setStrategyName(strategyTypeDOS.get(0).getStrategyTypeName());
        adminCandidateStrategy.setCodeIds(JSON.toJSONString(nodeCodeIdMap));
        adminCandidateStrategy.setCommitIds(JSON.toJSONString(commitIdMap));
        adminCandidateStrategy.setStatus(CandidateStrategyStatusEnum.RUNNING.getCode());
        adminCandidateStrategy.setUserName(UserInfoUtil.getUsername());
        adminCandidateStrategy.setTestNumber(submit.getTestNumber());
        if (submit.getCreateTimeStart() != null && submit.getCreateTimeEnd() != null) {
            adminCandidateStrategy.setSampleStartTime(DateUtils.parseDate(submit.getCreateTimeStart()));
            adminCandidateStrategy.setSampleEndTime(DateUtils.parseDate(submit.getCreateTimeEnd()));
        }
        adminCandidateStrategy.setSampleSteps(submit.getNodeName());
        adminCandidateStrategy.setCreateTime(new Date());
        adminCandidateStrategy.setUpdateTime(new Date());
        // 默认变量确认按钮还没有点击
        adminCandidateStrategy.setVariableConfirmFlag((byte) 0);
        adminCandidateStrategyService.insert(adminCandidateStrategy);
    }

    private void createStrategyNodeRelation(AdminCandidateStrategy adminCandidateStrategy, Map<String, Long> nodeCodeIdMap) {
        List<StrategyNodeRelationalDO> strategyNodeRelationalDOList = new ArrayList<>();
        for (Map.Entry<String, Long> entry : nodeCodeIdMap.entrySet()) {
            String nodeName = entry.getKey();
            Long codeId = entry.getValue();

            AdminStrategyCode adminStrategyCode = adminStrategyCodeMapper.selectStrategyCode(codeId);
            // 全部使用的变量
            Set<String> variableAllList = new HashSet<>();
            // 查询策略代码使用的变量集合 并设置
            Set<String> variableList = strategyCodeHandler.queryVariableList(adminStrategyCode.getStrategyCode());
            logger.info("variableList:{}", JSON.toJSONString(variableList));
            // 正则匹配提取getRule('rule_name','score_name','None')中的rule_name
            Set<String> ruleList = ruleScoreHandler.queryRuleList(adminStrategyCode.getStrategyCode());
            logger.info("ruleList before:{}", JSON.toJSONString(ruleList));

            Set<String> ruleListNew = new HashSet<>(ruleList);
            // 遍历使用的规则分列表
            for (String ruleKey : ruleList) {
                // 查询当前规则分依赖了哪些规则分
                List<RuleScoreRelationOnline> dependRules = ruleScoreService.findRuleScoreRelationList(ruleKey);
                // 如果可以查询到依赖的规则分，那么添加到使用的规则分列表中
                if (!CollectionUtils.isEmpty(dependRules)) {
                    ruleListNew.addAll(dependRules.stream().map(RuleScoreRelationOnline::getRelationRuleKey)
                            .collect(Collectors.toList()));
                }
            }
            logger.info("ruleList after:{}", JSON.toJSONString(ruleListNew));
            // 根据节点名称查询绑定的规则集，校验是否绑定了rule_name
            List<RuleCandidateScore> bindRules = ruleScoreService
                    .findBindRuleKeyListByNode(adminCandidateStrategy.getStrategyType(), nodeName);
            List<String> bindRuleKeys = bindRules.stream().map(RuleCandidateScore::getRuleKey).collect(Collectors.toList());
            logger.info("bindRuleKeys:{}", JSON.toJSONString(bindRuleKeys));
            // 查询未绑定的规则分
            List<String> unBindRuleList = ruleListNew.stream()
                    .filter(rule -> !bindRuleKeys.contains(rule)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unBindRuleList)) {
                throw new RuntimeException("未绑定的规则分:" + JSON.toJSONString(unBindRuleList));
            }
            // 添加规则分的变量
            Set<String> usedVarsForRules = new HashSet<>();
            for (RuleCandidateScore binRule : bindRules) {
                Set<String> usedVarsForRule = ruleScoreHandler.queryUsedVarsForRule(binRule);
                logger.info("ruleKey:{} usedVarsForRule:{}", binRule.getRuleKey(), JSON.toJSONString(usedVarsForRule));
                usedVarsForRules.addAll(usedVarsForRule);
            }

            // 添加规则集使用的变量
            variableAllList.addAll(usedVarsForRules);
            // 添加策略代码使用的变量
            variableAllList.addAll(variableList);

            StrategyNodeRelationalDO strategyNodeRelationalDO = new StrategyNodeRelationalDO();
            strategyNodeRelationalDO.setStrategyNodeId(nodeName);
            strategyNodeRelationalDO.setStrategyCodeId(codeId);
            // 策略节点需要调用的变量=策略代码使用的变量+绑定规则集使用的变量
            strategyNodeRelationalDO.setVariableListStr(JSON.toJSONString(variableAllList));
            // 策略代码使用的规则集
            strategyNodeRelationalDO.setRuleScoreListStr(JSON.toJSONString(ruleList));
            // 设置策略代码使用的变量
            strategyNodeRelationalDO.setStrategyVarListStr(JSON.toJSONString(variableList));
            strategyNodeRelationalDO.setStrategyCandidateId(Long.valueOf(adminCandidateStrategy.getId()));
            strategyNodeRelationalDO.setStrategyType(adminCandidateStrategy.getStrategyType());
            strategyNodeRelationalDOList.add(strategyNodeRelationalDO);
        }

        if (!CollectionUtils.isEmpty(strategyNodeRelationalDOList)) {
            strategyNodeRelationalMapper.insertBatch(strategyNodeRelationalDOList);
        }
    }

    private Map<String, Integer> buildCommitIdMap(StrategyIntegrationTestModel submit) {
        List<RuleProjectUserCommit> ruleProjectUserCommits = ruleProjectUserCommitService
                .queryByProjectIdAndUserName(submit.getProjectId(), UserInfoUtil.getUsername());
        Map<String, Integer> commitIdMap = new HashMap<>();
        for (RuleProjectUserCommit ruleProjectUserCommit : ruleProjectUserCommits) {
            RuleProjectCommitHistory ruleProjectCommitHistory = ruleProjectCommitHistoryService
                    .queryById(ruleProjectUserCommit.getHistoryCurrentId());
            commitIdMap.put(ruleProjectUserCommit.getFileName(), ruleProjectCommitHistory.getId());
        }
        return commitIdMap;
    }

}