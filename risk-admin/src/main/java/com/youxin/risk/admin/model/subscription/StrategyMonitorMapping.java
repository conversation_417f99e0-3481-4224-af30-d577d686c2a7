package com.youxin.risk.admin.model.subscription;

import java.util.List;

/**
 * 策略监控映射配置实体类
 * 

 */
public class StrategyMonitorMapping {

    /**
     * 策略类型名称
     */
    private String name;

    /**
     * 策略级别的推送频率(分钟)
     */
    private Integer frequencyMinutes;

    /**
     * 策略级别的总推送次数
     */
    private Integer totalPushes;

    /**
     * 监控项列表
     */
    private List<MonitorConfig> monitors;

    public StrategyMonitorMapping() {
    }

    public StrategyMonitorMapping(String name, List<MonitorConfig> monitors) {
        this.name = name;
        this.monitors = monitors;
    }

    public StrategyMonitorMapping(String name, Integer frequencyMinutes, Integer totalPushes, List<MonitorConfig> monitors) {
        this.name = name;
        this.frequencyMinutes = frequencyMinutes;
        this.totalPushes = totalPushes;
        this.monitors = monitors;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MonitorConfig> getMonitors() {
        return monitors;
    }

    public void setMonitors(List<MonitorConfig> monitors) {
        this.monitors = monitors;
    }

    public Integer getFrequencyMinutes() {
        return frequencyMinutes;
    }

    public void setFrequencyMinutes(Integer frequencyMinutes) {
        this.frequencyMinutes = frequencyMinutes;
    }

    public Integer getTotalPushes() {
        return totalPushes;
    }

    public void setTotalPushes(Integer totalPushes) {
        this.totalPushes = totalPushes;
    }

    /**
     * 获取有效的推送频率（分钟）
     * 优先使用策略级别配置，如果没有则使用默认值
     *
     * @param defaultFrequency 默认频率
     * @return 有效的推送频率
     */
    public int getEffectiveFrequencyMinutes(int defaultFrequency) {
        return frequencyMinutes != null && frequencyMinutes > 0 ? frequencyMinutes : defaultFrequency;
    }

    /**
     * 获取有效的总推送次数
     * 优先使用策略级别配置，如果没有则使用默认值
     *
     * @param defaultTotalPushes 默认总推送次数
     * @return 有效的总推送次数
     */
    public int getEffectiveTotalPushes(int defaultTotalPushes) {
        return totalPushes != null && totalPushes > 0 ? totalPushes : defaultTotalPushes;
    }

    @Override
    public String toString() {
        return "StrategyMonitorMapping{" +
                "name='" + name + '\'' +
                ", frequencyMinutes=" + frequencyMinutes +
                ", totalPushes=" + totalPushes +
                ", monitors=" + monitors +
                '}';
    }
}
