package com.youxin.risk.admin.service;

import com.youxin.risk.admin.model.GwMiniRequest;

import java.util.Date;
import java.util.List;

/**
 * @description: 卡单监控表service类
 * @author: juxiang
 * @create: 2021-09-22 12:06
 **/
public interface GwMiniRequestService {

    List<GwMiniRequest> queryByEventAndTime(String eventCode, Date date,Integer limit);

    int batchUpdate(List<Long> ids,Integer queryStatus);

}
