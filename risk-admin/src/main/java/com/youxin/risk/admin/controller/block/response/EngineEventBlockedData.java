package com.youxin.risk.admin.controller.block.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-06-10
 */
@Data
@ApiModel
public class EngineEventBlockedData {

    private Long id;

    private String sessionId;

    private String eventCode;

    private String processInstanceId;

    private String processDefId;

    private String loanKey;

    private String userKey;

    private String status;

    private String currentNodeId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSSZ")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSSZ")
    private Date updateTime;

    private String errMsg;

}
