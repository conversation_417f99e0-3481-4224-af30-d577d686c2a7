package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.youxin.risk.admin.dao.admin.RuleProjectMapper;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.dto.PageInfo;
import com.youxin.risk.admin.model.ruleEngine.*;
import com.youxin.risk.admin.service.AdminProcessDefinitionService;
import com.youxin.risk.admin.service.AdminStrategyTypeService;
import com.youxin.risk.admin.service.ruleEngine.AdminCandidateStrategyService;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectCommitHistoryService;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectService;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectUserCommitService;
import com.youxin.risk.admin.tools.ruleEngine.StrategyCodeHandler;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.admin.vo.ruleEngine.*;
import com.youxin.risk.commons.model.ProcessDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 新决策引擎项目表(RuleProject)表服务实现类
 */
@Service
public class RuleProjectServiceImpl implements RuleProjectService {
    private Logger logger = LoggerFactory.getLogger(RuleProjectServiceImpl.class);

    @Resource
    private RuleProjectMapper ruleProjectMapper;
    @Resource
    private RuleProjectUserCommitService ruleProjectUserCommitService;
    @Resource
    private RuleProjectCommitHistoryService ruleProjectCommitHistoryService;
    @Resource
    private StrategyCodeHandler strategyCodeHandler;
    @Resource
    private AdminProcessDefinitionService adminProcessDefinitionService;
    @Autowired
    private AdminStrategyTypeService adminStrategyTypeService;
    @Resource
    private ProjectPullServiceImpl projectPullServiceImpl;
    @Resource
    private AdminCandidateStrategyService adminCandidateStrategyService;

    /**
     * 通过ID查询单条决策引擎项目数据
     *
     * @param id 项目ID
     * @return 决策引擎项目对象
     */
    @Override
    public RuleProject getProjectById(Integer id) {
        return ruleProjectMapper.queryById(id);
    }



    /**
     * 通过项目名称查询单条决策引擎项目数据
     *
     * @param projectName 项目名称
     * @return 决策引擎项目对象
     */
    @Override
    public RuleProject getProjectByName(String projectName) {
        return ruleProjectMapper.queryByProjectName(projectName);
    }

    /**
     * 通过策略类型查询决策引擎项目列表
     *
     * @param strategyType 策略类型
     * @return 决策引擎项目列表
     */
    @Override
    public List<RuleProject> getProjectsByStrategyType(String strategyType) {
        return ruleProjectMapper.queryByStrategyType(strategyType);
    }

    /**
     * 分页查询决策引擎项目列表
     *
     * @param projectQuery 查询条件
     * @return 决策引擎项目列表分页信息
     */
    @Override
    public PageInfo<ProjectItemModel> listProjects(ProjectQuery projectQuery) {
        PageHelper.startPage(projectQuery.getPageNum(), projectQuery.getPageSize(), true);
        Page<RuleProject> ruleProjects = ruleProjectMapper.queryProjectByPage(projectQuery);
        Page<ProjectItemModel> projectModelDetails = convertToProjectItemModelPage(ruleProjects);

        PageInfo<ProjectItemModel> pageInfo = new PageInfo<>(projectModelDetails);
        pageInfo.setPageNum(projectQuery.getPageNum());
        pageInfo.setPageSize(projectQuery.getPageSize());
        pageInfo.setTotal(ruleProjects.getTotal());
        return pageInfo;
    }

    /**
     * 将 RuleProject 对象列表转换为 ProjectItemModel 对象列表
     *
     * @param ruleProjects RuleProject 对象列表
     * @return ProjectItemModel 对象列表
     */
    private Page<ProjectItemModel> convertToProjectItemModelPage(Page<RuleProject> ruleProjects) {
        Page<ProjectItemModel> projectModelDetails = new Page<>();
        for (RuleProject ruleProject : ruleProjects) {
            ProjectItemModel projectModelDetail = convertToProjectItemModel(ruleProject);
            projectModelDetails.add(projectModelDetail);
        }
        return projectModelDetails;
    }

    /**
     * 将 RuleProject 对象转换为 ProjectItemModel 对象
     *
     * @param ruleProject RuleProject 对象
     * @return ProjectItemModel 对象
     */
    private ProjectItemModel convertToProjectItemModel(RuleProject ruleProject) {
        ProjectItemModel projectModelDetail = new ProjectItemModel();
        projectModelDetail.setProjectId(ruleProject.getId());
        projectModelDetail.setProjectName(ruleProject.getProjectName());
        projectModelDetail.setStrategyType(ruleProject.getStrategyType());
        projectModelDetail.setRemark(ruleProject.getRemark());
        String lastOnlineModifier = "";
        Date lastOnlineUpdateTime = null;

        AdminCandidateStrategy adminCandidateStrategy = adminCandidateStrategyService
                .selectDeploySuccess(ruleProject.getStrategyType());
        if (adminCandidateStrategy != null) {
            lastOnlineModifier = adminCandidateStrategy.getUserName();
            lastOnlineUpdateTime = adminCandidateStrategy.getUpdateTime();
        }else {
            // 没有部署成功的策略，取最新的提交记录
            RuleProjectCommitHistory latestCommitHistory = ruleProjectCommitHistoryService
                    .getLatestCommitHistoryByProjectId(ruleProject.getId());
            if (latestCommitHistory != null) {
                lastOnlineModifier = latestCommitHistory.getUsername();
                lastOnlineUpdateTime = latestCommitHistory.getUpdateTime();
            }
        }
        projectModelDetail.setModifier(lastOnlineModifier);
        projectModelDetail.setUpdateTime(lastOnlineUpdateTime);
        return projectModelDetail;
    }

    /**
     * 新增数据
     *
     * @param projectModel 实例对象
     * @return 实例对象
     */
    @Transactional(value = "adminTransactionManager", rollbackFor = Exception.class)
    @Override
    public RuleProject createProject(ProjectModel projectModel) {
        // 1. 判断项目名称是否重复
        RuleProject existingProject = getProjectByName(projectModel.getProjectName());
        if (existingProject != null) {
            throw new RuntimeException("决策引擎项目名称重复");
        }
        // 2. 判断策略类型是否存在
        List<RuleProject> existingProjects = getProjectsByStrategyType(projectModel.getStrategyType());
        if (!CollectionUtils.isEmpty(existingProjects)) {
            throw new RuntimeException("决策引擎策略类型重复");
        }

        List<String> nodeNames = getNodeNamesByStrategyType(projectModel.getStrategyType());
        if (CollectionUtils.isEmpty(nodeNames)) {
            throw new RuntimeException("决策引擎策略未绑定节点");
        }

        // 3. 创建项目信息
        RuleProject ruleProject = new RuleProject();
        ruleProject.setProjectName(projectModel.getProjectName());
        ruleProject.setStrategyType(projectModel.getStrategyType());
        ruleProject.setRemark(projectModel.getRemark());
        ruleProject.setUpdateTime(new Date());
        ruleProject.setOutputStructure(new JSONArray().toJSONString());
        ruleProject.setIsDel(0);

        // 4. 落库
        this.ruleProjectMapper.insert(ruleProject);

        // 5. 初始化历史提交记录，并插入历史提交区代码和个人分支代码
        initializeProjectCommits(ruleProject, nodeNames);

        return ruleProject;
    }

    /**
     * 初始化项目提交记录和个人分支代码
     *
     * @param ruleProject 决策引擎项目对象
     * @param nodeNames   节点名称列表
     */
    private void initializeProjectCommits(RuleProject ruleProject, List<String> nodeNames) {
        Date now = new Date();
        for (String nodeName : nodeNames) {
            // 初始化历史提交记录
            RuleProjectCommitHistory commitHistory = new RuleProjectCommitHistory();
            commitHistory.setProjectId(ruleProject.getId());
            commitHistory.setProjectName(ruleProject.getProjectName());
            commitHistory.setFileName(nodeName);
            commitHistory.setComment("初始化项目");
            commitHistory.setCode(strategyCodeHandler.initCode());
            commitHistory.setUsername(UserInfoUtil.getUsername());
            commitHistory.setIsDel(0);
            commitHistory.setUpdateTime(now);
            commitHistory.setCreateTime(now);
            ruleProjectCommitHistoryService.insert(commitHistory);

            // 初始化个人提交记录
            RuleProjectUserCommit userCommit = new RuleProjectUserCommit();
            userCommit.setProjectId(ruleProject.getId());
            userCommit.setProjectName(ruleProject.getProjectName());
            userCommit.setFileName(nodeName);
            userCommit.setHistoryCurrentId(commitHistory.getId());
            userCommit.setHistoryMasterId(commitHistory.getId());
            userCommit.setUserName(UserInfoUtil.getUsername());
            userCommit.setIsDel(0);
            userCommit.setIsUpdate(0);
            userCommit.setUpdateTime(now);
            userCommit.setCreateTime(now);
            ruleProjectUserCommitService.insert(userCommit);
        }
    }

    /**
     * 根据策略类型获取节点名称列表
     *
     * @param strategyType 策略类型
     * @return 节点名称列表
     */
    private List<String> getNodeNamesByStrategyType(String strategyType){
        return adminStrategyTypeService.selectStrategyNodes(strategyType);
    }

    /**
     * 修改决策引擎项目
     *
     * @param ruleProject 决策引擎项目对象
     * @return 修改后的决策引擎项目对象
     */
    @Override
    public RuleProject updateProject(RuleProject ruleProject) {
        ruleProjectMapper.update(ruleProject);
        return getProjectById(ruleProject.getId());
    }

    /**
     * 查询项目详情
     * @param projectId
     * @return
     */
    @Transactional(value = "adminTransactionManager", rollbackFor = Exception.class)
    @Override
    public ProjectDetailModel detail(Integer projectId) {
        // 1. 查询项目信息
        RuleProject ruleProject = getProjectById(projectId);
        if (ruleProject == null) {
            throw new IllegalArgumentException("决策引擎项目不存在");
        }
        // 2. 获取当前用户
        ProjectDetailModel projectDetailModel = new ProjectDetailModel();
        List<ProjectDetailModel.Node> nodes = new ArrayList<>();
        String LastOnlineModifier = "";
        Date LastOnlineUpdateTime = null;
        // 查询最近一次部署成功的记录
        AdminCandidateStrategy adminCandidateStrategy = adminCandidateStrategyService
                .selectDeploySuccess(ruleProject.getStrategyType());
        // 3. 查询当前用户提交记录
        List<String> nodeNames = getNodeNamesByStrategyType(ruleProject.getStrategyType());
        for (String nodeName: nodeNames){
            RuleProjectUserCommit ruleProjectUserCommit = ruleProjectUserCommitService
                    .queryByProjectIdAndUserNameAndFileName(projectId, UserInfoUtil.getUsername(), nodeName);
            ProjectDetailModel.Node node = new ProjectDetailModel.Node();
            // 查询最新的提交记录
            RuleProjectCommitHistory ruleProjectCommitHistory = ruleProjectCommitHistoryService
                    .queryLatestByProjectIdAndNodeName(projectId, nodeName);
            if(ruleProjectUserCommit == null){
                // 查询被删除的节点记录
                ruleProjectUserCommit = ruleProjectUserCommitService
                        .queryByProjectIdAndFileName(projectId, UserInfoUtil.getUsername(), nodeName);
                if(null != ruleProjectUserCommit){
                    ruleProjectUserCommit.setIsDel(0);
                    ruleProjectUserCommitService.update(ruleProjectUserCommit);
                }else {
                    if(null == ruleProjectCommitHistory){
                        // todo 可能可以删除 增加日志
                        logger.error("ruleProjectUserCommit is null, projectId: {}, nodeName: {}", projectId, nodeName);
                        // 后续增加的节点 需要初始化
                        ruleProjectCommitHistory = ruleProjectCommitHistoryService
                                .queryEarliestByProjectId(projectId);
                        ruleProjectCommitHistory.setFileName(nodeName);
                        ruleProjectCommitHistoryService.insert(ruleProjectCommitHistory);
                        RuleProjectUserCommit ruleProjectUserCommit1 = ruleProjectUserCommitService
                                .queryEarliestByProjectId(projectId);
                        ruleProjectUserCommit1.setFileName(nodeName);
                        ruleProjectUserCommit1.setHistoryMasterId(ruleProjectCommitHistory.getId());
                        ruleProjectUserCommit1.setHistoryCurrentId(ruleProjectCommitHistory.getId());
                        ruleProjectUserCommitService.insert(ruleProjectUserCommit1);
                    }
                }
            }
            node.setNodeName(nodeName);
            if (adminCandidateStrategy != null) {
                LastOnlineModifier = adminCandidateStrategy.getUserName();
                LastOnlineUpdateTime = adminCandidateStrategy.getUpdateTime();
            } else {
                if (ruleProjectCommitHistory != null) {
                    LastOnlineModifier = ruleProjectCommitHistory.getUsername();
                    LastOnlineUpdateTime = ruleProjectCommitHistory.getUpdateTime();
                }
            }
            node.setModifier(LastOnlineModifier);
            node.setUpdateTime(LastOnlineUpdateTime);
            node.setStrategyType(ruleProject.getStrategyType());
            nodes.add(node);
        }

        // 4. 获取有序数组
        List<String> orderedArray = new ArrayList<>();
        projectDetailModel.setGraph(getGraph(ruleProject.getStrategyType(), orderedArray));

        // 5. 根据有序数组排序nodes
        nodes.sort(Comparator.comparingInt(node -> orderedArray.indexOf(node.getNodeName())));
        projectDetailModel.setList(nodes);

        // 拉取线上代码
        List<String> preMergeOnlineList = projectPullServiceImpl.getUnPulledNodes(ruleProject.getId());
        projectPullServiceImpl.pullOnlineVersion(ruleProject.getId(), preMergeOnlineList);

        return projectDetailModel;
    }

    public JSONObject getGraph(String strategyType, List<String> orderedArray) {
        try {
            JSONObject graphOld = null;
            String getData = getStrategyGraph(strategyType);
            logger.info("getGraph strategyType: {} getData: {}", strategyType, getData);
            if (getData != null) {
                graphOld = JSON.parseObject(getData);
            }
            if (graphOld == null) {
                // 子流程获取为空
                return new JSONObject();
            }

            // 获取子流程图所有节点信息
            JSONArray nodes = graphOld.getJSONArray("nodes");
            // 获取子流程图所有连线信息
            JSONArray edges = graphOld.getJSONArray("edges");

            Map<String, List<JSONObject>> edgesBySource = new HashMap<>();
            for (Object edge : edges) {
                JSONObject edgeObj = (JSONObject) edge;
                String source = edgeObj.getString("source");
                edgesBySource.computeIfAbsent(source, k -> new ArrayList<>()).add(edgeObj);
            }

            Map<String, JSONObject> nodesById = new HashMap<>();
            JSONObject startNode = null;
            for (Object node : nodes) {
                JSONObject nodeObj = (JSONObject) node;
                nodesById.put(nodeObj.getString("id"), nodeObj);
                // 找到开始节点
                String nodeType = nodeObj.getString("type");
                if ("Start".equalsIgnoreCase(nodeType)) {
                    startNode = nodeObj;
                }
            }

            if (startNode == null) {
                // 没有找到开始节点
                throw new IllegalArgumentException("子流程图缺少开始节点");
            }

            // 从开始节点开始遍历
            List<JSONObject> nodesNew = new ArrayList<>();
            Queue<JSONObject> queue = new LinkedList<>();
            queue.offer(startNode);

            while (!queue.isEmpty()) {
                JSONObject currentNode = queue.poll();
                nodesNew.add(processNode(currentNode, orderedArray));

                List<JSONObject> connectedEdges = edgesBySource.get(currentNode.getString("id"));
                if (connectedEdges != null) {
                    for (JSONObject edgeObj : connectedEdges) {
                        JSONObject targetNode = nodesById.get(edgeObj.getString("target"));
                        if (targetNode != null) {
                            queue.offer(targetNode);
                        }
                    }
                }
            }

            JSONArray edgesNew = new JSONArray();
            for (Object edge : edges) {
                JSONObject edgeNew = new JSONObject();
                JSONObject edgeOld = (JSONObject) edge;
                edgeNew.put("source", edgeOld.getString("source"));
                edgeNew.put("target", edgeOld.getString("target"));
                edgeNew.put("label", edgeOld.getString("label"));
                edgesNew.add(edgeNew);
            }

            JSONObject newGraph = new JSONObject();
            newGraph.put("nodes", nodesNew);
            newGraph.put("edges", edgesNew);
            return newGraph;
        } catch (Exception e) {
            logger.error("getGraph error", e);
            throw new IllegalArgumentException("子流程图解析失败");
        }
    }

    private String getStrategyGraph(String strategyType) {
        List<ProcessDefinition> processDefinitionList = adminProcessDefinitionService.selectByStrategyType(strategyType);
        for (ProcessDefinition processDefinition : processDefinitionList) {
            if (processDefinition.getInUse()) {
                return processDefinition.getProcessJson();
            }
        }
        return null;
    }

    private JSONObject processNode(JSONObject node, List<String> orderedArray) {
        JSONObject nodeNew = new JSONObject();
        String nodeType = node.getString("type");
        nodeNew.put("id", node.getString("id"));

        if ("Start".equalsIgnoreCase(nodeType)) {
            nodeNew.put("dataType", "alps");
            nodeNew.put("name", "开始");
        } else if ("END".equalsIgnoreCase(nodeType)) {
            nodeNew.put("dataType", "alps");
            nodeNew.put("name", "结束");
        } else if ("JUDGE".equalsIgnoreCase(nodeType)) {
            nodeNew.put("dataType", "alps");
            nodeNew.put("name", "判断分支");
        } else if ("CUSTOM".equals(nodeType) || "DATA".equals(nodeType) || "FEATURE".equals(nodeType) || "STRATEGY".equals(nodeType)) {
            nodeNew.put("dataType", "alps");

            if ("CUSTOM".equals(nodeType)) {
                nodeNew.put("name", "定制节点: " + node.getString("nodeCode"));
            } else if ("DATA".equals(nodeType)) {
                nodeNew.put("name", "数据节点: " + node.getString("nodeCode"));
            } else if ("FEATURE".equals(nodeType)) {
                nodeNew.put("name", "特征节点: " + node.getString("nodeCode"));
            } else if ("STRATEGY".equals(nodeType)) {
                nodeNew.put("name", "策略节点: " + node.getString("nodeCode"));
            }

            JSONArray properties = new JSONArray();
            String label = node.getString("label");
            String[] labelArr = label.split("\n");
            for (String labelItem : labelArr) {
                JSONObject property = new JSONObject();
                String[] labelItemArr = labelItem.split("：");
                if (labelItemArr.length < 2) {
                    property.put("label", labelItemArr[0]);
                    property.put("value", "");
                } else {
                    property.put("label", labelItemArr[0]);
                    property.put("value", labelItemArr[1]);

                    if ("STRATEGY".equals(nodeType) && "step".equalsIgnoreCase(labelItemArr[0].trim())) {
                        orderedArray.add(labelItemArr[1].trim());
                    }
                }
                properties.add(property);
            }
            nodeNew.put("conf", properties);
        }

        return nodeNew;
    }

    /**
     * 获取部署成功的提交id并更新到个人分支historyid
     */
    public void updateUserHistoryForOnlineId(Integer projectId, String userName){
        projectPullServiceImpl.updateUserHistoryForOnlineId(projectId, userName);
    }

    /**
     * 项目回滚
     * @param projectId
     */
    @Override
    public void recover(Integer projectId) {
        projectPullServiceImpl.recoverToOnlineVersion(projectId);
    }
}
