package com.youxin.risk.admin.model.cp;

import java.util.Date;

public class SystemParameter {
	/**
	 * id 唯一标识
	 */
	private int id;
	/**
	 * 参数key名称
	 */
	private String keyName;
	/**
	 * 参数值
	 */
	private String value;
	/**
	 * 参数描述
	 */
	private String description;
	/**
	 * 是否删除，0-未删除
	 */
	private int status;
	/**
	 * 参数类型
	 */
	private int type;
	private Date createTime;
	private Date updateTime;
	private String  createUserId;
	private String  modifyUserId;
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getKeyName() {
		return keyName;
	}
	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	 
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
 
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	public String getModifyUserId() {
		return modifyUserId;
	}
	public void setModifyUserId(String modifyUserId) {
		this.modifyUserId = modifyUserId;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	
	
	
}
