package com.youxin.risk.admin.service.rulescore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.dao.admin.*;
import com.youxin.risk.admin.dto.PageInfo;
import com.youxin.risk.admin.dto.rulescore.RuleScoreDTO;
import com.youxin.risk.admin.model.rulescore.*;
import com.youxin.risk.admin.service.notifier.BindNotifier;
import com.youxin.risk.admin.service.notifier.UnBindNotifier;
import com.youxin.risk.admin.tools.notification.AlertNotificationHandler;
import com.youxin.risk.admin.utils.RuleSetCodeHandler;
import com.youxin.risk.admin.utils.StringUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.StrategyCandidateStatusEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.exception.ValidateException;
import com.youxin.risk.commons.model.StrategyNodeRelationalDO;
import com.youxin.risk.commons.mongo.RuleScoreResultMongoDao;
import com.youxin.risk.commons.service.StrategyNodeRelationalService;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import com.youxin.risk.commons.vo.RuleScoreMirrorResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.youxin.risk.admin.model.rulescore.RuleCandidateScore.*;
import static com.youxin.risk.admin.model.rulescore.RuleScoreMirror.START;

@Service
@Slf4j
public class RuleScoreService {
    private static final Logger logger = LoggerFactory.getLogger(RuleScoreService.class);
    @Resource
    private RuleScoreMapper ruleScoreMapper;
    @Resource
    private RuleScoreRelationMapper ruleScoreRelationMapper;
    @Resource
    private RuleScoreVarMapper ruleScoreVarMapper;
    @Resource
    private RuleCandidateScoreMapper ruleCandidateScoreMapper;
    @Resource
    private RuleCandidateScoreService ruleCandidateScoreService;
    @Autowired
    private RuleScoreStrategyMapper ruleScoreStrategyMapper;
    @Resource
    private StrategyNodeRelationalService strategyNodeRelationalService;
    @Resource
    private RuleScoreRelationOnlineMapper ruleScoreRelationOnlineMapper;
    @Autowired
    private AdminVariableConfigMapper adminVariableConfigMapper;
    @Resource
    private RedisLock redisLock;
    @Autowired
    private RuleScoreMirrorMapper ruleScoreMirrorMapper;
    @Autowired
    private RuleScoreResultMongoDao ruleScoreResultMongoDao;
    @Resource
    private BindNotifier bindNotifier;
    @Resource
    private UnBindNotifier unBindNotifier;

    @Resource
    private RuleSetCodeHandler ruleSetCodeHandler;
    @Resource
    private AlertNotificationHandler alertNotificationHandler;

    /**
     * 新增操作
     * @param ruleScoreDTO
     * @param userKey
     */
    @Transactional(transactionManager = "adminTransactionManager")
    public void save(RuleScoreDTO ruleScoreDTO,String userKey){
        LockResult result = null;
        String lockKey = "RuleScore.save.key";
        try {
            logger.info("RuleScoreService save param = {} userKey = {}", JSON.toJSONString(ruleScoreDTO),userKey);

            result = redisLock.tryLock(lockKey, 5);
            if (!result.isSuccess()) {
                return;
            }

            check(ruleScoreDTO);

            List<RuleCandidateScore> ruleCandidateScoreList = ruleCandidateScoreMapper.findRuleCandidateScoreList(ruleScoreDTO.getRuleKey());
            /** 新的规则分版本 **/
            int newVersion = 1;

            if (CollectionUtils.isEmpty(ruleCandidateScoreList)) {
                /**  新增的时候才会新建一个表 **/
                RuleScore ruleScore = new RuleScore();
                BeanUtils.copyProperties(ruleScoreDTO, ruleScore, "ruleVersion");
                ruleScore.setModifyUser(userKey);
                ruleScore.setCreateTime(new Date());
                ruleScore.setUpdateTime(new Date());
                ruleScoreMapper.insert(ruleScore);
            }else {
                newVersion = ruleCandidateScoreList.size() + 1;
            }

            insertRuleScoreRelation(ruleScoreDTO, newVersion);

            List<String> variableList = insertRuleScoreVar(ruleScoreDTO, newVersion);

            ruleCandidateScoreService.save(newVersion,userKey,variableList,ruleScoreDTO);

        }catch (Exception e){
            /** 捕获异常要往外面抛出 **/
            logger.error("RuleScoreService error param = {} userKey = {}", JSON.toJSONString(ruleScoreDTO),userKey);
            throw new RuntimeException(e);
        }finally {
            if (result != null && result.isSuccess()){
                redisLock.releaseLock(lockKey, result.getLockId());
            }
            logger.info("RuleScoreService finish param = {} userKey = {}", JSON.toJSONString(ruleScoreDTO),userKey);
        }
    }

    /**
     * 依赖变量
     * @param ruleScoreDTO
     * @param version
     * @return
     */
    private List<String> insertRuleScoreVar(RuleScoreDTO ruleScoreDTO, int version) {
        List<String> variableList = ruleSetCodeHandler.queryVariableList(ruleScoreDTO.getRuleRunCode());
        if (!CollectionUtils.isEmpty(variableList)){
            /** 依赖多个变量的时候去重 **/
            variableList = variableList.stream().distinct().collect(Collectors.toList());
            List<RuleScoreVar> ruleScoreVarList = new ArrayList<>();
            for (String variableCode : variableList){
                RuleScoreVar ruleScoreVar = new RuleScoreVar();
                ruleScoreVar.setRuleKey(ruleScoreDTO.getRuleKey());
                ruleScoreVar.setRuleVersion(version);
                ruleScoreVar.setVariableCode(variableCode);
                ruleScoreVar.setUpdateTime(new Date());
                ruleScoreVar.setCreateTime(new Date());
                ruleScoreVarList.add(ruleScoreVar);
            }
            ruleScoreVarMapper.insertBatch(ruleScoreVarList);
        }
        return variableList;
    }

    /**
     * 依赖规则分
     * @param ruleScoreDTO
     * @param version
     */
    private void insertRuleScoreRelation(RuleScoreDTO ruleScoreDTO, int version) {
        List<String> ruleScoreRelations = ruleScoreDTO.getRuleScoreRelations();
        String ruleKey = ruleScoreDTO.getRuleKey();
        if (!CollectionUtils.isEmpty(ruleScoreRelations)) {
            List<RuleScoreRelation> list = new ArrayList<>();
            for (String relationRuleKey : ruleScoreRelations) {
                RuleScoreRelation ruleScoreRelation = new RuleScoreRelation();
                ruleScoreRelation.setRuleKey(ruleKey);
                ruleScoreRelation.setRuleVersion(version);
                ruleScoreRelation.setRelationRuleKey(relationRuleKey);
                RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreOnlineByRuleKey(relationRuleKey);
                ruleScoreRelation.setRelationRuleVersion(ruleCandidateScore.getRuleVersion());
                ruleScoreRelation.setUpdateTime(new Date());
                ruleScoreRelation.setCreateTime(new Date());
                list.add(ruleScoreRelation);
            }
            ruleScoreRelationMapper.insertBatch(list);
        }
    }

    /**
     * 检查规则分的层级依赖
     * @param ruleScoreDTO
     */
    private void check(RuleScoreDTO ruleScoreDTO) {
        List<String> ruleScoreRelations = ruleScoreDTO.getRuleScoreRelations();
        /** 查看依赖的规则分是否有其他的依赖 **/
        for (String relationRuleKey : ruleScoreRelations) {
            List<RuleScoreRelationOnline> relationOnlineByRelationRuleKey = ruleScoreRelationOnlineMapper.findRuleScoreRelationOnlineByRuleKey(relationRuleKey);
            if (CollectionUtils.isNotEmpty(relationOnlineByRelationRuleKey)){
                throw new ValidateException("规则分限制最大2层级: 依赖的规则分" + relationRuleKey +"存在依赖");
            }
        }
        /** 查看当前的规则分是否被其他所依赖 **/
        if (CollectionUtils.isNotEmpty(ruleScoreDTO.getRuleScoreRelations())) {
            String ruleKey = ruleScoreDTO.getRuleKey();
            List<RuleScoreRelationOnline> ruleScoreRelationOnlineByRelationRuleKey = ruleScoreRelationOnlineMapper.findRuleScoreRelationOnlineByRelationRuleKey(ruleKey);
            if (CollectionUtils.isNotEmpty(ruleScoreRelationOnlineByRelationRuleKey)) {
                List<String> ruleKeys = ruleScoreRelationOnlineByRelationRuleKey.stream()
                        .map(ruleScoreRelationOnline -> ruleScoreRelationOnline.getRuleKey()).distinct().collect(Collectors.toList());
                throw new ValidateException("规则分限制最大2层级: 当前规则分被" + JSON.toJSONString(ruleKeys) + "所依赖");
            }
        }

        /** 此规则在规则候选表中没有状态为运行中、运行失败、待审批、审批中、待上线的记录 **/
        List<RuleCandidateScore> ruleCandidateScoreList = ruleCandidateScoreMapper.findRuleCandidateScoreList(ruleScoreDTO.getRuleKey());
        if (CollectionUtils.isNotEmpty(ruleCandidateScoreList)){
            for (RuleCandidateScore ruleCandidateScore : ruleCandidateScoreList){
                Integer ruleStatus = ruleCandidateScore.getRuleStatus();
                if (ruleStatus != null && (ruleStatus == ruleStatusRunning || ruleStatus == ruleStatusFail || ruleStatus == ruleWaitAudit || ruleStatus == ruleAudit || ruleStatus == ruleWaitOnline)){
                    throw new ValidateException("规则分存在运行中、运行失败、待审批、审批中、待上线的数据");
                }
            }
        }
    }

    /**
     * 查询上线的规则分
     */
    public List<RuleScore> findBindRuleKeyList() {
        return ruleScoreMapper.findOnlineRuleKey();
    }

    /**
     * 根据策略类型、节点名称查询现在已经绑定的规则分key
     */
    public List<RuleCandidateScore> findBindRuleKeyListByNode(String strategyType, String nodeName) {
        /** 获取所有的绑定关系最大的版本号 **/
        List<RuleScoreStrategy> strategyListByStrategyNode = ruleScoreStrategyMapper.findMaxVersionRuleScoreByStrategyNode(strategyType, nodeName);
        return getRuleCandidateScores(strategyListByStrategyNode);
    }

    /**
     * 通过策略类型和策略节点查看上线规则分,然后查看规则分使用的变量
     * @param strategyType
     * @param nodeName
     * @return
     */
    public List<String> findBindRuleKeyListByTypeAndNode(String strategyType, String nodeName){
        List<RuleScoreStrategy> ruleScoreStrategies = ruleScoreStrategyMapper.findRuleScoreByStrategyTypeAndNode(strategyType, nodeName);
        List<String> ruleKeyList = ruleScoreStrategies.stream().map(ruleScoreStrategy -> ruleScoreStrategy.getRuleKey()).collect(Collectors.toList());
        return ruleKeyList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 删除策略类型、节点名称查询出来的已经绑定的规则集
     */
    public void deleteBindRuleKeyListByNode(String strategyType, String nodeName) {
        // 查询节点绑定的规则集
        List<RuleCandidateScore> bindRuleKeyListByNode = this.findBindRuleKeyListByNode(strategyType, nodeName);
        if (!CollectionUtils.isEmpty(bindRuleKeyListByNode)) {
            // 遍历bindRuleKeyListByNode
            for (RuleCandidateScore ruleCandidateScore : bindRuleKeyListByNode) {
                // 删除绑定
                int i = ruleScoreStrategyMapper.deleteByRuleKeyVersion(ruleCandidateScore.getRuleKey()
                        , ruleCandidateScore.getRuleVersion(), strategyType, nodeName);
                if (i > 0){
                    logger.info("删除规则分绑定成功, ruleKey={}, ruleVersion={}, strategyType={}, nodeName={}"
                            , ruleCandidateScore.getRuleKey(), ruleCandidateScore.getRuleVersion(), strategyType, nodeName);
                }
            }
        }else {
            logger.info("没有可以删除的规则分绑定, strategyType={}, nodeName={}", strategyType, nodeName);
        }
    }

    /**
     * 根据策略类型、节点名称查询现在已经绑定的规则分key
     */
    public PageInfo<RuleCandidateScore> findBindRuleKeyList(String strategyType, String nodeName, String ruleKey, Integer pageNum, Integer pageSize) {
        /** 获取所有的绑定关系 **/
        List<RuleCandidateScore> ruleCandidateScores = getRuleCandidateScores(strategyType, nodeName, ruleKey);
        if(CollectionUtils.isNotEmpty(ruleCandidateScores)){
            // 按照pageNum、pageSize切分 todo 后续改成sql
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(pageNum * pageSize, ruleCandidateScores.size());
            ruleCandidateScores = ruleCandidateScores.subList(start, end);
        }

        Set<String> ruleScoreSet = new HashSet<>();
        StrategyNodeRelationalDO strategyNodeRelationalDO = strategyNodeRelationalService
                .getByStrategyNodeIdAndStatus(nodeName, StrategyCandidateStatusEnum.ONLINE.name());
        if (strategyNodeRelationalDO != null) {
            // 获取策略代码使用的规则集
            String ruleScoreListStr = strategyNodeRelationalDO.getRuleScoreListStr() == null
                    ? "[]" : strategyNodeRelationalDO.getRuleScoreListStr();
            List<String> ruleScoreList = JSON.parseArray(ruleScoreListStr, String.class);
            ruleScoreSet.addAll(ruleScoreList);
            // 查询规则分依赖的规则分 都是使用的规则分
            ruleScoreList.forEach(rule -> {
                List<RuleScoreRelationOnline> dependRuleScoreRelationList = ruleScoreRelationOnlineMapper
                        .findRuleScoreRelationOnlineByRuleKey(rule);
                for (RuleScoreRelationOnline ruleScoreRelationOnline : dependRuleScoreRelationList) {
                    ruleScoreSet.add(ruleScoreRelationOnline.getRelationRuleKey());
                }
            });
        }
        // 如果在ruleScoreList中，那么代表使用了规则集
        for (RuleCandidateScore ruleCandidateScore : ruleCandidateScores) {
            if (ruleScoreSet.contains(ruleCandidateScore.getRuleKey())) {
                ruleCandidateScore.setRuleIsUsed(1);
            }else {
                ruleCandidateScore.setRuleIsUsed(0);
            }
        }
        return new PageInfo<>(ruleCandidateScores);
    }

    /**
     * 根据策略类型、节点名称查询现在已经绑定的规则分key
     */
    public List<RuleCandidateScore> getRuleCandidateScores(String strategyType, String nodeName, String ruleKey) {
        List<RuleScoreStrategy> strategyListByStrategyNode = ruleScoreStrategyMapper.findMaxVersionRuleScoreByStrategyNode(strategyType, nodeName);
        return getRuleCandidateScores(strategyListByStrategyNode, ruleKey);
    }

    /**
     * 根据策略类型、节点名称查询现在已经绑定的规则分key
     */
    public List<RuleCandidateScore> getRuleCandidateScores(String strategyType, String nodeName) {
        List<RuleScoreStrategy> strategyListByStrategyNode = ruleScoreStrategyMapper.findMaxVersionRuleScoreByStrategyNode(strategyType, nodeName);
        return getRuleCandidateScores(strategyListByStrategyNode);
    }

    /**
     * 获得节点绑定上线所有规则分信息
     * @param strategyListByStrategyNode
     * @param ruleKey 过滤的ruleKey
     * @return
     */
    public List<RuleCandidateScore> getRuleCandidateScores(List<RuleScoreStrategy> strategyListByStrategyNode, String...ruleKey) {
        List<RuleCandidateScore> ruleCandidateScoreList = new ArrayList<>();
        String filterRuleKey = ruleKey.length == 0 ? null : ruleKey[0];

        for (RuleScoreStrategy ruleScoreStrategy : strategyListByStrategyNode){
            String bindRuleKey = ruleScoreStrategy.getRuleKey();
            Integer bindRuleVersion = ruleScoreStrategy.getRuleVersion();
            if (!StringUtils.isEmpty(filterRuleKey) && !bindRuleKey.contains(filterRuleKey)){
                continue;
            }
            RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreOnlineByRuleKeyAndVersion(bindRuleKey,bindRuleVersion);
            if (ruleCandidateScore != null) {
                // 使用绑定规则分的修改人
                ruleCandidateScore.setModifyUser(ruleScoreStrategy.getModifyUser());
                ruleCandidateScore.setCreateTime(ruleScoreStrategy.getCreateTime());
                ruleCandidateScore.setUpdateTime(ruleScoreStrategy.getUpdateTime());
                ruleCandidateScoreList.add(ruleCandidateScore);
            }
        }
        return ruleCandidateScoreList;
    }

    /**
     * 查询规则分依赖的哪些规则分
     */
    public List<RuleScoreRelationOnline> findRuleScoreRelationList(String ruleKey) {
        return ruleScoreRelationOnlineMapper.findRuleScoreRelationOnlineByRuleKey(ruleKey);
    }

    public RuleCandidateScore findRuleScore(String ruleKey, Integer ruleVersion){
        RuleCandidateScore ruleCandidateScore = ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleKey, ruleVersion);
        String generateCode = ruleSetCodeHandler.generate(ruleKey, ruleVersion, ruleCandidateScore.getRuleRunCode());
        ruleCandidateScore.setRuleRunCode(generateCode);
        return ruleCandidateScore;
    }

    public void binding(String ruleKey, Integer version, JSONArray nodeStrategy,Boolean isForce) throws ParseException {
        RuleScore onlineRuleScore = ruleScoreMapper.findOnlineRuleScore(ruleKey, version);
        if (onlineRuleScore == null){
            throw new RiskRuntimeException("当前绑定没有上线,可能状态已经更新，请刷新列表");
        }
        List<String> bindNode = new ArrayList<>();
        for (int i = 0 ; i<nodeStrategy.size() ; i++){
            JSONObject data = nodeStrategy.getJSONObject(i);
            String strategy = data.getString("strategy");
            String node = data.getString("node");
            if (data.containsKey("id")){
                continue;
            }

            if (!isForce) {
                checkMirrorResult(ruleKey, version, strategy, node);
            }

            /** strategy和node 新绑定策略和节点 **/
            checkBind(ruleKey,version,strategy,node);

            RuleScoreStrategy ruleScoreStrategy = new RuleScoreStrategy();
            ruleScoreStrategy.setRuleKey(ruleKey);
            ruleScoreStrategy.setRuleVersion(version);
            ruleScoreStrategy.setModifyUser(UserInfoUtil.getUsername());
            ruleScoreStrategy.setNode(node);
            ruleScoreStrategy.setStrategy(strategy);
            ruleScoreStrategy.setCreateTime(new Date());
            ruleScoreStrategy.setUpdateTime(new Date());
            ruleScoreStrategyMapper.insert(ruleScoreStrategy);

            // 规则集绑定后，更新策略节点变量使用信息
            updateBindStrategyNodeVariableUsage(strategy, node);

            bindNode.add(node);
        }

        if (!CollectionUtils.isEmpty(bindNode)){
            alertNotificationHandler.sendRuleBindingNotification(ruleKey, version, bindNode);
        }
    }

    private void updateBindStrategyNodeVariableUsage(String strategyType, String strategyNodeCode) {
        Map<String, String> argMap = new HashMap<>();
        argMap.put("strategyNodeCode", strategyNodeCode);
        argMap.put("strategyType", strategyType);
        bindNotifier.notifyListeners(argMap);
    }

    /**
     * 检查检验结果
     * @param ruleKey
     * @param version
     * @param strategy
     * @param node
     * @throws ParseException
     */
    private void checkMirrorResult(String ruleKey, Integer version, String strategy, String node) throws ParseException {
        /** 绑定新节点的时候: 最后的镜像实验且结束 **/
        RuleScoreMirror ruleMirror = ruleScoreMirrorMapper.findRuleMirror(ruleKey, version);
        if (ruleMirror == null ||ruleMirror.getRuleMirrorStatus() == START) {
            throw new RiskRuntimeException("未镜像,或者镜像还未结束,不能绑定" + ruleKey);
        }

        String ruleMirrorCode = ruleMirror.getRuleMirrorCode();
        Integer ruleMirrorCount = ruleMirror.getRuleMirrorCount() == null ? 50000 : ruleMirror.getRuleMirrorCount();
        Map<String, Object> params = new HashMap<>();
        params.put("ruleMirrorCode", ruleMirrorCode);
        params.put("strategyCodeType", strategy);
        params.put("node", node);
        params.put("pageNum", 1);
        params.put("pageSize", ruleMirrorCount);
        List<RuleScoreMirrorResultVo> ruleScoreMirrorResultVoList = ruleScoreResultMongoDao.selectPage(params);
        log.info("ruleScoreMirrorResultVoList = {}", JSON.toJSONString(ruleScoreMirrorResultVoList));
        if (org.springframework.util.CollectionUtils.isEmpty(ruleScoreMirrorResultVoList)){
            throw new RiskRuntimeException("没有镜像结果");
        }

        Map<String,Integer> mirrorMap = new HashMap<>();
        /** 遍历所有镜像节点 **/
        for (RuleScoreMirrorResultVo ruleScoreMirrorResultVo : ruleScoreMirrorResultVoList) {
            if (node.equals(ruleScoreMirrorResultVo.getNode()) && strategy.equals(ruleScoreMirrorResultVo.getStrategyCodeType())) {
                if ("无差异".equals(ruleScoreMirrorResultVo.getStatus()) || "有差异".equals(ruleScoreMirrorResultVo.getStatus())) {
                    /** 记录成功数量 **/
                    String name = ruleScoreMirrorResultVo.getStrategyCodeType() + "_" + ruleScoreMirrorResultVo.getNode();
                    if (mirrorMap.containsKey(name)) {
                        Integer count = mirrorMap.get(name);
                        mirrorMap.put(name, ++count);
                    } else {
                        mirrorMap.put(name, 1);
                    }
                } else {
                    /** 有失败的不可以上线,抛异常 **/
                    throw new RiskRuntimeException(" 镜像策略 "+strategy + " 节点" + node  + "存在镜像失败,不可以绑定");
                }
            }
        }

        /** 成功数量判断 **/
        String mirrorConfig = ApolloClientAdapter.getStringConfig(ApolloNamespaceEnum.ADMIN_SPACE, "mirror", "{}");
        JSONObject mirrorJson = JSON.parseObject(mirrorConfig);
        Integer successCount = mirrorJson.getInteger("successCount");
        for (Map.Entry<String, Integer> entry : mirrorMap.entrySet()) {
            String key = entry.getKey();
            Integer value = entry.getValue() == null ? 0 : entry.getValue();
            if (value < successCount) {
                throw new RiskRuntimeException(" 镜像策略节点" + key + "数量 " + value + "不满足要求数量 " + successCount);
            }
        }
    }

    /**
     * 判断当前规则分依赖的变量、依赖规则分是否在要绑定的节点上
     * @param ruleKey
     * @param version
     * @param strategy
     * @param node
     */
    private void checkBind(String ruleKey,Integer version,String strategy,String node) {
        List<RuleScoreVar> ruleScoreVar = ruleScoreVarMapper.findRuleScoreVar(ruleKey, version);
        List<String> variableList = ruleScoreVar.stream().map(r -> r.getVariableCode()).collect(Collectors.toList());
        /** 检查依赖的变量 **/
        if (!org.springframework.util.CollectionUtils.isEmpty(variableList)) {
            JSONObject param = new JSONObject();
            param.put("strategyType", strategy);
            param.put("strategyNodeCode", node);
            List<String> allVars = adminVariableConfigMapper.getVariableCodesByParam(param);
            if (!allVars.containsAll(variableList)) {
                throw new RiskRuntimeException("依赖的变量没有绑定在节点 " + "策略 " + strategy + "节点 " + node);
            }
        }

        /** 检查依赖的规则分 **/
        List<RuleScoreRelation> ruleScoreRelationList = ruleScoreRelationMapper.findRuleScoreRelationList(ruleKey, version);
        for (RuleScoreRelation ruleScoreRelation : ruleScoreRelationList){
            RuleScoreStrategy ruleScoreStrategy = ruleScoreStrategyMapper.findRuleScoreStrategyByParam(ruleScoreRelation.getRelationRuleKey(), ruleScoreRelation.getRelationRuleVersion(), strategy, node);
            if (ruleScoreStrategy == null){
                throw new RiskRuntimeException("依赖的规则分没有绑定在节点 " + "策略 " +  strategy + "节点 " + node);
            }
        }
    }

    /**
     * 规则分解绑
     */
    public void unbindRuleScore(String ruleKey, Integer ruleVersion, String strategy, String node) {
        RuleScore ruleScore = ruleScoreMapper.findOnlineRuleScore(ruleKey, ruleVersion);
        if (ruleScore == null){
            throw new ValidateException("规则分不存在");
        }
        // 校验线上策略代码是否使用规则分
        StrategyNodeRelationalDO strategyNodeRelationalDO = strategyNodeRelationalService
                .getByStrategyNodeIdAndStatus(node, StrategyCandidateStatusEnum.ONLINE.name());
        if (strategyNodeRelationalDO != null) {
            String ruleScoreListStr = strategyNodeRelationalDO.getRuleScoreListStr() == null
                    ? "{}" : strategyNodeRelationalDO.getRuleScoreListStr();
            List<String> ruleScoreList = JSONObject.parseArray(ruleScoreListStr, String.class);
            Set<String> ruleScoreSet = new HashSet<>(ruleScoreList);

            // 查询规则分依赖的规则分 都是使用的规则分
            ruleScoreList.forEach(rule -> {
                List<RuleScoreRelationOnline> dependRuleScoreRelationList = ruleScoreRelationOnlineMapper
                        .findRuleScoreRelationOnlineByRuleKey(rule);
                for (RuleScoreRelationOnline ruleScoreRelationOnline : dependRuleScoreRelationList) {
                    ruleScoreSet.add(ruleScoreRelationOnline.getRelationRuleKey());
                }
            });
            if (ruleScoreSet.contains(ruleKey)) {
                throw new ValidateException("规则分" + ruleKey + "已被策略节点" + node + "使用！");
            }
        }

        // 校验当前绑定的规则分中，是否有此规则分的父规则分
        List<RuleScoreRelationOnline> dependRuleScoreRelationList = ruleScoreRelationOnlineMapper
                .findDependRuleScoreRelationList(ruleKey);
        // 查询当前节点绑定的规则分
        List<RuleCandidateScore> bindRuleKeyList = ruleCandidateScoreMapper.findBindRuleKeyListByNode(strategy, node);
        List<String> stillBoundRuleKeys = new ArrayList<>();
        for (RuleScoreRelationOnline ruleScoreRelationOnline : dependRuleScoreRelationList) {
            for (RuleCandidateScore ruleCandidateScore : bindRuleKeyList) {
                if (ruleScoreRelationOnline.getRuleKey().equals(ruleCandidateScore.getRuleKey())) {
                    stillBoundRuleKeys.add(ruleScoreRelationOnline.getRuleKey());
                    break;
                }
            }
        }
        if (!stillBoundRuleKeys.isEmpty()) {
            throw new ValidateException("以下父规则分仍然绑定，不能解绑: " + String.join(", ", stillBoundRuleKeys));
        }

        // 删除绑定
        int i = ruleScoreStrategyMapper.deleteByRuleKeyVersion(ruleKey, ruleVersion, strategy, node);
        if (i == 0){
            throw new ValidateException("规则分解绑失败");
        }
        logger.info("规则分解绑成功, size={}", i);

        alertNotificationHandler.sendRuleUnbindingNotification(ruleKey, ruleVersion, node);

        // 规则集解绑后，更新策略节点变量使用信息
        updateUnBindStrategyNodeVariableUsage(strategy, node);
    }


    private void updateUnBindStrategyNodeVariableUsage(String strategyType, String strategyNodeCode) {
        Map<String, String> argMap = new HashMap<>();
        argMap.put("strategyNodeCode", strategyNodeCode);
        argMap.put("strategyType", strategyType);
        unBindNotifier.notifyListeners(argMap);
    }

    /**
     * 【*********】
     * @param ruleKey
     * @param ruleVersion
     * @param userKey
     */
    @Transactional(transactionManager = "adminTransactionManager")
    public void delete(String ruleKey, Integer ruleVersion,String userKey) {
        List<RuleScoreStrategy> ruleScoreStrategyList = ruleScoreStrategyMapper.findRuleScoreStrategyList(ruleKey, ruleVersion);
        if (CollectionUtils.isNotEmpty(ruleScoreStrategyList)){
            List<JSONObject> nodes = ruleScoreStrategyList.stream().map(ruleScoreStrategy -> {
                JSONObject node = new JSONObject();
                node.put("策略", ruleScoreStrategy.getStrategy());
                node.put("节点", ruleScoreStrategy.getNode());
                return node;
            }).collect(Collectors.toList());
            throw new ValidateException("此规则在:" + JSON.toJSONString(nodes) + "绑定");
        }

        List<RuleScoreRelation> ruleScoreChildRelationList = ruleScoreRelationMapper.findRuleScoreChildRelationList(ruleKey, ruleVersion);
        if (CollectionUtils.isNotEmpty(ruleScoreChildRelationList)){
            List<JSONObject> relations = new ArrayList<>();
            for (RuleScoreRelation ruleScoreRelation : ruleScoreChildRelationList){
                /** 已下线和已废弃过滤 **/
                RuleCandidateScore ruleCandidateScore =
                        ruleCandidateScoreMapper.findRuleCandidateScoreByRuleKeyVersion(ruleScoreRelation.getRuleKey(), ruleScoreRelation.getRuleVersion());
                if (ruleCandidateScore.getRuleStatus().intValue() == ruleCancel || ruleCandidateScore.getRuleStatus().intValue() == ruleOffline){
                    continue;
                }else {
                    JSONObject relation = new JSONObject();
                    relation.put("规则分:", ruleScoreRelation.getRuleKey());
                    relation.put("规则分版本:", ruleScoreRelation.getRuleVersion());
                    relations.add(relation);
                }
            }
            if (CollectionUtils.isNotEmpty(relations)) {
                throw new ValidateException("此规则在:" + JSON.toJSONString(relations) + "依赖");
            }
        }
        ruleScoreMapper.updateRuleScoreStatus(ruleKey,ruleVersion,userKey,new Date());
    }
}
