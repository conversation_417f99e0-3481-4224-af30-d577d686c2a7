package com.youxin.risk.admin.controller.policy;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.controller.BaseController;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminCdThirdPartySource;
import com.youxin.risk.admin.model.GwRequest;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.GwRequestService;
import com.youxin.risk.admin.service.ManualRunPolicyService;
import com.youxin.risk.admin.service.ThirdPartyDatasourceService;
import com.youxin.risk.commons.model.creditDriver.ManualRunPolicyChildTask;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date
 */
@Controller
@RequestMapping("/manualRunPolicy")
public class ManualRunPolicyController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ManualRunPolicyController.class);

    private static final int _1MB = 1024 * 1024;

    @Autowired
    ThirdPartyDatasourceService thirdPartyDatasourceService;

    @Autowired
    ManualRunPolicyService manualRunPolicyService;

    @RequestMapping(value = "/uploadUserkeys")
    @ResponseBody
	@SystemLog
    public ResponseEntity<?> uploadUserkeys(@RequestParam("file") MultipartFile[] files, @RequestParam("taskId") String taskId,
                                 @RequestParam("sourceSystem") String sourceSystem, @RequestParam("thirdPartyServiceKey") String thirdPartyServiceKey,
                                            @RequestParam("strategyType") String strategyType) {
        LoggerProxy.info("uploadUserkeys", logger, "taskId={},sourceSystem={},thirdPartyServiceKey={}", taskId, sourceSystem, thirdPartyServiceKey);
        //判断file数组不能为空并且长度大于0
        if(files!=null&&files.length>0){
            //循环获取file数组中得文件
            MultipartFile file = files[0];
            LoggerProxy.info(logger, "orginalFileName={}", file.getOriginalFilename());
            Map<String, String> errMsg = new HashMap<>(4);
            if (!checkFile(file, errMsg)) {
                String message = errMsg.get("message");
                return buildErrorResponse(message);
            }
            ManualRunPolicyChildTask manualRunPolicyChildTask = new ManualRunPolicyChildTask();
            manualRunPolicyChildTask.setTaskId(taskId);
            manualRunPolicyChildTask.setSourceSystem(sourceSystem);
            manualRunPolicyChildTask.setThirdPartyServiceKey(thirdPartyServiceKey);
            manualRunPolicyChildTask.setStrategyType(strategyType);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    manualRunPolicyService.handleTask(file, manualRunPolicyChildTask);
                }
            }).start();
        }
        return buildSuccessResponse("提交成功");
    }

    private boolean checkFile(MultipartFile file, Map<String, String> errMsg) {
        String[] parts = file.getOriginalFilename().split("\\.");
        String suffix = parts[parts.length - 1];
        if (!Objects.equals("txt", suffix)) {
            errMsg.put("message", "上传文件只能是txt格式！");
            return false;
        }
        if (file.getSize() > _1MB) {
            errMsg.put("message", "上传文件大小不能超过 1MB!");
            return false;
        }
        return true;
    }


    /**
     * 三方数据源配置
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/datasource/selectPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        params.put("pageNo", params.get("pageNum"));
        Page<AdminCdThirdPartySource> page = thirdPartyDatasourceService.selectPage(params);
        return buildSuccessResponse(page);
    }

    @ResponseBody
    @RequestMapping(value = "/datasource/get", method = RequestMethod.POST)
    public ResponseEntity<?> get(@RequestBody JSONObject request) {
        Long id = request.getLong("id");
        AdminCdThirdPartySource item = thirdPartyDatasourceService.get(id);
        return buildSuccessResponse(item);
    }

    @ResponseBody
    @RequestMapping(value = "/datasource/save", method = RequestMethod.POST)
	@SystemLog
    public ResponseEntity<?> save(@RequestBody AdminCdThirdPartySource request) {
        if(request.getId() == null){
            request.setCreateTime(new Date());
        }
        request.setUpdateTime(new Date());
        thirdPartyDatasourceService.save(request);
        return buildSuccessResponse(1);
    }

    @ResponseBody
    @RequestMapping(value = "/datasource/delete", method = RequestMethod.POST)
	@SystemLog
    public ResponseEntity<?> delete(@RequestBody JSONObject request) {
        Long id = request.getLong("id");
        thirdPartyDatasourceService.delete(id);
        return buildSuccessResponse(1);
    }



}
