package com.youxin.risk.admin.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CandidateStrategyStatusEnum {
    RUNNING("RUNNING", "运行中"),
    TEST_FAILED("TEST_FAILED", "运行失败"),
    WAIT_APPROVE("WAIT_APPROVE", "待审批"),
    IN_APPROVAL("IN_APPROVAL", "审批中"),
    WAIT_ONLINE("WAIT_ONLINE", "待上线"),
    ONLINE("ONLINE", "已上线"),
    OFFLINE("OFFLINE", "已下线"),
    DROPPED("DROPPED", "已废弃");

    private final String code;
    private final String desc;

    /**
     * 根据code查询枚举
     */
    public static CandidateStrategyStatusEnum getByCode(String code) {
        for (CandidateStrategyStatusEnum value : CandidateStrategyStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据code判断是否是终态
     */
    public static boolean isFinalStatus(String code) {
        return ONLINE.getCode().equals(code) || OFFLINE.getCode().equals(code) || DROPPED.getCode().equals(code);
    }
}
