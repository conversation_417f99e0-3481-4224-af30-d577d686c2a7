package com.youxin.risk.admin.vo.template;

import com.alibaba.fastjson.annotation.JSONField;
import com.yonxin.risk.approve.client.annotation.QwBaseControl;
import com.yonxin.risk.approve.client.client.RiskApproveClient;
import com.yonxin.risk.approve.client.model.ApproveSubmitReq;
import com.yonxin.risk.approve.client.model.template.BaseApproveTemplate;
import com.yonxin.risk.approve.client.model.template.base.Approver;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/15 17:12
 * @desc 策略上线操作审批模板
 */
@Setter
public class StrategyOnlineOpApproveTemplate extends BaseApproveTemplate {
    /**
     * 策略上线操作审批模板id
     */
    @JSONField(name = "template_id")
    private static String templateId = "3WLJ77GpukjfYji8sVR5pkQk93awMwuqHB43pfTa";

    public static void main(String[] args) {
        StrategyOnlineOpApproveTemplate template = new StrategyOnlineOpApproveTemplate("wangyefeng");
        template.setStrategyType("策略类型");
        template.buildApplyData();
        template.addSummaryInfo("操作类型：新增");
        ApproveSubmitReq approveSubmitReq = new ApproveSubmitReq();
        approveSubmitReq.setApproveTemplateValue(template.toString());
        approveSubmitReq.setUserName("wangyefeng");
        approveSubmitReq.setCallbackUrl("");
        RiskApproveClient.submit(approveSubmitReq);
    }


    public StrategyOnlineOpApproveTemplate(String creatorUserId, List<String> notifyers, List<Approver> approvers) {
        super(templateId, creatorUserId, notifyers, approvers);
    }

    public StrategyOnlineOpApproveTemplate(String creatorUserId) {
        this(creatorUserId, null, null);
    }

    /**
     * 业务线
     */
    @QwBaseControl(id = "Text-03")
    private String sourceSystem;

    /**
     * 策略类型
     */
    @QwBaseControl(id = "Text-04")
    private String strategyType;

    /**
     * 策略版本
     */
    @QwBaseControl(id = "Text-05")
    private String strategyVersion;

    /**
     * 申请人
     */
    @QwBaseControl(id = "Text-06")
    private String applicant;

    /**
     * 申请名称
     */
    @QwBaseControl(id = "Text-07")
    private String applicationName;

    /**
     * 申请内容
     */
    @QwBaseControl(id = "Textarea-01")
    private String applicationContent;

    /**
     * 申请原因
     */
    @QwBaseControl(id = "Textarea-02")
    private String applicationReason;

}
