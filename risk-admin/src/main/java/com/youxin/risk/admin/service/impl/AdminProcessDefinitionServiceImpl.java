package com.youxin.risk.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.youxin.risk.admin.dao.admin.AdminEventInfoMapper;
import com.youxin.risk.admin.dao.admin.AdminProcessDefinitionMapper;
import com.youxin.risk.admin.dao.admin.AdminProcessNodeMapper;
import com.youxin.risk.admin.model.*;
import com.youxin.risk.admin.service.AdminProcessDefinitionService;
import com.youxin.risk.admin.utils.Constant;
import com.youxin.risk.commons.constants.NodeTypeEnum;
import com.youxin.risk.commons.model.ProcessDefinition;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/13 12:01
 */
@Service
public class AdminProcessDefinitionServiceImpl extends BaseServiceImpl<ProcessDefinition> implements AdminProcessDefinitionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdminProcessDefinitionServiceImpl.class);

    @Resource
    private AdminProcessDefinitionMapper adminProcessDefinitionMapper;
    @Resource
    private AdminEventInfoMapper adminEventInfoMapper;
    @Resource
    private AdminProcessNodeMapper adminProcessNodeMapper;

    @Override
    protected void init() {
        tableName = "admin_process_definition";
        baseMapper = adminProcessDefinitionMapper;
    }

    @Override
    public List<ProcessDefinition> selectForEvent() {
        return adminProcessDefinitionMapper.selectForEvent();
    }

    @Override
    public List<ProcessDefinition> selectGraphEvent() {
        return adminProcessDefinitionMapper.selectGraphEvent();
    }

    @Override
    public Boolean checkDependency(String processDefId) {
        List<AdminEventInfo> adminEventInfos = adminEventInfoMapper.selectByProcessDefId(processDefId);
        if (CollectionUtils.isNotEmpty(adminEventInfos)) {
            return true;
        }
        return false;
    }

    @Override
    public ProcessDefinition getByProcessDefId(String processDefId) {
        return adminProcessDefinitionMapper.getByProcessDefId(processDefId);
    }

    @Override
    public Integer getMaxVersion(String referrenceId) {
        return adminProcessDefinitionMapper.getMaxVersion(referrenceId);
    }

    @Override
    public List<ProcessDefinition> getByReferrenceIds(List<String> referrenceIds) {
        return adminProcessDefinitionMapper.getByReferrenceIds(referrenceIds);
    }

    @Override
    public List<Integer> getVersions(String referrenceId) {
        List<ProcessDefinition> versions = adminProcessDefinitionMapper.getVersions(referrenceId);
        ArrayList<Integer> ret = new ArrayList<>();
        if(CollectionUtils.isEmpty(versions)){
            return ret;
        }
        for (ProcessDefinition definition : versions){
            ret.add(definition.getVersion());
        }
        return ret;
    }


    @Override
    public List<Integer> getActiveVersions(String referrenceId) {
        List<ProcessDefinition> versions = adminProcessDefinitionMapper.getVersions(referrenceId);
        ArrayList<Integer> ret = new ArrayList<>();
        if(CollectionUtils.isEmpty(versions)){
            return ret;
        }
        for (ProcessDefinition definition : versions){
            if("ENABLE".equalsIgnoreCase(definition.getStatus())) { // 只查询启用的版本
                ret.add(definition.getVersion());
            }
        }
        return ret;
    }

    @Override
    public Page<ProcessDefinition> selectPage(JSONObject params){
        List<Integer> ids = adminProcessDefinitionMapper.selectIds(params);
        int pageNo = params.containsKey("pageNum") ? (Integer) params.get("pageNum") : 1;
        int pageSize = params.containsKey("pageSize") ? (Integer) params.get("pageSize") : 20;
        int start = (pageNo - 1) * pageSize;
        int limit = pageSize;
        List<ProcessDefinition> adminProcessDefinitions = null;
        if(CollectionUtils.isNotEmpty(ids)){
            adminProcessDefinitions = adminProcessDefinitionMapper.
                    selectListById(ids,start,limit);
        }
        params.put("start", start);
        params.put("limit", limit);
        Page<ProcessDefinition> page = new Page<>();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotal(new Long(ids.size()) );
        page.setList(adminProcessDefinitions);
        return page;
    }

    @Override
    public List<ProcessDefinition> selectEffectives() {
        return adminProcessDefinitionMapper.selectEffectives();
    }

    @Override
    public void updateInUse(String referrenceId) {
        adminProcessDefinitionMapper.updateInUse(referrenceId);
    }

    @Override
    public void updateInUseTrue(String referrenceId, Integer version) {
        adminProcessDefinitionMapper.updateInUseTrue(referrenceId,version);
    }

    @Override
    public void updateDisable(String referrenceId, Integer version) {
        ProcessDefinition adminProcessDefinition = this.getByReferrenceId(referrenceId, version);
        if (adminProcessDefinition == null) {
            throw new RuntimeException("流程不存在！");
        }

        // 判断是否是子流程
        if(!BooleanUtils.isTrue(adminProcessDefinition.getSubProcess())){
            // 主流程
            // 获取主流程引用的子流程id+version
            String referrenceProcessDefIds = adminProcessDefinition.getReferrenceProcessDefIds();
            if (StringUtils.isNotBlank(referrenceProcessDefIds)) {
                String[] subProcessDefIds = referrenceProcessDefIds.split(",");
                // 更新子流程中referrenceProcessDefIds，移除当前主流程的processDefId
                for (String subProcessDefId : subProcessDefIds) {
                    ProcessDefinition subProcess = adminProcessDefinitionMapper.getByProcessDefId(subProcessDefId);
                    if (subProcess == null) {
                        throw new RuntimeException("子流程不存在，子流程id=" + subProcessDefId);
                    }
                    String referrenceProcessDefIds1 = subProcess.getReferrenceProcessDefIds();
                    String[] referrenceProcessDefIdsArray = referrenceProcessDefIds1.split(",");
                    List<String> newReferrenceProcessDefIdsList = new ArrayList<>();
                    for (String referrenceProcessDefId : referrenceProcessDefIdsArray) {
                        // 排除当前子流程引用的主流程idv
                        if (!referrenceProcessDefId.equals(String.format("%s%s%d", referrenceId, Constant.POUND, version))) {
                            newReferrenceProcessDefIdsList.add(referrenceProcessDefId);
                        }
                    }
                    String newReferrenceProcessDefIds = StringUtils.join(newReferrenceProcessDefIdsList, ",");
                    adminProcessDefinitionMapper.updateReferrenceProcessDefIds(newReferrenceProcessDefIds, subProcess.getId());
                    LOGGER.info("废弃流程成功，主流程id={}，子流程id={}，referrenceProcessDefIds={}", referrenceId, subProcessDefId, newReferrenceProcessDefIds);
                    // 如果子流程引用的主流程已经没有的话，更新子流程的inUse为false
                    if (newReferrenceProcessDefIdsList.isEmpty()) {
                        adminProcessDefinitionMapper.updateInUseFalse(subProcess.getReferrenceId(), subProcess.getVersion());
                        LOGGER.info("废弃流程成功，主流程id={}，子流程id={}，inUse=false", referrenceId, subProcessDefId);
                    }
                }
            }
        }

        // 判断是否正在使用
        if (BooleanUtils.isTrue(adminProcessDefinition.getInUse())) {
            throw new RuntimeException("废弃失败，此流程正在使用！");
        }
        int i = adminProcessDefinitionMapper.updateDisable(referrenceId, version);
        if (i > 0) {
            LOGGER.info("废弃流程成功，referrenceId={}, version={}", referrenceId, version);
        }
    }


    @Override
    public ProcessDefinition getByReferrenceId(String referrenceId, Integer version) {
        return adminProcessDefinitionMapper.getByReferrenceId(referrenceId,version);
    }

    @Override
    public ProcessDefinition getInUse(String referrenceId){
        return adminProcessDefinitionMapper.getInUse(referrenceId);
    }

    @Override
    public void preDeal(ProcessDefinition request){
        String referId = request.getProcessDefId();
        if(referId.contains(Constant.POUND)){
            String[] array = referId.split(Constant.POUND);

            //若 processJson 不为空，说明是新流程，referId截取 "#" 前的部分
            if(!StringUtils.isBlank(request.getProcessJson())){
                referId = array[0];
            }
        }
        Integer maxVersion = adminProcessDefinitionMapper.getMaxVersion(referId);
        int newVersion = 0;
        if(maxVersion != null){
            newVersion = maxVersion + 1;
        }
        request.setVersion(newVersion);
        if(newVersion!=0){
            request.setProcessDefId(referId + Constant.POUND + newVersion);
        }
    }


    @Override
    public void saveOrUpdate(ProcessDefinition request, List<Element> serialDatas){
        //清空
        request.setId(null);
        String referId = request.getProcessDefId();
        if(referId.contains(Constant.POUND)){
            String[] array = referId.split(Constant.POUND);

            //若 processJson 不为空，说明是新流程，referId截取 "#" 前的部分
            if(!StringUtils.isBlank(request.getProcessJson())){
                referId = array[0];
            }

        }
        request.setReferrenceId(referId);
        if(StringUtils.isBlank(request.getProcessJson())){
            List<ProcessDefinition> versions = adminProcessDefinitionMapper.getVersions(referId);
            if(CollectionUtils.isEmpty(versions)){
                request.setInUse(true);
            }else {
                request.setInUse(false);
            }
        }else {
            // 新流程创建 默认不在使用
            request.setInUse(false);
        }

        //创建串行数据节点
        createSerialDataNode(serialDatas);

        List<String> subReferrenceIds = new ArrayList<>();
        //子流程处理
        try {
            handleSubProcess(request, subReferrenceIds);
        } catch (Exception e) {
            LOGGER.error("子流程处理异常：主流程id={}，errMsg=", request.getProcessDefId(), e);
        }

        // 更新主流程引用的子流程id+version
        if(!BooleanUtils.isTrue(request.getSubProcess())){
            request.setReferrenceProcessDefIds(StringUtils.join(subReferrenceIds, ","));
        }
        //更新引用流程的版本
        super.save(request);
    }

    private void handleSubProcess(ProcessDefinition request, List<String> subReferrenceIds) {
        //主流程且processJson不为空，说明是引用子流程的主流程
        if (BooleanUtils.isTrue(request.getSubProcess()) || StringUtils.isBlank(request.getProcessJson())) {
            return;
        }
        if (!request.getProcessJson().contains("SUB_PROCESS")) {
            return;
        }
        LOGGER.info("子流程处理：主流程id={}", request.getProcessDefId());
        JSONArray nodes = JSON.parseObject(request.getProcessJson()).getJSONArray("nodes");
        for (int i = 0; i < nodes.size(); i++) {
            JSONObject node = nodes.getJSONObject(i);
            if ("SUB_PROCESS".equals(node.getString("type"))) {
                String subProcessDefId = node.getString("subProcessDefId");
                subReferrenceIds.add(subProcessDefId); // 主流程引用的子流程id+version
                ProcessDefinition subProcess = adminProcessDefinitionMapper.getByProcessDefId(subProcessDefId);
                if (subProcess == null) {
                    throw new RuntimeException("子流程不存在，子流程id=" + subProcessDefId);
                }else {
                    // subProcess 中referrenceProcessDefIds如果不为空，那么逗号拼接分隔，添加当前主流程的processDefId
                    String referrenceProcessDefIds = subProcess.getReferrenceProcessDefIds();
                    if (StringUtils.isBlank(referrenceProcessDefIds)) {
                        referrenceProcessDefIds = request.getProcessDefId();
                    } else {
                        referrenceProcessDefIds = referrenceProcessDefIds + "," + request.getProcessDefId();
                    }
                    adminProcessDefinitionMapper.updateReferrenceProcessDefIds(referrenceProcessDefIds, subProcess.getId());
                    LOGGER.info("子流程处理：主流程id={}，子流程id={}，referrenceProcessDefIds={}", request.getProcessDefId(),
                            subProcessDefId, referrenceProcessDefIds);
                }
                if (subProcessDefId.contains(Constant.POUND)) {
                    int index = subProcessDefId.lastIndexOf(Constant.POUND);
                    String subProcessDefIdWithOutVersion = subProcessDefId.substring(0, index);
                    int version = Integer.parseInt(subProcessDefId.substring(index + 1));
                    adminProcessDefinitionMapper.updateInUse(subProcessDefIdWithOutVersion);
                    adminProcessDefinitionMapper.updateInUseTrue(subProcessDefIdWithOutVersion, version);

                    LOGGER.info("子流程处理：主流程id={}，子流程id={}，在用版本更新为={}", request.getProcessDefId(),
                            subProcessDefIdWithOutVersion, version);
                }
            }
        }

    }

    private Integer maxVersion(List<ProcessDefinition> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        int max = 0;
        for(ProcessDefinition value : list){
            if(value.getVersion() > max){
                max = value.getVersion();
            }
        }
        return max;
    }

    //创建串行数据节点
    public void createSerialDataNode(List<Element> serialDatas){
        if(CollectionUtils.isNotEmpty(serialDatas)){
            boolean update = false;
            for(Element element : serialDatas){
                AdminProcessNode adminProcessNode = new AdminProcessNode();
                String nodeCode = element.attributeValue("id");
                AdminProcessNode node = adminProcessNodeMapper.getByNodeCode(nodeCode);
                if(node != null){
                    continue;
                }
                adminProcessNode.setNodeCode(nodeCode);
                adminProcessNode.setNodeName(nodeCode);
                adminProcessNode.setNodeType(NodeTypeEnum.DATA.name());
                adminProcessNode.setTaskName(nodeCode);
                adminProcessNode.setWaitNotRequired(false);
                adminProcessNode.setCreateTime(new Date());
                adminProcessNode.setUpdateTime(new Date());
                adminProcessNodeMapper.insert(adminProcessNode);
                update = true;
            }
            if(update == true){
                updateSysDbUpdateTimeRelated(false,"admin_process_node");
            }
        }
    }

    @Override
    public List<ProcessDefinition> selectByStrategyType(String strategyType) {
        return adminProcessDefinitionMapper.selectAllByStrategyType(strategyType);
    }

    @Override
    public List<String> selectProcessDefIdBySubProcess(Integer isSubProcess) {
        return adminProcessDefinitionMapper.selectProcessDefIdBySubProcess(isSubProcess);
    }

    @Override
    public Map<String, Map<String, String>> getVariableInProcess(List<String> varCodes) {
        List<ProcessDefinition> adminProcessDefinitions = this.selectAllEffectivesProcessDefinition();
        Map<String, Map<String, String>> varMap = new HashMap<>();
        if (org.springframework.util.CollectionUtils.isEmpty(adminProcessDefinitions)) {
            return varMap;
        }

        Map<String, Set<String>> eventMap = selectAllDefinitionEventMap();
        // 正则表达式
        String s = "event\\.variableMap\\.(%s)\\s*([!=<>]=?)";

        adminProcessDefinitions.forEach(processDefinition -> {
            varCodes.forEach(varCode -> {
                String regex = String.format(s, varCode);
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(processDefinition.getProcessContent());
                if (!matcher.find() || StringUtils.isBlank(matcher.group(1))) {
                    return;
                }
                Map<String, String> map = varMap.getOrDefault(varCode, new HashMap<>());
                Set<String> eventCodes = eventMap.get(processDefinition.getProcessDefId());
                if (CollectionUtils.isNotEmpty(eventCodes)) {
                    eventCodes.forEach(eventCode -> {
                        String val = map.getOrDefault(eventCode, "");
                        if (StringUtils.isBlank(val)) {
                            map.put(eventCode, processDefinition.getProcessDefId());
                        } else {
                            map.put(eventCode, val +","+ processDefinition.getProcessDefId());
                        }
                    });
                }
                varMap.put(varCode, map);
            });
        });

        return varMap;
    }

    private List<ProcessDefinition> selectAllEffectivesProcessDefinition() {
        List<AdminEventInfo> infos = adminEventInfoMapper.selectAll(null);

        List<String> ids = infos.stream().map(AdminEventInfo::getProcessDefId).
                collect(Collectors.toList());
        List<ProcessDefinition> definitions = adminProcessDefinitionMapper.getByProcessDefIds(ids);
        List<String> subDefinitionIds = definitions.stream()
                .map(ProcessDefinition::getSubProcessDefIds)
                .flatMap(Collection::stream)
                .filter(id->!ids.contains(id))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subDefinitionIds)) {
            List<ProcessDefinition> subDefinitions = adminProcessDefinitionMapper.getByProcessDefIds(subDefinitionIds);
            definitions.addAll(subDefinitions);
        }
        return definitions;
    }

    private Map<String, Set<String>> selectAllDefinitionEventMap() {
        List<AdminEventInfo> infos = adminEventInfoMapper.selectAll(null);

        List<String> ids = infos.stream().map(AdminEventInfo::getProcessDefId).
                collect(Collectors.toList());
        Map<String, Set<String>> map = infos.stream().
                collect(Collectors.toMap(AdminEventInfo::getProcessDefId, info -> Sets.newHashSet(info.getEventCode()),
                        (list1, list2) -> { // mergeFunction 合并角色列表
                            Set<String> merged = new HashSet<>(list1);
                            merged.addAll(list2);
                            return merged;
                        }));

        List<ProcessDefinition> definitions = adminProcessDefinitionMapper.getByProcessDefIds(ids);
        definitions.stream()
                .forEach(definition -> {
                    Set<String> mainList = map.getOrDefault(definition.getProcessDefId(), new HashSet<>());

                    definition.getSubProcessDefIds().stream().forEach(subId -> {
                        Set<String> subList = map.getOrDefault(subId, new HashSet<>());
                        subList.addAll(mainList);

                        map.put(subId, subList);
                    });
                });

        return map;
    }
}
