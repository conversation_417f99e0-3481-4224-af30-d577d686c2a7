/*
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.service.impl;

import com.youxin.risk.admin.dao.rm.AdminStrategyMapper;
import com.youxin.risk.admin.model.AdminStrategy;
import com.youxin.risk.admin.service.AdminStrategyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-29
 */
@Service
public class AdminStrategyServiceImpl extends BaseServiceImpl<AdminStrategy> implements AdminStrategyService{
    @Resource
    private AdminStrategyMapper adminStrategyMapper;

    @Override
    public List<String> selectTypes() {
        return adminStrategyMapper.selectTypes();
    }

    @Override
    public AdminStrategy selectByCodeId(String codeId) {
        return adminStrategyMapper.selectByCodeId(codeId);
    }

    @Override
    protected void init() {
        baseMapper = adminStrategyMapper;
    }
}