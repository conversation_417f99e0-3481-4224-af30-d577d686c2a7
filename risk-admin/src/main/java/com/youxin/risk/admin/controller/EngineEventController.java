package com.youxin.risk.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.admin.annotation.BusinessIsolation;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.EngineEvent;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.EngineEventService;
import com.youxin.risk.admin.vo.EngineEventVo;
import com.youxin.risk.commons.constants.EngineEventStatusEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.EventHandleResult;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/13 13:45
 */
@Controller
@RequestMapping("/engineEvent")
public class EngineEventController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(EngineEventController.class);

    @Resource
    private EngineEventService engineEventService;

    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/selectPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        params.put("pageNo", params.get("pageNum"));
        Page<EngineEvent> page = engineEventService.selectPage(params);
        return buildSuccessResponse(page);
    }

    @BusinessIsolation
    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseEntity<?> get(@RequestBody JSONObject request) {
        Long id = request.getLong("id");
        EngineEventVo engineEventVo = engineEventService.getVo(id);
        return buildSuccessResponse(engineEventVo);
    }

    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
	@SystemLog
    public ResponseEntity<?> save(@RequestBody EngineEvent request) {
        EngineEventStatusEnum engineEventStatusEnum = null;
        try{
            engineEventStatusEnum = EngineEventStatusEnum.valueOf(request.getStatus());
        }catch (Exception ex){
            LoggerProxy.error("saveEngineEventError", LOGGER, "requestParam=" + JSONObject.toJSONString(request));
            return buildErrorResponse("status状态不合法");
        }
        if(engineEventStatusEnum == EngineEventStatusEnum.FAILED){//将引擎事件状态修改为人工处理状态，并更新create_time。
            request.setCreateTime(new Date());
        }
        request.setStatus(request.getStatus());
        engineEventService.save(request);
        return buildSuccessResponse("setStatusSuccess");
    }
}
