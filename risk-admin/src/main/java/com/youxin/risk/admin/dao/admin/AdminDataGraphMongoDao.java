/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.dao.admin;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import com.youxin.risk.admin.vo.AdminDataGraphVo;
import com.youxin.risk.commons.mongo.BaseMongoDao;
import com.youxin.risk.commons.vo.ProcessSplitFlowResultVo;

public class AdminDataGraphMongoDao extends BaseMongoDao {
    public AdminDataGraphMongoDao() {
        collectionName = "AdminDataGraph";
    }

    public AdminDataGraphVo getByGroupCode(String groupCode) {
        Query query = Query.query(Criteria.where("groupCode").is(groupCode));
        return template.findOne(query, AdminDataGraphVo.class, collectionName);
    }

    public void update(AdminDataGraphVo vo) {
        Query query = Query.query(Criteria.where("_id").is(vo.getId()));
        Update u = new Update();
        u.set("data", vo.getData());
        template.updateFirst(query, u, collectionName);
    }

    public void insertOrUpdate(AdminDataGraphVo vo) {
        if (vo.getId() != null) {
            update(vo);
        } else {
            insert(vo);
        }
    }
}
