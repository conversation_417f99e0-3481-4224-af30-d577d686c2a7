package com.youxin.risk.admin.model.subscription;

import java.util.List;
import java.util.Map;

/**
 * SLS 配置实体类
 * 
 * <AUTHOR> Assistant
 */
public class SLSConfig {

    /**
     * 仪表盘名称
     */
    private String dashboardName;

    /**
     * Token 参数列表
     */
    private List<Map<String, String>> token;

    /**
     * 扩展配置参数列表
     */
    private List<Map<String, String>> extensions;

    public SLSConfig() {
    }

    public SLSConfig(String dashboardName, List<Map<String, String>> token, List<Map<String, String>> extensions) {
        this.dashboardName = dashboardName;
        this.token = token;
        this.extensions = extensions;
    }

    public String getDashboardName() {
        return dashboardName;
    }

    public void setDashboardName(String dashboardName) {
        this.dashboardName = dashboardName;
    }

    public List<Map<String, String>> getToken() {
        return token;
    }

    public void setToken(List<Map<String, String>> token) {
        this.token = token;
    }

    public List<Map<String, String>> getExtensions() {
        return extensions;
    }

    public void setExtensions(List<Map<String, String>> extensions) {
        this.extensions = extensions;
    }

    @Override
    public String toString() {
        return "SLSConfig{" +
                "dashboardName='" + dashboardName + '\'' +
                ", token=" + token +
                ", extensions=" + extensions +
                '}';
    }
}
