package com.youxin.risk.admin.controller;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminLibraryConf;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.admin.service.AdminLibraryConfService;

/**
 *
 */
@Controller
@RequestMapping("/libraryConf")
public class AdminLibraryConfController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminLibraryConfController.class);

    @Resource
    private AdminLibraryConfService adminLibraryConfService;

    @ResponseBody
    @RequestMapping(value = "/selectPage", method = RequestMethod.POST)
    public ResponseEntity<?> selectPage(@RequestBody JSONObject params) {
        Page<AdminLibraryConf> page = adminLibraryConfService.selectPage(params);
        return buildSuccessResponse(page);
    }

    @ResponseBody
    @RequestMapping(value = "/selectAll", method = RequestMethod.POST)
    public ResponseEntity<?> selectAll(@RequestBody JSONObject params) {
        List<AdminLibraryConf> result = adminLibraryConfService.selectAll(null);
        return buildSuccessResponse(result);
    }

    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> save(@RequestBody AdminLibraryConf request) {
        adminLibraryConfService.save(request);
        return buildSuccessResponse(1);
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @SystemLog
    public ResponseEntity<?> delete(@RequestBody JSONObject request) {
        Long id = request.getLong("id");
        adminLibraryConfService.delete(id);
        return buildSuccessResponse(1);
    }

    @ResponseBody
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseEntity<?> get(@RequestBody JSONObject request) {
        Long id = request.getLong("id");
        AdminLibraryConf adminLibraryConf = adminLibraryConfService.get(id);
        return buildSuccessResponse(adminLibraryConf);
    }
}
