mode.name=test
app.name=risk-admin

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=/opt/app/tomcat/logs

tomcat.home=${home.base}/products/tomcat/tomcat_risk_admin
tomcat.port=8011
tomcat.shutdown.port=8012
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider
-Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.engine.maxActive=1
datasource.engine.initialSize=1
datasource.engine.minIdle=1

datasource.admin.maxActive=50
datasource.admin.initialSize=2
datasource.admin.minIdle=2


datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=root
admin.datasource.pwd=aeagh12da45O2GBfoin

di.datasource.url=**************************************?${datasource.url.params}
di.datasource.username=test
di.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

channel.datasource.url=*******************************************?${datasource.url.params}
channel.datasource.username=test|
channel.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

gw.datasource.url=*******************************************?${datasource.url.params}
gw.datasource.username=test
gw.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

engine.datasource.url=*****************************************?${datasource.url.params}
engine.datasource.username=root
engine.datasource.pwd=aeagh12da45O2GBfoin

rm.datasource.url=*****************************************?${datasource.url.params}
rm.datasource.username=risk
rm.datasource.pwd=aeagh12da45O2GBfoin

cp.datasource.url=*********************************?${datasource.url.params}
cp.datasource.username=root
cp.datasource.pwd=1234

redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=passwd456
redis.cluster.nodes=************:7000,************:7001,************:7002,\
  ************:7100,************:7101,************:7102


admin.login.domain=@ucredit.com
admin.login.url=ldap://************:389

admin.login.sso.server=http://***********:8080/v1/serviceTicket?service=
admin.login.self.server=http://cp.test.ucredit.com
admin.login.sso.private.key=0KVXaN-nlXafRUwg
admin.login.sso.encryption.key=0KVXaN-nlXafRUwgsr3H_l6hkufY7lzoTy7OVI5pN0E
admin.login.sso.signing.key=K-9Nwl0UdVtkObDF81-uveuArKPVF4oVg0weQTt9mall8CHIsOlvMSnzi5tZ-xC1V6ALPWe_RfP5981KLL8PTQ


mongo.host=************:27017
mongo.username=test
mongo.password=test
mongo.database=transfer
mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}

mongo.risk.host=***********:27017
mongo.risk.username=test
mongo.risk.password=test
mongo.risk.database=risk
mongo.risk.credentials=${mongo.risk.username}:${mongo.risk.password}@${mongo.risk.database}

mongo.riskExp.host=************:27017
mongo.riskExp.username=test
mongo.riskExp.password=test
mongo.riskExp.database=risk
mongo.riskExp.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

#mongo.sharding.host=************:27017
#mongo.sharding.username=risk
#mongo.sharding.password=j8cWZbL9PHK3NUdAeNBB
#mongo.sharding.database=risk
#mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

mongo.sharding.host=************:27017
mongo.sharding.username=test
mongo.sharding.password=test
mongo.sharding.database=risk
mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

alertlog.datasource.url=*****************************************?${datasource.url.params}
alertlog.datasource.username=test
alertlog.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

admin.token.expire.time.seconds=21600

alert.collect.config.url = http://antifraud-risk-alert.test.rrdbg.com/alert/admin/
alert.base.url = http://***********:8051


kafka.dp.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
kafka.mirror.dp.hosts=***********:9092,***********:9092,***********:9092
kafka.admin.cd.topic=risk.admin.cd.userKey.topic.test

# verify manage
verify.manage.baseurl=http://************:9080

admin.permission.host = http://antifraud-authorization.test.weicai.com.cn/
cp.host = http://************:80
cp.auth.host = http://cp-web.test.weicai.com.cn/api
role.public.list = /role/public/list
perm.public.list = /perm/public/list2

dp.trigger.callback.url=http://crawlers.test.rrdbg.com/crawlers/v1/crawl/record/notify
ra.base.url=http://***********:13080
rrd.service.url=http://************:9100
gw.service.url=http://antifraud-risk-gateway.test.rrdbg.com/risk/api/analyse/v2
gateway.url=http://antifraud-risk-gateway.test.rrdbg.com

pudao.url=http://risk-ui-pudao.testp.weicai.com.cn/api
heika.url=http://antifraud-risk-admin-heika.test.weicai.com.cn/api
rongdan.url=http://antifraud-risk-admin-rongdan.test.weicai.com.cn/api
weicai.url=http://antifraud-risk-admin.test.weicai.com.cn/api

admin.black.server.host=http://***********:8040
admin.login.sso.server.excute.host=http://***********:8080/?service=
admin.special.check.url=/mongodb/operation/execute

#fs.service.url=http://***********:8121
fs.service.url=http://antifraud-risk-fs.test.rrdbg.com
heika.fs.service.url=http://antifraud-risk-fs-heika.test.weicai.com.cn
pudao.fs.service.url=http://antifraud-risk-fs-pudao.test.weicai.com.cn
rongdan.fs.service.url=http://antifraud-risk-fs-rongdan.test.weicai.com.cn

risk.assistant.url=http://risk-assistant.test.rrdbg.com

channel.forward.url=http://antifraud-risk-channel.test.rrdbg.com

risk.verify.url=http://antifraud-risk-verify.test.rrdbg.com

youxin.env=DEV
apollo.mate=http://risk-apollo-configservice.test.rrdbg.com

cd.url=http://***********:8101

risk.dc.url=http://***********:8071

risk.dc.inside.url=http://antifraud-risk-datacenter-inside.test.weicai.com.cn

risk.admin.url=http://antifraud-risk-admin.test.weicai.com.cn
domain.url=http://antifraud-risk-admin.test.weicai.com.cn/api
risk.approve.service.url=http://antifraud-risk-approve.test.weicai.com.cn
process.engine=http://antifraud-risk-process-engine.test.rrdbg.com
pudao.engine.url=http://antifraud-risk-process-engine-pudao.test.weicai.com.cn
heika.engine.url=http://antifraud-risk-process-engine-heika.test.weicai.com.cn
rongdan.engine.url=http://antifraud-risk-process-engine-rongdan.test.weicai.com.cn
cp.util.getUserParent=http://************:80/api/user/getUserParent?sourceUser=

xxl.job.admin.addresses=http://risk-xxl-job-manager.test.rrdbg.com
xxl.job.accessToken=
xxl.job.executor.appname=risk-admin
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

transfer.mongo.host=************:27017
transfer.mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}

influx.url=http://***********:8086
influx.user=admin
influx.password=admin
influx.database=antifraud_risk_monitor
influx.database.engine=antifraud_risk_engine
influx.antifraud.database.risk=renrendai_antifraud_risk
influx.antifraud.database.risk.rm=antifraud_risk_rm

mail.from=<EMAIL>
mail.host=smtp.qf.ucredit.com
mail.port=25
mail.username=<EMAIL>
mail.password=hp7V6ZgL
mail.properties.mail.smtp.auth=true
mail.properties.mail.smtp.timeout=10000
mail.properties.mail.debug=false

wechat.credit.warn.url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
feature.wechat.bot.key = 5bc5940f-7e07-4a1a-9095-a297f5080fb7
wechat.base.url = https://qyapi.weixin.qq.com/cgi-bin
wechat.risk.plat.aesKey=Q1DYbd7Cvmvwq2C4o2GHSKh5OlLI39KEB3V8MAxaJ11
wechat.risk.plat.token=6GpB4bENhIYWN3ZWYvxaTE
wechat.risk.plat.corpId=ww63478d6a674cf652

youxin.environment.type=weicai


weicai_pudao_gw_url=http://risk-api-gateway.test.rrdbg.com/puDaoApi
weicai_heika_gw_url=http://risk-api-gateway.test.rrdbg.com/heiKaApi
weicai_rongdan_gw_url=http://risk-api-gateway.test.rrdbg.com/rongDanApi

pudao_weicai_gw_url=http://risk-api-gateway-pudao.test.weicai.com.cn/weiCaiApi

heika_weicai_gw_url=http://risk-api-gateway-heika.test.weicai.com.cn/weiCaiApi
heika_rongdan_gw_url=http://risk-api-gateway-heika.test.weicai.com.cn/rongDanApi

rongdan_weicai_gw_url=http://risk-api-gateway-rongdan.test.weicai.com.cn/weiCaiApi
rongdan_heika_gw_url=http://risk-api-gateway-rongdan.test.weicai.com.cn/heiKaApi

system.check.robotKey=32f7544e-4acc-4193-b4dd-6c7d3b4b7e05

variable.url=http://risk-variable-gateway.test.weicai.com.cn

risk.admin.heika.url=https://antifraud-risk-admin-heika.test.weicai.com.cn/api
risk.admin.rongdan.url=https://antifraud-risk-admin-rongdan.test.weicai.com.cn/api