# SLSLinkGenerator 配置文件名修复说明

## 问题描述

在实现统一配置方案时，发现 `SLSLinkGenerator` 类中的多个方法仍在使用旧的配置文件名 `"strategy-monitor-mapping"`，而不是新的统一配置文件名 `"strategy-monitor-unified-config"`。这导致无法正确从统一配置中获取监控配置信息。

## 修复内容

### 1. 修复的方法

#### getMonitorConfigByStrategyAndId()
**修复前：**
```java
String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-mapping", "");
```

**修复后：**
```java
String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-unified-config", "");
```

#### getEventCodeMonitorConfigFromUnified()
**修复前：**
```java
String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-mapping", "");
```

**修复后：**
```java
String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-unified-config", "");
```

#### isEventCodeConfiguredInUnifiedConfig()
**修复前：**
```java
String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-mapping", "");
```

**修复后：**
```java
String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-unified-config", "");
```

### 2. StrategyMonitorConfigService 配置常量修复

**修复前：**
```java
private static final String CONFIG_DATA_ID = "strategy-monitor-mapping";
```

**修复后：**
```java
private static final String CONFIG_DATA_ID = "strategy-monitor-unified-config";
```

## 增强功能

### 1. 改进的错误处理

#### 详细的日志记录
```java
logger.debug("开始解析统一配置, strategyType={}, monitorId={}", strategyType, monitorId);
logger.warn("策略类型 '{}' 在统一配置中没有对应的配置。", strategyType);
logger.error("解析统一配置 strategy-monitor-unified-config 失败, strategyType={}, monitorId={}, error={}", 
        strategyType, monitorId, e.getMessage(), e);
```

#### 参数验证
```java
// 验证 dashboardName 不为空
String dashboardName = slsConfig.getString("dashboardName");
if (dashboardName == null || dashboardName.trim().isEmpty()) {
    logger.error("监控项 '{}' 的 dashboardName 为空, strategyType={}", monitorId, strategyType);
    return null;
}

// 验证 token 配置的完整性
if (key != null && value != null) {
    tokenMap.put("key", key);
    tokenMap.put("value", value);
    tokenList.add(tokenMap);
} else {
    logger.warn("Token配置中存在空的key或value, strategyType={}, monitorId={}", strategyType, monitorId);
}
```

### 2. 增强的配置解析

#### 更安全的配置解析
```java
// 安全地解析 extensions 配置
if (extensionsArray != null) {
    for (int j = 0; j < extensionsArray.size(); j++) {
        JSONObject extensionObj = extensionsArray.getJSONObject(j);
        if (extensionObj != null) {
            Map<String, String> extensionMap = new HashMap<>();
            for (String key : extensionObj.keySet()) {
                String value = extensionObj.getString(key);
                if (key != null && value != null) {
                    extensionMap.put(key, value);
                }
            }
            if (!extensionMap.isEmpty()) {
                extensionsList.add(extensionMap);
            }
        }
    }
}
```

#### 详细的成功日志
```java
logger.info("成功从统一配置获取监控配置, strategyType={}, monitorId={}, dashboardName={}, tokenCount={}, extensionCount={}", 
        strategyType, monitorId, dashboardName, tokenList.size(), extensionsList.size());
```

## 测试验证

### 1. 单元测试

创建了 `SLSLinkGeneratorUnifiedConfigTest` 测试类，包含以下测试用例：

#### 测试事件代码检查
```java
@Test
public void testIsEventCodeConfiguredInUnifiedConfig_Found() {
    // 测试存在的事件代码
    boolean result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("ApiVerify");
    assertTrue("应该找到 ApiVerify 事件代码", result);
}
```

#### 测试配置文件名正确性
```java
@Test
public void testConfigFileNameCorrectness() {
    // 验证是否使用了正确的配置文件名
    mockedNacos.verify(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
            anyString(), 
            "strategy-monitor-unified-config", // 验证使用了正确的配置文件名
            anyString()));
}
```

#### 测试链接生成
```java
@Test
public void testGetShareableLinkByStrategyAndMonitor() {
    // 测试根据策略类型和监控ID生成链接
    String result = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("apiverify", "dashboard-1747904265305-468192");
    assertEquals("应该返回预期的链接", expectedLink, result);
}
```

### 2. 集成测试

#### API 接口测试
```bash
# 测试统一配置的动态链接生成
GET /admin/strategy/subscription/test-unified-link?strategyType=apiverify&monitorId=dashboard-1747904265305-468192

# 获取监控配置详情
GET /admin/strategy/subscription/monitor-config?strategyType=apiverify&monitorId=dashboard-1747904265305-468192
```

## 配置文件对应关系

### 1. Nacos 配置文件名映射

| 组件 | 旧配置文件名 | 新配置文件名 |
|------|-------------|-------------|
| SLSLinkGenerator | strategy-monitor-mapping | strategy-monitor-unified-config |
| StrategyMonitorConfigService | strategy-monitor-mapping | strategy-monitor-unified-config |

### 2. 配置结构对应

#### 统一配置结构
```json
{
  "strategyType": {
    "name": "策略名称",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "监控项ID",
        "name": "监控项名称",
        "eventCode": "事件代码",
        "useDynamicLink": true,
        "slsConfig": {
          "dashboardName": "仪表盘名称",
          "token": [{"key": "参数名", "value": "参数值"}],
          "extensions": [{"配置项": "配置值"}]
        }
      }
    ]
  }
}
```

## 向后兼容性

### 1. 保持方法签名不变

所有修复的方法保持原有的输入参数和返回值格式：

```java
// 方法签名保持不变
private static Map<String, Object> getMonitorConfigByStrategyAndId(String strategyType, String monitorId)
public static boolean isEventCodeConfiguredInUnifiedConfig(String eventCode)
private static Map<String, Object> getEventCodeMonitorConfigFromUnified(String eventCode)
```

### 2. 兼容旧配置查找

在 `getEventCodeMonitorConfig()` 方法中，仍然保持先查找旧配置，再查找新配置的逻辑：

```java
// 先尝试从旧配置中获取
String oldConfigString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "eventCode.monitor.config", "");
if (oldConfigString != null && !oldConfigString.isEmpty()) {
    // 处理旧配置...
}

// 从新的统一配置中获取
return getEventCodeMonitorConfigFromUnified(eventCode);
```

## 部署注意事项

### 1. 配置文件准备

在部署前确保 Nacos 中已经创建了 `strategy-monitor-unified-config` 配置文件，并包含正确的配置内容。

### 2. 配置验证

部署后通过以下方式验证配置是否正确：

```bash
# 1. 检查事件代码是否配置
curl "http://localhost:8080/admin/strategy/subscription/check-config?strategyType=apiverify"

# 2. 测试动态链接生成
curl "http://localhost:8080/admin/strategy/subscription/test-unified-link?strategyType=apiverify&monitorId=dashboard-1747904265305-468192"

# 3. 获取监控配置详情
curl "http://localhost:8080/admin/strategy/subscription/monitor-config?strategyType=apiverify&monitorId=dashboard-1747904265305-468192"
```

### 3. 日志监控

部署后关注以下日志：

- 配置获取成功日志：`成功从统一配置获取监控配置`
- 配置解析错误日志：`解析统一配置 strategy-monitor-unified-config 失败`
- 配置为空警告日志：`Nacos 统一配置 strategy-monitor-unified-config 为空`

## 总结

本次修复解决了以下问题：

1. **配置文件名不一致**：统一使用 `strategy-monitor-unified-config` 配置文件名
2. **错误处理不完善**：增加了详细的日志记录和参数验证
3. **配置解析不够安全**：增加了空值检查和异常处理
4. **测试覆盖不足**：创建了完整的单元测试用例

修复后的代码能够正确从统一配置文件中获取监控配置，支持多监控面板的配置，并提供了更好的错误处理和日志记录功能。
