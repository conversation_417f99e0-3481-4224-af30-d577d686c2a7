### **策略监控自动化订阅与推送系统 - 技术方案**
#### 1. 项目背景与目标

**1.1. 背景**
当前，策略研发人员在策略上线后，需手动查找并持续关注相关的监控大盘。为在策略上线初期提供关键的监控提醒，我们计划构建一个自动化的策略监控订阅与限次推送系统。

**1.2. 目标**
*   **无感订阅**: 策略上线后，系统能自动为策略负责人订阅关联的核心监控大盘，并通过企业微信消息主动触达。
*   **限次定时推送**: 订阅成功后，系统**每小时**推送一次监控链接，**总共推送3次**后自动停止，以确保策略上线初期的核心关注度，同时避免长期信息骚扰。

---

#### 2. 系统整体架构

**2.1. 架构图**

```mermaid
graph TD
    A[策略管理平台] -- 1. 策略上线成功 --> B{本系统 API};
    B -- 2. 写入订阅记录(含推送次数) --> C[(数据库 t_subscription_record)];
    B -- 3. 发送订阅成功通知 --> F[企业微信];
    D[配置中心 Nacos] -- 读取策略-监控映射 --> B;
    
    subgraph "定时推送核心逻辑"
        E[分布式调度中心 XXL-Job] -- 4. 每分钟触发 --> G{本系统定时任务};
        G -- 5. 查询到期且未完成的订阅 --> C;
        D -- 读取监控详情 --> G;
        G -- 6. 发送定时推送 --> F;
        G -- 7. 更新通知时间 & 递减剩余次数 --> C;
    end
```

**2.2. 核心组件说明**
*   **本系统 API**: 提供唯一的外部接口，用于接收策略平台的上线通知并创建订阅。
*   **数据库 (MySQL)**: 持久化存储用户与监控项的订阅关系及状态。
*   **配置中心 (Nacos)**: 动态管理核心业务关系，即“策略类型”到其关联“监控大盘列表”的映射。
*   **分布式调度中心 (XXL-Job)**: 负责高可靠、周期性地触发监控推送任务。
*   **企业微信**:作为消息的最终触达渠道。

---

#### 3. 核心流程设计

**3.1. 流程一：策略上线与自动订阅**

**a. 触发**:
策略管理平台在策略成功上线后，调用本系统的工具类，请求企业微信应用接口：
请求方式：POST（HTTPS）
请求地址： https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=ACCESS_TOKEN

ACCESS_TOKEN使用com.youxin.risk.admin.tools.wechat.QwClient获取

**b. 请求体示例**:
```json
{
  "userId": "zhangsan",
  "strategyType": "strategyTypeA",
  "eventCode": "apiverify"
}
```

**c. 后端处理逻辑**:
1.  **参数校验**: 校验请求体。
2.  **获取配置**: 从Nacos获取该策略的监控项列表。
3.  **数据持久化**:
    *   遍历监控项列表，为每一个监控项在 `t_subscription_record` 表中插入一条新记录。
    *   **关键字段设置**:
        *   `frequency_minutes`: 固定设置为 `60` (每小时一次)。
        *   `total_pushes`: 固定设置为 `3` (总共要推送的次数)。
        *   `sent_pushes`: 初始值为 `0` (已推送次数)。
        *   `is_active`: `1` (激活状态)。
        *   `last_notified_at`: `NULL`，确保首次推送在任务首次扫描时立即触发。
4.  **发送通知**: 构造并发送“订阅成功”的Markdown消息。

**d. Markdown 消息设计 (订阅成功通知)**:
```json
{
    "touser": "zhangsan",
    "msgtype": "markdown",
    "agentid": 1000146,
    "markdown": {
        "content": "**【订阅成功】新策略已自动订阅**\n\n您上线的 “A类推荐策略” 已为您自动订阅核心监控。\n\n> **后续安排**\n> 系统将从现在开始，**每20分钟**为您推送一次监控大盘链接。\n\n[立即查看核心指标大盘](http://sls/d/xxx/core)"
    }
}
```

**3.2. 流程二：周期性监控推送**

**a. 触发**:
XXL-Job 中配置一个 Cron 任务，频率为每分钟执行一次 (`0 * * * * ?`)，触发本系统的推送逻辑。

**b. 后端处理逻辑**:
1.  **查询到期订阅**: 执行SQL查询，找出所有需要推送的订阅记录。
    *   **查询条件**:
        1.  订阅是激活的 (`is_active = 1`)。
        2.  已推送次数小于总次数 (`sent_pushes < total_pushes`)。
        3.  距离上次推送已满一个周期 (`last_notified_at IS NULL` OR `last_notified_at <= NOW() - INTERVAL frequency_minutes MINUTE`)。
    ```sql
    SELECT * FROM t_subscription_record 
    WHERE is_active = 1 
    AND sent_pushes < total_pushes
    AND (last_notified_at IS NULL OR last_notified_at <= NOW() - INTERVAL frequency_minutes MINUTE);
    ```
2.  **数据聚合**: 按 `user_id` 分组。
3.  **消息构建与发送**:
    *   遍历每个 `user_id`，聚合其名下所有到期的监控项，构造Markdown消息。
    *   调用企业微信API发送消息。
4.  **更新状态 (关键)**: 消息发送成功后，**批量更新**这些被推送过的记录：
    *   `last_notified_at` 更新为当前时间。
    *   `sent_pushes` 字段**加一** (`sent_pushes = sent_pushes + 1`)。
    ```sql
    UPDATE t_subscription_record
    SET 
        last_notified_at = CURRENT_TIMESTAMP,
        sent_pushes = sent_pushes + 1
    WHERE id IN (...); -- 传入本次已处理的记录ID列表
    ```
    当一条记录的 `sent_pushes` 等于 `total_pushes` 后，它将自然不再被查询逻辑选中，从而实现自动停止。

**c. Markdown 消息设计 (周期性推送)**:
```json
{
    "touser": "zhangsan",
    "msgtype": "markdown",
    "agentid": 1000146,
    "markdown": {
        "content": "**【定时监控推送】**\n\n**A类推荐策略** (每20分钟)\n[• 核心指标大盘](http://sls/d/xxx/core)\n[• 性能监控](http://sls/d/xxx/perf)\n\n**C类活动策略** (每20分钟)\n[• 活动效果实时监控](http://sls/d/xxx/activity)"
    }
}
```
*   **体验优化**: 可以在标题中加入推送计数，如 `(第 {sent_pushes + 1}/{total_pushes} 次)`。
---

#### 4. 核心配置与数据模型

**4.1. Nacos 配置**
*   **Data ID**: `strategy-monitor-mapping.json`
*   **Group**: `STRATEGY_CENTER`
*   **配置内容示例**:
```json
{
  "strategyTypeA": {
    "name": "A类推荐策略",
    "monitors": [
      { "id": "sls-001", "name": "核心指标大盘", "url": "http://sls/d/xxx/core" },
      { "id": "sls-002", "name": "性能监控", "url": "http://sls/d/xxx/perf" }
    ]
  },
  "strategyTypeC": {
    "name": "C类活动策略",
    "monitors": [
      { "id": "sls-003", "name": "活动效果实时监控", "url": "http://sls/d/xxx/activity" }
    ]
  }
}
```

**4.2. 数据库表设计 (`t_subscription_record`)**
```sql
CREATE TABLE `t_subscription_record` (
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `user_id` VARCHAR(64) NOT NULL COMMENT '企业微信userid',
  `strategy_type` VARCHAR(128) NOT NULL COMMENT '策略类型标识',
  `monitor_id` VARCHAR(128) NOT NULL COMMENT '监控项唯一ID',
  `frequency_minutes` INT(11) NOT NULL DEFAULT 60 COMMENT '推送频率(分钟)。固定为60分钟。',
  `total_pushes` TINYINT(4) NOT NULL DEFAULT 3 COMMENT '总共需要推送的次数',
  `sent_pushes` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '已经推送的次数',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活 (1:是, 0:否)',
  `last_notified_at` DATETIME NULL DEFAULT NULL COMMENT '上次通知时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_strategy_monitor` (`user_id`, `strategy_type`, `monitor_id`),
  KEY `idx_push_task` (`is_active`, `sent_pushes`, `total_pushes`, `last_notified_at`) COMMENT '定时推送任务查询优化索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅信息记录表';
```
*   **表结构变更**:
    *   新增 `total_pushes` 字段，用于存储总推送目标次数。
    *   新增 `sent_pushes` 字段，用于计数已完成的推送。
*   **索引优化**: 更新了索引 `idx_push_task` 以包含新的计数列，确保查询性能。

---
*   **索引优化**: 创建了联合索引 `idx_user_active_time` 来加速定时任务的查询性能。