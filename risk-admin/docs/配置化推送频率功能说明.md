# 配置化推送频率功能说明

## 概述

本文档描述了策略监控订阅系统中配置化推送频率功能的实现。该功能将原本硬编码的"每60分钟推送3次"改为可配置的参数，支持策略级别和监控项级别的个性化配置，实现更灵活的推送策略。

## 功能特性

### 1. 多层级配置支持
- **系统默认配置**: 60分钟频率，3次推送
- **策略级别配置**: 可为每个策略类型设置独特的推送参数
- **监控项级别配置**: 可为单个监控项设置特殊的推送参数
- **配置优先级**: 监控项 > 策略 > 系统默认

### 2. 灵活的推送策略
- **推送频率**: 支持1分钟到24小时的任意频率设置
- **推送次数**: 支持1次到100次的推送次数配置
- **混合配置**: 同一策略下不同监控项可使用不同推送参数

### 3. 智能配置管理
- **配置验证**: 自动验证配置参数的合理性
- **向后兼容**: 未配置的项目使用默认值
- **配置查询**: 提供API查询当前生效的配置

## 技术实现

### 1. 数据模型增强

#### StrategyMonitorMapping 策略级别配置
```java
public class StrategyMonitorMapping {
    private String name;                    // 策略名称
    private Integer frequencyMinutes;       // 策略级推送频率
    private Integer totalPushes;           // 策略级总推送次数
    private List<MonitorConfig> monitors;   // 监控项列表
    
    // 获取有效配置（带默认值回退）
    public int getEffectiveFrequencyMinutes(int defaultFrequency);
    public int getEffectiveTotalPushes(int defaultTotalPushes);
}
```

#### MonitorConfig 监控项级别配置
```java
public class MonitorConfig {
    private String id;                      // 监控项ID
    private String name;                    // 监控项名称
    private String eventCode;               // 事件代码
    private Boolean useDynamicLink;         // 是否使用动态链接
    private Integer frequencyMinutes;       // 监控项级推送频率
    private Integer totalPushes;           // 监控项级总推送次数
    
    // 获取有效配置（带默认值回退）
    public int getEffectiveFrequencyMinutes(int defaultFrequency);
    public int getEffectiveTotalPushes(int defaultTotalPushes);
}
```

### 2. 配置优先级逻辑

#### 配置解析流程
```java
// 1. 系统默认配置
int defaultFrequency = 60;      // 60分钟
int defaultTotalPushes = 3;     // 3次

// 2. 策略级别配置（覆盖默认值）
int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
int strategyTotalPushes = mapping.getEffectiveTotalPushes(defaultTotalPushes);

// 3. 监控项级别配置（覆盖策略配置）
int finalFrequency = monitor.getEffectiveFrequencyMinutes(strategyFrequency);
int finalTotalPushes = monitor.getEffectiveTotalPushes(strategyTotalPushes);
```

### 3. 服务层实现

#### StrategySubscriptionService 订阅创建
```java
public boolean createStrategySubscription(String userId, String strategyType) {
    // 获取策略配置
    StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
    
    for (MonitorConfig monitor : mapping.getMonitors()) {
        // 计算最终生效的配置
        int effectiveFrequency = monitor.getEffectiveFrequencyMinutes(
                mapping.getEffectiveFrequencyMinutes(defaultFrequency));
        int effectiveTotalPushes = monitor.getEffectiveTotalPushes(
                mapping.getEffectiveTotalPushes(defaultTotalPushes));
        
        // 创建订阅记录
        SubscriptionRecord record = new SubscriptionRecord();
        record.setFrequencyMinutes(effectiveFrequency);
        record.setTotalPushes(effectiveTotalPushes);
        // ... 其他设置
    }
}
```

## 配置示例

### 1. Nacos 配置文件

#### strategy-monitor-mapping-enhanced.json
```json
{
  "strategyTypeA": {
    "name": "A类推荐策略",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "核心指标大盘",
        "eventCode": "ApiVerify",
        "useDynamicLink": true,
        "frequencyMinutes": 30,
        "totalPushes": 5
      },
      {
        "id": "sls-002",
        "name": "性能监控",
        "url": "http://sls.example.com/d/xxx/perf",
        "useDynamicLink": false
      }
    ]
  },
  "strategyTypeB": {
    "name": "B类风控策略",
    "frequencyMinutes": 120,
    "totalPushes": 2,
    "monitors": [
      {
        "id": "sls-003",
        "name": "风控效果监控",
        "eventCode": "RiskMonitor",
        "useDynamicLink": true,
        "frequencyMinutes": 60,
        "totalPushes": 4
      }
    ]
  }
}
```

### 2. 配置说明

| 配置项 | 级别 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|------|
| frequencyMinutes | 策略/监控项 | Integer | 否 | 推送频率（分钟），1-1440 | 60 |
| totalPushes | 策略/监控项 | Integer | 否 | 总推送次数，1-100 | 3 |

### 3. 配置优先级示例

以上面的配置为例：

**strategyTypeA 下的监控项配置生效情况：**
- `dashboard-1747904265305-468192`: 30分钟/5次（使用监控项级配置）
- `sls-002`: 60分钟/3次（使用策略级配置）

**strategyTypeB 下的监控项配置生效情况：**
- `sls-003`: 60分钟/4次（频率使用监控项配置，次数使用监控项配置）

## API 接口

### 1. 查询策略推送配置

#### 接口信息
```
GET /admin/strategy/subscription/push-config?strategyType={strategyType}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "strategyName": "A类推荐策略",
    "strategyFrequencyMinutes": 60,
    "strategyTotalPushes": 3,
    "strategyConfigSummary": "每1小时推送，共3次",
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "核心指标大盘",
        "eventCode": "ApiVerify",
        "useDynamicLink": true,
        "frequencyMinutes": 30,
        "totalPushes": 5,
        "configSummary": "每30分钟推送，共5次",
        "isCustomConfig": true
      },
      {
        "id": "sls-002",
        "name": "性能监控",
        "frequencyMinutes": 60,
        "totalPushes": 3,
        "configSummary": "每1小时推送，共3次",
        "isCustomConfig": false
      }
    ]
  }
}
```

### 2. 获取所有策略推送配置概览

#### 接口信息
```
GET /admin/strategy/subscription/push-config/overview
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "strategyType": "strategyTypeA",
      "strategyName": "A类推荐策略",
      "frequencyMinutes": 60,
      "totalPushes": 3,
      "configSummary": "每1小时推送，共3次",
      "monitorCount": 2,
      "customConfigMonitorCount": 1
    },
    {
      "strategyType": "strategyTypeB",
      "strategyName": "B类风控策略",
      "frequencyMinutes": 120,
      "totalPushes": 2,
      "configSummary": "每2小时推送，共2次",
      "monitorCount": 1,
      "customConfigMonitorCount": 1
    }
  ]
}
```

## 企业微信消息增强

### 1. 订阅成功通知

#### 显示推送频率信息
```markdown
**【订阅成功】新策略已自动订阅**

您上线的 "A类推荐策略" 已为您自动订阅核心监控。

> **后续安排**
> 系统将从现在开始，为您推送监控大盘链接。
> 推送频率：**每60分钟**

[核心指标大盘 (每30分钟)](动态链接)
[性能监控](静态链接)

> **链接刷新**
> 如果监控链接失效，请点击 [刷新监控链接](刷新链接) 获取最新链接。
```

### 2. 定时推送消息

#### 显示个性化频率
```markdown
**【定时监控推送】**

**A类推荐策略** (每60分钟)
[• 核心指标大盘 (第1/5次)](动态链接) - 每30分钟
[• 性能监控 (第1/3次)](静态链接)

> **链接刷新**
> 如果监控链接失效，请点击 [刷新监控链接](刷新链接) 获取最新链接。
```

## 工具类增强

### 1. SubscriptionUtils 新增方法

```java
// 验证推送频率配置
public static boolean validateFrequencyMinutes(Integer frequencyMinutes);

// 验证总推送次数配置
public static boolean validateTotalPushes(Integer totalPushes);

// 获取推送频率的显示文本
public static String getFrequencyDisplayText(int frequencyMinutes);

// 构建推送配置摘要
public static String buildPushConfigSummary(int frequencyMinutes, int totalPushes);
```

### 2. 显示文本格式化

```java
// 示例输出
getFrequencyDisplayText(30)   // "30分钟"
getFrequencyDisplayText(60)   // "1小时"
getFrequencyDisplayText(120)  // "2小时"
getFrequencyDisplayText(90)   // "1小时30分钟"

buildPushConfigSummary(60, 3) // "每1小时推送，共3次"
```

## 配置验证

### 1. 参数验证规则

```java
// 推送频率验证
- 范围：1-1440分钟（1分钟到24小时）
- 建议值：15, 30, 60, 120, 180, 360, 720, 1440

// 推送次数验证
- 范围：1-100次
- 建议值：1, 2, 3, 5, 10
```

### 2. 配置合理性检查

```java
// 自动检查配置合理性
if (frequencyMinutes < 5) {
    logger.warn("推送频率过于频繁，建议不少于5分钟");
}

if (totalPushes > 10) {
    logger.warn("推送次数过多，建议不超过10次");
}
```

## 使用场景

### 1. 高频监控场景
```json
{
  "name": "实时交易监控",
  "frequencyMinutes": 15,
  "totalPushes": 8
}
```

### 2. 低频监控场景
```json
{
  "name": "日报监控",
  "frequencyMinutes": 1440,
  "totalPushes": 1
}
```

### 3. 混合监控场景
```json
{
  "name": "综合业务监控",
  "frequencyMinutes": 60,
  "totalPushes": 3,
  "monitors": [
    {
      "name": "核心指标",
      "frequencyMinutes": 30,
      "totalPushes": 6
    },
    {
      "name": "辅助指标"
      // 使用策略级配置：60分钟/3次
    }
  ]
}
```

## 最佳实践

### 1. 配置建议
- **高优先级监控**: 15-30分钟频率，5-8次推送
- **常规监控**: 60分钟频率，3次推送
- **低优先级监控**: 120-180分钟频率，2次推送

### 2. 配置原则
- 避免过于频繁的推送，防止用户疲劳
- 重要监控项可设置更高频率
- 考虑用户工作时间，避免非工作时间过度推送

### 3. 监控建议
- 定期检查推送配置的合理性
- 根据用户反馈调整推送策略
- 监控推送成功率和用户点击率

## 注意事项

1. **配置更新**: Nacos配置更新后需要重启应用或实现配置热更新
2. **数据一致性**: 确保配置更新时已有订阅记录的处理策略
3. **性能考虑**: 高频推送可能增加系统负载，需要合理配置
4. **用户体验**: 避免推送频率过高导致用户反感
5. **兼容性**: 确保新配置与现有功能的兼容性
