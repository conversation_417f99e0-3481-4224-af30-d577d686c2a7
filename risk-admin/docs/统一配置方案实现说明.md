# 统一配置方案实现说明

## 概述

本文档描述了将 `strategy-monitor-mapping.json` 和 `eventCode.monitor.config` 两个配置文件合并为一个统一配置的实现方案。新的统一配置以策略类型为key，包含了策略监控映射、推送配置和SLS配置等所有相关信息。

## 配置合并方案

### 1. 原有配置结构

#### strategy-monitor-mapping.json
```json
{
  "strategyTypeA": {
    "name": "A类推荐策略",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "核心指标大盘",
        "eventCode": "ApiVerify",
        "useDynamicLink": true
      }
    ]
  }
}
```

#### eventCode.monitor.config
```json
{
  "ApiVerify": {
    "dashboardName": "dashboard-1747904265305-468192",
    "token": [{"key": "date", "value": "60"}],
    "extensions": [{"autoFresh": "30s"}]
  }
}
```

### 2. 新的统一配置结构

#### strategy-monitor-unified-config.json
```json
{
  "strategyTypeA": {
    "name": "A类推荐策略",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "核心指标大盘",
        "eventCode": "ApiVerify",
        "useDynamicLink": true,
        "frequencyMinutes": 30,
        "totalPushes": 5,
        "slsConfig": {
          "dashboardName": "dashboard-1747904265305-468192",
          "token": [{"key": "date", "value": "60"}],
          "extensions": [{"autoFresh": "30s"}]
        }
      }
    ]
  }
}
```

## 技术实现

### 1. 数据模型增强

#### SLSConfig 新增模型
```java
public class SLSConfig {
    private String dashboardName;                    // 仪表盘名称
    private List<Map<String, String>> token;        // Token 参数列表
    private List<Map<String, String>> extensions;   // 扩展配置参数列表
}
```

#### MonitorConfig 模型更新
```java
public class MonitorConfig {
    // 原有字段...
    private SLSConfig slsConfig;  // 新增 SLS 配置
}
```

### 2. SLSLinkGenerator 增强

#### 新增方法
```java
// 根据配置参数生成链接
public static String getShareableLinkByConfig(String dashboardName, 
                                             List<Map<String, String>> token, 
                                             List<Map<String, String>> extensions);

// 根据策略类型和监控ID生成链接
public static String getShareableLinkByStrategyAndMonitor(String strategyType, String monitorId);

// 在统一配置中检查事件代码
public static boolean isEventCodeConfiguredInUnifiedConfig(String eventCode);
```

#### 配置获取逻辑
```java
// 从统一配置中获取监控配置
private static Map<String, Object> getMonitorConfigByStrategyAndId(String strategyType, String monitorId);

// 从统一配置中根据事件代码获取配置
private static Map<String, Object> getEventCodeMonitorConfigFromUnified(String eventCode);
```

### 3. 向后兼容性

#### 兼容策略
1. **配置查找顺序**: 先查找旧配置，再查找新配置
2. **方法保持**: 原有的 `getShareableLinkByEventCode()` 方法保持不变
3. **渐进迁移**: 支持新旧配置并存，逐步迁移

#### 兼容实现
```java
public static boolean isEventCodeConfigured(String eventCode) {
    // 先尝试从旧配置中查找
    String oldConfigString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "eventCode.monitor.config", "");
    if (oldConfigString != null && !oldConfigString.isEmpty()) {
        // 检查旧配置...
    }
    
    // 从新的统一配置中查找
    return isEventCodeConfiguredInUnifiedConfig(eventCode);
}
```

## 配置优势

### 1. 统一管理
- **单一配置源**: 所有策略相关配置集中在一个文件中
- **关联性强**: 策略、监控项、SLS配置紧密关联
- **维护简单**: 减少配置文件数量，降低维护复杂度

### 2. 扩展性强
- **多监控面板**: 一个策略类型下可配置多个监控面板
- **个性化配置**: 每个监控项可有独立的推送和SLS配置
- **灵活组合**: 支持动态链接和静态链接的混合使用

### 3. 配置层次清晰
```
策略类型 (strategyTypeA)
├── 策略基本信息 (name, frequencyMinutes, totalPushes)
└── 监控项列表 (monitors)
    ├── 监控项1
    │   ├── 基本信息 (id, name, eventCode)
    │   ├── 推送配置 (frequencyMinutes, totalPushes)
    │   └── SLS配置 (slsConfig)
    └── 监控项2
        └── ...
```

## 使用示例

### 1. 配置示例

#### 多监控面板策略
```json
{
  "apiverify": {
    "name": "API验证策略",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "API调用监控",
        "eventCode": "ApiVerify",
        "useDynamicLink": true,
        "frequencyMinutes": 20,
        "totalPushes": 5,
        "slsConfig": {
          "dashboardName": "dashboard-1747904265305-468192",
          "token": [{"key": "date", "value": "60"}],
          "extensions": [{"autoFresh": "30s"}]
        }
      },
      {
        "id": "sls-007",
        "name": "验证成功率监控",
        "url": "http://sls.example.com/d/xxx/verify",
        "useDynamicLink": false
      },
      {
        "id": "dashboard-1747904265305-468195",
        "name": "API性能监控",
        "eventCode": "ApiPerformance",
        "useDynamicLink": true,
        "slsConfig": {
          "dashboardName": "dashboard-1747904265305-468195",
          "token": [
            {"key": "date", "value": "120"},
            {"key": "service", "value": "api-gateway"}
          ],
          "extensions": [{"autoFresh": "60s"}]
        }
      }
    ]
  }
}
```

### 2. API 调用示例

#### 生成动态链接
```java
// 方式1：使用策略类型和监控ID
String link = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("apiverify", "dashboard-1747904265305-468192");

// 方式2：使用事件代码（兼容旧方式）
String link = SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify");

// 方式3：直接使用配置参数
String link = SLSLinkGenerator.getShareableLinkByConfig(dashboardName, token, extensions);
```

#### 获取监控配置
```java
// 获取特定监控项配置
MonitorConfig monitor = strategyMonitorConfigService.getMonitorConfig("apiverify", "dashboard-1747904265305-468192");

// 获取策略下所有监控项
StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping("apiverify");
```

### 3. 测试接口

#### 测试统一配置链接生成
```
GET /admin/strategy/subscription/test-unified-link?strategyType=apiverify&monitorId=dashboard-1747904265305-468192
```

#### 获取监控配置详情
```
GET /admin/strategy/subscription/monitor-config?strategyType=apiverify&monitorId=dashboard-1747904265305-468192
```

## 迁移指南

### 1. 配置迁移步骤

#### 步骤1：创建统一配置
1. 创建新的 `strategy-monitor-mapping` 配置
2. 将原有的两个配置文件内容合并
3. 按照新的结构组织配置数据

#### 步骤2：部署新代码
1. 部署包含统一配置支持的新代码
2. 验证新旧配置都能正常工作
3. 测试动态链接生成功能

#### 步骤3：切换配置
1. 逐步将配置迁移到统一配置
2. 验证功能正常性
3. 删除旧配置文件

### 2. 配置转换工具

#### 自动转换脚本示例
```javascript
// 将旧配置转换为新配置的示例脚本
function convertToUnifiedConfig(strategyMapping, eventCodeConfig) {
    const unifiedConfig = {};
    
    for (const [strategyType, strategy] of Object.entries(strategyMapping)) {
        unifiedConfig[strategyType] = {
            ...strategy,
            monitors: strategy.monitors.map(monitor => {
                const newMonitor = { ...monitor };
                
                if (monitor.eventCode && eventCodeConfig[monitor.eventCode]) {
                    newMonitor.slsConfig = eventCodeConfig[monitor.eventCode];
                }
                
                return newMonitor;
            })
        };
    }
    
    return unifiedConfig;
}
```

## 最佳实践

### 1. 配置组织
- **策略分组**: 按业务域或功能模块组织策略类型
- **命名规范**: 使用清晰的策略类型和监控项命名
- **配置验证**: 定期验证配置的正确性和完整性

### 2. 监控项设计
- **职责单一**: 每个监控项专注于特定的监控维度
- **配置独立**: 避免监控项之间的配置依赖
- **扩展友好**: 预留扩展字段，便于后续功能增强

### 3. 性能优化
- **配置缓存**: 在应用层缓存解析后的配置对象
- **懒加载**: 按需加载特定策略的配置
- **批量操作**: 支持批量获取多个监控项的配置

## 注意事项

1. **配置格式**: 确保JSON格式正确，避免解析错误
2. **字段完整性**: 必填字段不能为空，可选字段要有默认值
3. **向后兼容**: 在完全迁移前保持对旧配置的支持
4. **配置热更新**: 考虑实现配置的热更新机制
5. **错误处理**: 完善的配置解析错误处理和日志记录

## 总结

统一配置方案通过将多个配置文件合并为一个，实现了：

- **配置集中化**: 所有策略相关配置统一管理
- **关联性增强**: 策略、监控项、SLS配置紧密关联
- **扩展性提升**: 支持一个策略下多个监控面板
- **维护简化**: 减少配置文件数量，降低维护成本
- **向后兼容**: 保持对现有系统的兼容性

这个方案为策略监控订阅系统提供了更加灵活和强大的配置能力，同时保持了良好的可维护性和扩展性。
