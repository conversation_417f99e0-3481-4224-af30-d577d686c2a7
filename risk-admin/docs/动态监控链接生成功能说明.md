# 动态监控链接生成功能说明

## 概述

本文档描述了在策略监控订阅系统中集成的动态监控链接生成功能。该功能通过集成 `SLSLinkGenerator` 工具类，实现了基于 eventCode 的动态监控链接生成，解决了 SLS 分享链接在多端查看时失效的问题。

## 功能特性

### 1. 动态链接生成
- 使用 `SLSLinkGenerator.getShareableLinkByEventCode(eventCode)` 方法生成最新的监控链接
- 支持基于 eventCode 的实时链接生成，确保链接的有效性
- 自动处理 SLS 票据（ticket）的创建和管理

### 2. 向后兼容性
- 保持对现有静态 URL 配置的支持
- 当动态链接生成失败时，自动回退到静态 URL
- 支持混合配置模式（部分监控项使用动态链接，部分使用静态链接）

### 3. 链接刷新功能
- 提供手动刷新监控链接的 API 接口
- 在企业微信消息中添加刷新链接按钮
- 支持单个用户或特定策略类型的链接刷新

## 技术实现

### 1. 数据模型增强

#### MonitorConfig 模型更新
```java
public class MonitorConfig {
    private String id;           // 监控项ID (对应 dashboardName)
    private String name;         // 监控项名称
    private String url;          // 静态URL (向后兼容)
    private String eventCode;    // 事件代码 (用于动态生成)
    private Boolean useDynamicLink; // 是否使用动态链接
    
    // 判断是否启用动态链接
    public boolean isDynamicLinkEnabled() {
        return useDynamicLink != null && useDynamicLink && 
               eventCode != null && !eventCode.trim().isEmpty();
    }
}
```

### 2. 服务层增强

#### StrategyMonitorConfigService 新增方法
```java
// 生成监控链接（动态或静态）
public String generateMonitorLink(MonitorConfig monitor)

// 批量生成监控链接
public Map<String, String> batchGenerateMonitorLinks(List<MonitorConfig> monitors)

// 刷新监控链接
public String refreshMonitorLink(String eventCode)
```

#### WechatMessageService 增强
```java
// 发送刷新后的监控链接
public boolean sendRefreshedMonitorLinks(String userId, Map<String, String> refreshedLinks)
```

### 3. API 接口

#### 新增刷新链接接口
```java
// 刷新用户监控链接
POST /admin/strategy/subscription/refresh-links
参数: userId, strategyType (可选)

// 根据事件代码刷新链接
POST /admin/strategy/subscription/refresh-link-by-event
参数: eventCode
```

## 配置说明

### 1. Nacos 配置更新

#### strategy-monitor-mapping.json 配置示例
```json
{
  "strategyTypeA": {
    "name": "A类推荐策略",
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "核心指标大盘",
        "eventCode": "ApiVerify",
        "useDynamicLink": true
      },
      {
        "id": "sls-002",
        "name": "性能监控",
        "url": "http://sls.example.com/d/xxx/perf",
        "useDynamicLink": false
      }
    ]
  }
}
```

#### eventCode.monitor.config 配置示例
```json
{
  "ApiVerify": {
    "dashboardName": "dashboard-1747904265305-468192",
    "token": [
      {
        "key": "date",
        "value": "60"
      }
    ],
    "extensions": [
      {
        "autoFresh": "30s"
      }
    ]
  }
}
```

### 2. 配置字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | String | 是 | 监控项唯一ID，对应 dashboardName |
| name | String | 是 | 监控项显示名称 |
| eventCode | String | 条件 | 事件代码，useDynamicLink=true 时必填 |
| url | String | 条件 | 静态URL，useDynamicLink=false 时必填 |
| useDynamicLink | Boolean | 是 | 是否使用动态链接生成 |

## 使用流程

### 1. 策略上线自动订阅
1. 策略上线触发 `AlertNotificationHandler.sendStrategyOnlineToAlert()`
2. 调用 `StrategySubscriptionService.createStrategySubscription()`
3. 根据配置创建订阅记录
4. 发送订阅成功通知，包含动态生成的监控链接

### 2. 定时推送
1. `StrategyMonitorPushJob` 定时任务执行
2. 查询需要推送的订阅记录
3. 调用 `StrategyMonitorConfigService.generateMonitorLink()` 生成最新链接
4. 发送包含最新链接的企业微信消息

### 3. 手动刷新链接
1. 用户点击消息中的"刷新监控链接"按钮
2. 调用 `/admin/strategy/subscription/refresh-links` API
3. 重新生成所有相关监控链接
4. 发送包含最新链接的通知消息

## 链接生成逻辑

### 1. 动态链接生成流程
```java
public String generateMonitorLink(MonitorConfig monitor) {
    if (monitor.isDynamicLinkEnabled()) {
        // 尝试生成动态链接
        String dynamicLink = SLSLinkGenerator.getShareableLinkByEventCode(monitor.getEventCode());
        if (StringUtils.isNotEmpty(dynamicLink)) {
            return dynamicLink;
        }
        // 动态生成失败，回退到静态URL
    }
    
    // 使用静态URL
    return monitor.getUrl();
}
```

### 2. 错误处理机制
- **动态生成失败**: 自动回退到静态 URL
- **配置错误**: 记录警告日志，返回 null
- **网络异常**: 捕获异常，记录错误日志

## 企业微信消息增强

### 1. 订阅成功通知
```markdown
**【订阅成功】新策略已自动订阅**

您上线的 "A类推荐策略" 已为您自动订阅核心监控。

> **后续安排**
> 系统将从现在开始，**每60分钟**为您推送一次监控大盘链接。

[核心指标大盘](动态生成的链接)
[性能监控](静态链接)

> **链接刷新**
> 如果监控链接失效，请点击 [刷新监控链接](刷新API链接) 获取最新链接。
```

### 2. 定时推送消息
```markdown
**【定时监控推送】**

**A类推荐策略** (每60分钟)
[• 核心指标大盘 (第1/3次)](动态生成的链接)
[• 性能监控 (第1/3次)](静态链接)

> **链接刷新**
> 如果监控链接失效，请点击 [刷新监控链接](刷新API链接) 获取最新链接。
```

### 3. 链接刷新通知
```markdown
**【监控链接已刷新】**

您的监控链接已更新为最新版本：

[• 核心指标大盘](新生成的动态链接)
[• 性能监控](静态链接)

> **提示**: 新链接在多端查看时具有更好的稳定性。
```

## 测试说明

### 1. 单元测试
- `StrategyMonitorConfigServiceTest`: 测试动态链接生成逻辑
- `StrategySubscriptionServiceTest`: 测试订阅服务集成
- 覆盖正常流程、异常处理、回退机制等场景

### 2. 集成测试步骤
1. 配置 Nacos 中的策略监控映射和事件代码配置
2. 触发策略上线，验证自动订阅和动态链接生成
3. 等待定时任务执行，验证推送消息中的链接有效性
4. 测试手动刷新链接功能
5. 验证链接失效时的回退机制

## 监控和运维

### 1. 关键指标
- 动态链接生成成功率
- 链接刷新请求频率
- SLS API 调用延迟和成功率
- 用户链接点击率

### 2. 日志监控
- 动态链接生成失败的错误日志
- SLS API 调用异常
- 配置解析错误

### 3. 告警设置
- 动态链接生成成功率低于 95%
- SLS API 调用失败率超过 5%
- 链接刷新请求异常增长

## 注意事项

1. **SLS 配置**: 确保 eventCode.monitor.config 中的配置与策略监控映射一致
2. **权限管理**: 确保 SLS API 访问权限正确配置
3. **性能考虑**: 动态链接生成涉及 SLS API 调用，注意控制并发和超时
4. **缓存策略**: 考虑对生成的链接进行短期缓存，减少 API 调用
5. **降级机制**: 当 SLS 服务不可用时，确保静态链接仍然可用

## 扩展性

### 1. 支持更多监控平台
- 抽象监控链接生成接口
- 支持 Grafana、Prometheus 等其他监控平台

### 2. 智能刷新策略
- 根据链接访问失败率自动触发刷新
- 支持预定义的刷新时间策略

### 3. 用户偏好设置
- 允许用户自定义推送频率
- 支持用户选择接收动态链接或静态链接
