{"strategyTypeA": {"name": "A类推荐策略", "frequencyMinutes": 60, "totalPushes": 3, "monitors": [{"id": "dashboard-1747904265305-468192", "name": "核心指标大盘", "eventCode": "ApiVerify", "useDynamicLink": true, "frequencyMinutes": 30, "totalPushes": 5}, {"id": "sls-002", "name": "性能监控", "url": "http://sls.example.com/d/xxx/perf", "useDynamicLink": false}]}, "strategyTypeB": {"name": "B类风控策略", "frequencyMinutes": 120, "totalPushes": 2, "monitors": [{"id": "sls-003", "name": "风控效果监控", "eventCode": "RiskMonitor", "useDynamicLink": true, "frequencyMinutes": 60, "totalPushes": 4}, {"id": "sls-004", "name": "规则命中率监控", "url": "http://sls.example.com/d/xxx/rules", "useDynamicLink": false}]}, "strategyTypeC": {"name": "C类活动策略", "frequencyMinutes": 30, "totalPushes": 6, "monitors": [{"id": "sls-005", "name": "活动效果实时监控", "eventCode": "ActivityMonitor", "useDynamicLink": true, "frequencyMinutes": 15, "totalPushes": 8}]}, "apiverify": {"name": "API验证策略", "frequencyMinutes": 60, "totalPushes": 3, "monitors": [{"id": "dashboard-1747904265305-468192", "name": "API调用监控", "eventCode": "ApiVerify", "useDynamicLink": true, "frequencyMinutes": 20, "totalPushes": 5}, {"id": "sls-007", "name": "验证成功率监控", "url": "http://sls.example.com/d/xxx/verify", "useDynamicLink": false}]}}