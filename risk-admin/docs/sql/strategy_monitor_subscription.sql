-- 策略监控订阅记录表
CREATE TABLE `t_subscription_record` (
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `user_id` VARCHAR(64) NOT NULL COMMENT '企业微信userid',
  `strategy_type` VARCHAR(128) NOT NULL COMMENT '策略类型标识',
  `monitor_id` VARCHAR(128) NOT NULL COMMENT '监控项唯一ID',
  `frequency_minutes` INT(11) NOT NULL DEFAULT 60 COMMENT '推送频率(分钟)。固定为60分钟。',
  `total_pushes` TINYINT(4) NOT NULL DEFAULT 3 COMMENT '总共需要推送的次数',
  `sent_pushes` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '已经推送的次数',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活 (1:是, 0:否)',
  `last_notified_at` DATETIME NULL DEFAULT NULL COMMENT '上次通知时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_strategy_monitor` (`user_id`, `strategy_type`, `monitor_id`),
  KEY `idx_push_task` (`is_active`, `sent_pushes`, `total_pushes`, `last_notified_at`) COMMENT '定时推送任务查询优化索引',
  KEY `idx_user_strategy` (`user_id`, `strategy_type`) COMMENT '用户策略查询索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='策略监控订阅记录表';

-- 插入示例数据（可选）
-- INSERT INTO `t_subscription_record` (`user_id`, `strategy_type`, `monitor_id`, `frequency_minutes`, `total_pushes`, `sent_pushes`, `is_active`, `last_notified_at`) VALUES
-- ('zhangsan', 'strategyTypeA', 'sls-001', 60, 3, 0, 1, NULL),
-- ('zhangsan', 'strategyTypeA', 'sls-002', 60, 3, 0, 1, NULL),
-- ('lisi', 'strategyTypeC', 'sls-003', 60, 3, 1, 1, '2024-01-01 10:00:00');
