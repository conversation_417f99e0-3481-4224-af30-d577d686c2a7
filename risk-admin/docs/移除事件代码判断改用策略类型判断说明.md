# 移除事件代码判断改用策略类型判断说明

## 概述

本文档描述了将 `SLSLinkGenerator.isEventCodeConfigured(eventCode)` 的调用改为使用策略类型判断的修改过程。这个改动使得系统更加统一，直接基于策略类型进行配置检查，而不是依赖事件代码。

## 修改背景

### 原有逻辑问题
1. **依赖事件代码**: 原来的逻辑依赖于事件代码来判断是否有监控配置
2. **配置分散**: 需要在多个地方维护事件代码与策略的映射关系
3. **不够直观**: 策略上线时应该直接检查策略类型的配置，而不是事件代码

### 新逻辑优势
1. **直接判断**: 直接基于策略类型判断是否有监控配置
2. **配置统一**: 所有配置都在统一的策略配置中
3. **逻辑清晰**: 策略上线 → 检查策略配置 → 创建订阅

## 修改内容

### 1. AlertNotificationHandler.java 修改

#### 修改前的代码
```java
if(StringUtils.isNotEmpty(eventCode)
        && SLSLinkGenerator.isEventCodeConfigured(eventCode)){
    // 创建策略监控自动订阅
    createStrategyMonitorSubscription(strategyType, userName);
}
```

#### 修改后的代码
```java
if(StringUtils.isNotEmpty(strategyType)
        && strategyMonitorConfigService.isStrategyTypeConfigured(strategyType)){
    // 创建策略监控自动订阅
    createStrategyMonitorSubscription(strategyType, userName);
}
```

#### 导入变更
```java
// 移除
import com.youxin.risk.commons.utils.SLSLinkGenerator;

// 新增
import com.youxin.risk.admin.service.subscription.StrategyMonitorConfigService;

// 新增依赖注入
@Autowired
private StrategyMonitorConfigService strategyMonitorConfigService;
```

### 2. SLSLinkGenerator.java 方法移除

#### 移除的方法
```java
/**
 * 判断指定的事件代码是否存在监控配置。
 *
 * @param eventCode 事件代码
 * @return 如果存在配置返回 true，否则返回 false
 */
public static boolean isEventCodeConfigured(String eventCode) {
    // 直接从统一配置中查找
    return isEventCodeConfiguredInUnifiedConfig(eventCode);
}
```

#### 保留的方法
- `isEventCodeConfiguredInUnifiedConfig()` - 仍然保留，供内部使用
- `getShareableLinkByEventCode()` - 保留，用于根据事件代码生成链接
- `getShareableLinkByStrategyAndMonitor()` - 保留，推荐使用的方法

### 3. 测试文件更新

#### SLSLinkGeneratorUnifiedConfigTest.java 修改
```java
// 修改前：测试旧配置回退逻辑
mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
        anyString(), anyString(), anyString()))
        .thenReturn("") // 第一次调用旧配置返回空
        .thenReturn(mockUnifiedConfig); // 第二次调用统一配置

// 修改后：直接测试统一配置
mockedNacos.when(() -> com.youxin.risk.commons.utils.service.NacosClient.getByNameSpace(
        anyString(), anyString(), anyString()))
        .thenReturn(mockUnifiedConfig);
```

## 逻辑对比

### 修改前的判断逻辑
```
策略上线 → 获取事件代码 → 检查事件代码是否配置 → 创建订阅
```

### 修改后的判断逻辑
```
策略上线 → 获取策略类型 → 检查策略类型是否配置 → 创建订阅
```

## 配置检查对比

### 原有的事件代码检查
```java
// 需要遍历所有策略类型，查找匹配的事件代码
public static boolean isEventCodeConfigured(String eventCode) {
    // 从统一配置中查找事件代码
    for (String strategyType : config.keySet()) {
        for (MonitorConfig monitor : monitors) {
            if (eventCode.equals(monitor.getEventCode())) {
                return true;
            }
        }
    }
    return false;
}
```

### 新的策略类型检查
```java
// 直接检查策略类型是否存在
public boolean isStrategyTypeConfigured(String strategyType) {
    StrategyMonitorMapping mapping = getStrategyMonitorMapping(strategyType);
    return mapping != null && mapping.getMonitors() != null && !mapping.getMonitors().isEmpty();
}
```

## 性能优化

### 查找效率提升
- **原有方式**: O(n*m) - 需要遍历所有策略类型和监控项
- **新方式**: O(1) - 直接通过策略类型key查找

### 代码简化
- **减少依赖**: AlertNotificationHandler 不再依赖 SLSLinkGenerator
- **逻辑清晰**: 直接使用策略配置服务进行判断
- **职责分离**: 各个组件职责更加明确

## 影响范围

### 直接影响
1. **AlertNotificationHandler**: 修改了策略上线时的配置检查逻辑
2. **SLSLinkGenerator**: 移除了 `isEventCodeConfigured` 方法
3. **测试文件**: 更新了相关的测试用例

### 间接影响
1. **配置管理**: 更加依赖策略类型的配置完整性
2. **错误处理**: 需要确保策略类型配置的正确性
3. **监控**: 可以更好地监控策略配置的使用情况

## 验证方式

### 1. 功能验证
```bash
# 测试策略上线自动订阅
curl -X POST "http://localhost:8080/admin/alert/strategy-online" \
  -H "Content-Type: application/json" \
  -d '{
    "strategyType": "apiverify",
    "userName": "testuser",
    "eventCode": "ApiVerify"
  }'
```

### 2. 配置验证
```bash
# 检查策略类型配置
curl "http://localhost:8080/admin/strategy/subscription/push-config?strategyType=apiverify"

# 验证策略类型是否配置
curl "http://localhost:8080/admin/strategy/subscription/check-config?strategyType=apiverify"
```

### 3. 单元测试
```bash
# 运行相关测试
mvn test -Dtest=SLSLinkGeneratorUnifiedConfigTest
mvn test -Dtest=StrategyMonitorConfigServiceTest
```

## 部署注意事项

### 1. 配置检查
- 确保所有策略类型都在统一配置中正确配置
- 验证策略类型与监控项的映射关系
- 检查配置文件的JSON格式正确性

### 2. 回归测试
- 测试策略上线的自动订阅功能
- 验证监控链接生成功能
- 确认推送功能正常工作

### 3. 监控指标
- 监控策略上线的成功率
- 跟踪自动订阅的创建情况
- 观察配置检查的性能表现

## 最佳实践

### 1. 配置管理
```json
// 确保每个策略类型都有完整的配置
{
  "strategyType": {
    "name": "策略名称",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "监控项ID",
        "name": "监控项名称",
        "eventCode": "事件代码",
        "useDynamicLink": true,
        "slsConfig": {
          // SLS配置
        }
      }
    ]
  }
}
```

### 2. 错误处理
```java
// 在策略上线时进行完整的配置检查
if (StringUtils.isEmpty(strategyType)) {
    logger.warn("策略类型为空，跳过自动订阅");
    return;
}

if (!strategyMonitorConfigService.isStrategyTypeConfigured(strategyType)) {
    logger.warn("策略类型 {} 未配置监控项，跳过自动订阅", strategyType);
    return;
}
```

### 3. 日志记录
```java
// 记录详细的配置检查日志
logger.info("检查策略类型配置, strategyType={}", strategyType);
logger.info("策略类型 {} 配置检查通过，开始创建自动订阅", strategyType);
```

## 总结

本次修改实现了以下目标：

1. **简化逻辑**: 直接基于策略类型进行配置检查，逻辑更加直观
2. **提升性能**: 从 O(n*m) 的查找复杂度优化为 O(1)
3. **减少依赖**: AlertNotificationHandler 不再依赖 SLSLinkGenerator
4. **统一配置**: 完全基于策略类型的统一配置进行判断
5. **职责清晰**: 各个组件的职责更加明确和专一

修改后的系统更加高效、清晰，为后续的功能扩展和维护提供了更好的基础。
