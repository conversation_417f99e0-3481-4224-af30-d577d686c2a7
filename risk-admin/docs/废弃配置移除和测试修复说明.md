# 废弃配置移除和测试修复说明

## 概述

本文档描述了移除 `SLSLinkGenerator` 中废弃的配置获取逻辑，并修复 `StrategyMonitorConfigServiceTest` 测试类以适配新的统一配置结构的详细过程。

## 修复内容

### 1. SLSLinkGenerator 废弃配置移除

#### 移除的方法
完全移除了 `getEventCodeMonitorConfig()` 方法，该方法包含了对旧配置 `eventCode.monitor.config` 的兼容逻辑。

**移除前的代码：**
```java
private static Map<String, Object> getEventCodeMonitorConfig(String eventCode) {
    // 先尝试从旧配置中获取
    String oldConfigString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "eventCode.monitor.config", "");
    if (oldConfigString != null && !oldConfigString.isEmpty()) {
        // 处理旧配置的复杂逻辑...
    }
    
    // 从新的统一配置中获取
    return getEventCodeMonitorConfigFromUnified(eventCode);
}
```

#### 简化的方法调用

**getShareableLinkByEventCode() 方法简化：**
```java
// 修复前
Map<String, Object> monitorConfig = getEventCodeMonitorConfig(eventCode);

// 修复后
Map<String, Object> monitorConfig = getEventCodeMonitorConfigFromUnified(eventCode);
```

**isEventCodeConfigured() 方法简化：**
```java
// 修复前
public static boolean isEventCodeConfigured(String eventCode) {
    // 先尝试从旧配置中查找
    String oldConfigString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "eventCode.monitor.config", "");
    if (oldConfigString != null && !oldConfigString.isEmpty()) {
        // 检查旧配置...
    }
    
    // 从新的统一配置中查找
    return isEventCodeConfiguredInUnifiedConfig(eventCode);
}

// 修复后
public static boolean isEventCodeConfigured(String eventCode) {
    // 直接从统一配置中查找
    return isEventCodeConfiguredInUnifiedConfig(eventCode);
}
```

### 2. StrategyMonitorConfigServiceTest 测试修复

#### 测试数据结构更新

**修复前的测试数据：**
```java
dynamicMonitor = new MonitorConfig();
dynamicMonitor.setId("dashboard-1747904265305-468192");
dynamicMonitor.setName("动态监控大盘");
dynamicMonitor.setEventCode("ApiVerify");
dynamicMonitor.setUseDynamicLink(true);
```

**修复后的测试数据：**
```java
dynamicMonitor = new MonitorConfig();
dynamicMonitor.setId("dashboard-1747904265305-468192");
dynamicMonitor.setName("动态监控大盘");
dynamicMonitor.setEventCode("ApiVerify");
dynamicMonitor.setUseDynamicLink(true);
dynamicMonitor.setFrequencyMinutes(30);
dynamicMonitor.setTotalPushes(5);

// 设置 SLS 配置
SLSConfig slsConfig = new SLSConfig();
slsConfig.setDashboardName("dashboard-1747904265305-468192");

List<Map<String, String>> token = new ArrayList<>();
Map<String, String> tokenMap = new HashMap<>();
tokenMap.put("key", "date");
tokenMap.put("value", "60");
token.add(tokenMap);
slsConfig.setToken(token);

List<Map<String, String>> extensions = new ArrayList<>();
Map<String, String> extensionMap = new HashMap<>();
extensionMap.put("autoFresh", "30s");
extensions.add(extensionMap);
slsConfig.setExtensions(extensions);

dynamicMonitor.setSlsConfig(slsConfig);
```

#### Mock 调用更新

**修复前的 Mock：**
```java
mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"))
        .thenReturn(expectedLink);
```

**修复后的 Mock：**
```java
mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(
        eq("dashboard-1747904265305-468192"), 
        anyList(), 
        anyList()))
        .thenReturn(expectedLink);
```

#### 新增的测试用例

##### 1. SLS配置测试
```java
@Test
public void testMonitorConfig_SLSConfig() {
    // 测试SLS配置的设置和获取
    assertNotNull("动态监控应该有SLS配置", dynamicMonitor.getSlsConfig());
    assertEquals("dashboard-1747904265305-468192", dynamicMonitor.getSlsConfig().getDashboardName());
    
    // 测试token配置
    List<Map<String, String>> tokens = dynamicMonitor.getSlsConfig().getToken();
    assertNotNull("Token配置不应该为空", tokens);
    assertEquals(1, tokens.size());
    assertEquals("date", tokens.get(0).get("key"));
    assertEquals("60", tokens.get(0).get("value"));
    
    // 测试extensions配置
    List<Map<String, String>> extensions = dynamicMonitor.getSlsConfig().getExtensions();
    assertNotNull("Extensions配置不应该为空", extensions);
    assertEquals(1, extensions.size());
    assertEquals("30s", extensions.get(0).get("autoFresh"));
}
```

##### 2. 有效配置测试
```java
@Test
public void testMonitorConfig_EffectiveConfiguration() {
    // 测试有效的推送频率配置
    assertEquals(30, dynamicMonitor.getEffectiveFrequencyMinutes(60));
    assertEquals(60, staticMonitor.getEffectiveFrequencyMinutes(60)); // 使用默认值
    
    // 测试有效的总推送次数配置
    assertEquals(5, dynamicMonitor.getEffectiveTotalPushes(3));
    assertEquals(3, staticMonitor.getEffectiveTotalPushes(3)); // 使用默认值
}
```

##### 3. SLS配置参数验证测试
```java
@Test
public void testGenerateMonitorLink_WithSLSConfig() {
    // Mock SLSLinkGenerator
    try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
        String expectedLink = "https://sls.console.aliyun.com/test-sls-config-link";
        mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByConfig(
                eq("dashboard-1747904265305-468192"), 
                argThat(tokens -> tokens.size() == 1 && "date".equals(tokens.get(0).get("key"))), 
                argThat(extensions -> extensions.size() == 1 && "30s".equals(extensions.get(0).get("autoFresh")))))
                .thenReturn(expectedLink);

        // 执行测试
        String result = strategyMonitorConfigService.generateMonitorLink(dynamicMonitor);

        // 验证结果和调用参数
        assertEquals(expectedLink, result);
    }
}
```

##### 4. 无SLS配置回退测试
```java
@Test
public void testGenerateMonitorLink_NoSLSConfig() {
    // 创建一个没有SLS配置的动态监控
    MonitorConfig noSLSConfigMonitor = new MonitorConfig();
    noSLSConfigMonitor.setUseDynamicLink(true);
    noSLSConfigMonitor.setUrl("http://fallback.url");
    // 没有设置 slsConfig

    // 执行测试
    String result = strategyMonitorConfigService.generateMonitorLink(noSLSConfigMonitor);

    // 验证结果 - 应该回退到静态URL
    assertEquals("http://fallback.url", result);
}
```

## 修复效果

### 1. 代码简化
- **移除冗余代码**: 删除了约50行的废弃配置处理代码
- **简化调用链**: 直接使用统一配置，减少了方法调用层次
- **提高可读性**: 代码逻辑更加清晰，易于理解和维护

### 2. 测试覆盖增强
- **新配置结构测试**: 完整测试了SLS配置的设置和获取
- **参数验证测试**: 验证了传递给SLSLinkGenerator的参数正确性
- **边界情况测试**: 测试了无SLS配置时的回退逻辑
- **配置优先级测试**: 测试了有效配置的计算逻辑

### 3. 向前兼容性
- **完全移除旧配置**: 不再支持旧的 `eventCode.monitor.config` 配置
- **统一配置标准**: 所有功能都使用 `strategy-monitor-unified-config` 配置
- **清晰的迁移路径**: 为完全迁移到新配置提供了明确的指导

## 配置对比

### 旧配置结构（已废弃）
```json
// eventCode.monitor.config
{
  "ApiVerify": {
    "dashboardName": "dashboard-1747904265305-468192",
    "token": [{"key": "date", "value": "60"}],
    "extensions": [{"autoFresh": "30s"}]
  }
}

// strategy-monitor-mapping
{
  "apiverify": {
    "name": "API验证策略",
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "API调用监控",
        "eventCode": "ApiVerify",
        "useDynamicLink": true
      }
    ]
  }
}
```

### 新统一配置结构
```json
// strategy-monitor-unified-config
{
  "apiverify": {
    "name": "API验证策略",
    "frequencyMinutes": 60,
    "totalPushes": 3,
    "monitors": [
      {
        "id": "dashboard-1747904265305-468192",
        "name": "API调用监控",
        "eventCode": "ApiVerify",
        "useDynamicLink": true,
        "frequencyMinutes": 30,
        "totalPushes": 5,
        "slsConfig": {
          "dashboardName": "dashboard-1747904265305-468192",
          "token": [{"key": "date", "value": "60"}],
          "extensions": [{"autoFresh": "30s"}]
        }
      }
    ]
  }
}
```

## 部署注意事项

### 1. 配置迁移
- **确保统一配置完整**: 部署前确认所有必要的监控配置都已迁移到统一配置中
- **删除旧配置**: 可以安全地删除 `eventCode.monitor.config` 配置
- **验证配置格式**: 确保统一配置的JSON格式正确

### 2. 测试验证
```bash
# 运行单元测试
mvn test -Dtest=StrategyMonitorConfigServiceTest

# 运行集成测试
mvn test -Dtest=SLSLinkGeneratorUnifiedConfigTest
```

### 3. 功能验证
```bash
# 验证事件代码配置检查
curl "http://localhost:8080/admin/strategy/subscription/check-config?strategyType=apiverify"

# 验证动态链接生成
curl "http://localhost:8080/admin/strategy/subscription/test-unified-link?strategyType=apiverify&monitorId=dashboard-1747904265305-468192"
```

## 总结

本次修复完成了以下目标：

1. **彻底移除废弃配置**: 删除了对旧配置 `eventCode.monitor.config` 的所有引用和兼容代码
2. **简化代码结构**: 统一使用 `strategy-monitor-unified-config` 配置，简化了代码逻辑
3. **完善测试覆盖**: 更新了测试用例以适配新的配置结构，并增加了新的测试场景
4. **提高代码质量**: 移除了冗余代码，提高了代码的可读性和可维护性

修复后的代码更加简洁、高效，完全基于统一配置工作，为后续的功能扩展和维护提供了良好的基础。
