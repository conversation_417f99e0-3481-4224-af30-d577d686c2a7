package com.ucredit.riskanalysis.data;

import com.ucredit.riskanalysis.data.vo.LoanAmount;
import com.youxin.risk.di.mapper.account.AccountMapper;
import com.youxin.risk.di.mapper.accountProxy.AccountProxyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class B180HavePayPrincipalAvgCalculator {
    @Autowired
    private AccountProxyMapper accountProxyMapper;

    @Autowired
    private AccountMapper accountMapper;

    public Object getValue(String businessUserKey) {
        List<LoanAmount> loanAmounts = accountProxyMapper.getLoanAmounts(businessUserKey);
        List<LoanAmount> loanRepayAmounts = accountMapper.getLoanRepayAmountsInDays(businessUserKey, 180);
        Map<String, Double> loanKey2amount = B90HavePayPrincipalMaxCalculator.toMap(loanAmounts);
        //TODO
        return loanRepayAmounts.stream().filter(x -> loanKey2amount.containsKey(x.getLoanKey()))
                .map(x -> x.getAmount() / loanKey2amount.get(x.getLoanKey())).mapToDouble(x->x).average().orElse(0);
    }
}


