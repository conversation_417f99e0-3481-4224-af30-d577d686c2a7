package com.ucredit.riskanalysis.data;

import com.alibaba.fastjson.JSON;
import com.ucredit.riskanalysis.common.product.RiskProduct;
import com.ucredit.riskanalysis.data.vo.LendRequestInfo;
import com.ucredit.riskanalysis.dc.submit.model.DcSubmitBankCard;
import com.ucredit.riskanalysis.dc.submit.model.DcSubmitOperationLog;
import com.ucredit.riskanalysis.dc.submit.service.DcSubmitBankCardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class BankCardLendRequestInfoCalculator extends LendRequestInfoCalculator {

    private Logger logger = LoggerFactory.getLogger(BankCardLendRequestInfoCalculator.class);

    @Autowired
    DcSubmitBankCardService submitBankCardService;

    public LendRequestInfo getValue(String loanKey, String cardNo) {
    	LendRequestInfo lendRequestInfo = null;
        try {
            lendRequestInfo = new LendRequestInfo();
            // 查出所有的userKey
            if (cardNo == null) {
                return lendRequestInfo;
            }
            List<DcSubmitBankCard> bankCards = this.submitBankCardService.findSubmitBankCardByBankCardNumber(cardNo);
            if (bankCards == null) {
                logger.info("BankCardLendRequestInfoCalculator bankCards is empty,loanKey:{}, cardNo:{}",loanKey,cardNo);
                return lendRequestInfo;
            }

            Set<String> userKeys = new HashSet<>();
            for (DcSubmitBankCard bankCard : bankCards) {
                DcSubmitOperationLog operationLog = submitOperationLogDao.get(bankCard.getApplyId());
                if (null == operationLog) {
                    continue;
                }
                String sourceSystem = operationLog.getSourceSystem();
                if (RiskProduct.RONG_360.name().equals(sourceSystem)) {
                    userKeys.add(RiskProduct.PAY_DAY_LOAN.name() + SPERATOR + bankCard.getUserKey());
                } else if (RiskProduct.HAO_HUAN.name().equals(sourceSystem) || RiskProduct.PAY_DAY_LOAN.name().equals(sourceSystem)) {
                    userKeys.add(sourceSystem + SPERATOR + bankCard.getUserKey());
                }
            }
//            logger.info("BankCardLendRequestInfoCalculator loanKey:{}, cardNo:{},userKeys:{},lendRequestInfo:{}",
//                    loanKey,cardNo, JSON.toJSONString(userKeys), JSON.toJSONString(lendRequestInfo));

            calLendRequestInfo(loanKey, lendRequestInfo, userKeys);
        } catch (Exception e) {
            this.logger.error("calculate feature {} error", this.getFeatureName(), e);
        }
        return lendRequestInfo;
    }

    private String getFeatureName() {
        return "bank_card_lend_request_info";
    }

}
