package com.ucredit.riskanalysis.data;

import com.youxin.risk.di.mapper.account.AccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class B180PrepayPeriodCntAvgCalculator {
    @Autowired
    private AccountMapper accountMapper;

    public Object getValue(String businessUserKey) {
        Double result = accountMapper.b180PrepayPeriodCntAvg(businessUserKey);
        return String.valueOf(result);

    }
}
