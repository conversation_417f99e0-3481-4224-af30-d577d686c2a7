package com.ucredit.riskanalysis.data;

import com.youxin.risk.commons.utils.SpringContext;
import com.youxin.risk.di.mapper.accountProxy.AccountProxyMapper;
import com.youxin.risk.di.service.hbase.RiskUserPayService;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.youxin.risk.di.common.Constant.*;

/**
 * 还款次数相关
 *
 * <AUTHOR>
 */
@Service
public class RepayTimesCalculator {

    private final AccountProxyMapper accountProxyMapper = SpringContext.getBean(AccountProxyMapper.class);
    private final RiskUserPayService riskUserPayService = SpringContext.getBean(RiskUserPayService.class);


    /**
     * 近 {days}天 主动还款成功次数
     *
     * @param userKey userKey
     * @param days    时间范围
     * @return 主动还款成功次数
     */
    public String getRepayTimes(boolean switchToNewData, String userKey, int days) {
        //主动还款成功次数
        Long successDeductionTimes = null;
        if (switchToNewData) {
            successDeductionTimes = (Long) riskUserPayService.getNewUserPayData(SELECT_SUCCESS_REPAY_TIMES, userKey, days);
        } else {
            successDeductionTimes = accountProxyMapper.selectSuccessRepayTimes(userKey, days);
            riskUserPayService.compareMysqlAndHbase(successDeductionTimes, SELECT_SUCCESS_REPAY_TIMES, userKey, days);
        }
        if (successDeductionTimes == null) {
            return "null";
        }

        //总还款次数
        Long totalDeductionTimes = null;
        if (switchToNewData) {
            totalDeductionTimes = (Long) riskUserPayService.getNewUserPayData(SELECT_TOTAL_REPAY_TIMES, userKey, days);
        } else {
            totalDeductionTimes = accountProxyMapper.selectTotalRepayTimes(userKey, days);
            riskUserPayService.compareMysqlAndHbase(totalDeductionTimes, SELECT_TOTAL_REPAY_TIMES, userKey, days);
        }
        if (totalDeductionTimes == null || totalDeductionTimes == 0) {
            return "null";
        }
        return String.valueOf(successDeductionTimes);
    }

    /**
     * 近 {days}天 扣款次数
     *
     * @param userKey userKey
     * @param days    时间范围
     * @return payment_cnt
     */
    public Object getPaymentCtn(boolean switchToNewData, String userKey, int days) {
        Long result = null;
        if (switchToNewData) {
            result = (Long) riskUserPayService.getNewUserPayData(SELECT_PAYMENT_CTN, userKey, days);
        } else {
            result = accountProxyMapper.selectPaymentCtn(userKey, days);
            riskUserPayService.compareMysqlAndHbase(result, SELECT_PAYMENT_CTN, userKey, days);
        }
        return Objects.isNull(result) ? "null" : result;
    }
}
