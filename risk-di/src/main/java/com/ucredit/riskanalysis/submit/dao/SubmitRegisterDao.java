package com.ucredit.riskanalysis.submit.dao;

import com.google.common.collect.Lists;
import com.ucredit.riskanalysis.submit.model.SubmitRegister;
import com.youxin.risk.commons.utils.MaskUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class SubmitRegisterDao extends SubmitInfoBaseDao<SubmitRegister> {

	@Override
	protected Class<SubmitRegister> getEntityClass() {
		return SubmitRegister.class;
	}


	/**
	 * 查询好还关联系统注册用户
	 *
	 * @param mobile
	 * @return
	 */
	public List<SubmitRegister> getRegistersByMobile(String mobile) {

		String hql = "FROM SubmitRegister WHERE mobile=:mobile group by userKey";
		return this
				.createQuery(hql)
				.setParameter("mobile", mobile).list();
	}

	/**
	 * 查询好还关联系统注册用户
	 *
	 * @param mobile
	 * @return
	 */
	public List<SubmitRegister> getRegistersByMobiles(String mobile) {
		List<String> mobiles = Lists.newArrayList(mobile, MaskUtils.maskValue(mobile));
		String hql = "FROM SubmitRegister WHERE mobile in (:mobiles) group by userKey";
		return this
				.createQuery(hql)
				.setParameterList("mobiles", mobiles).list();
	}
}
