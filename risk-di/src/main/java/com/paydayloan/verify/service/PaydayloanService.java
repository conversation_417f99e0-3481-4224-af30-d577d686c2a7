package com.paydayloan.verify.service;

import com.paydayloan.verify.model.VerifyResult;
import com.paydayloan.verify.dao.VerifyResultDao;
import com.ucredit.riskanalysis.common.feature.FeatureResult;
import com.ucredit.riskanalysis.common.product.RiskProduct;
import com.ucredit.riskanalysis.report.model.ReportRequest;
import com.ucredit.riskanalysis.report.vo.VerifyResultVo;
import com.ucredit.riskanalysis.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PaydayloanService {

	private static final Logger LOG = LoggerFactory.getLogger(PaydayloanService.class);

	@Autowired
	private VerifyResultDao verifyResultDao;

	public VerifyResultVo getVerifyResult(Map<String, List<ReportRequest>> list) {

		VerifyResultVo result = new VerifyResultVo();

		if (list.isEmpty()) {
			result.setLastApplyTime("None");
			result.setLastRejectTime(FeatureResult.INVALID.toString());
			result.setReason(FeatureResult.INVALID.toString());
			return result;
		}

		Map<String, String> userMap = new HashMap<>();
		String splitor = "_";
		List<Integer> loanIds = new ArrayList<>();
		Date lastRequestDate = null;
		for (Map.Entry<String, List<ReportRequest>> e : list.entrySet()) {
			String system = e.getKey();
			List<ReportRequest> reportRequests = e.getValue();
			for (ReportRequest reportRequest : reportRequests) {
				userMap.put(reportRequest.getLoanId() + splitor + reportRequest.getUserKey(), system);
				loanIds.add(reportRequest.getLoanId());
				Date requestDate = reportRequest.getCreateTime();
				if (lastRequestDate == null || DateUtils.compareDate(requestDate, lastRequestDate) == 1) {
					lastRequestDate = requestDate;
				}
			}
		}

		if (lastRequestDate == null) {
			result.setLastApplyTime("None");
		} else {
			result.setLastApplyTime(lastRequestDate.toString());
		}
		List<VerifyResult> verifyResults = verifyResultDao.findResultListByLoanList(loanIds);
		if (verifyResults == null) {
			result.setLastRejectTime(FeatureResult.INVALID.toString());
			result.setReason(FeatureResult.INVALID.toString());
			return result;
		}

		String lastRejectReasonCode = "";
		Date lastRejectDate = null;
		int qualifiedNumber = 0;
		int haohuanRejectNumber = 0;
		int pdlRejectNumber = 0;
		for (VerifyResult verify : verifyResults) {
			String userKey = verify.getUserKey();
			Integer loanId = verify.getLoanId();
			String sourceSystem = userMap.get(loanId + splitor + userKey);
			if (StringUtils.isBlank(sourceSystem)) {
				continue;
			}

			String reasonCode = verify.getReasonCode();
			Date finalVerifyTime = verify.getFinalVerifyTime();
			Boolean isFinalPassed = verify.getIsFinalPassed();
			if (isFinalPassed != null) {
				if (isFinalPassed) {
					qualifiedNumber++;
				} else {// reject
					if (RiskProduct.HAO_HUAN.name().equals(sourceSystem)) {
						haohuanRejectNumber++;
					} else if (RiskProduct.PAY_DAY_LOAN.name().equals(sourceSystem)
							|| RiskProduct.RONG_360.name().equals(sourceSystem)) {
						pdlRejectNumber++;
					}

					if (lastRejectDate == null
							|| DateUtils.compareDate(finalVerifyTime,
							lastRejectDate) == 1) {
						lastRejectDate = finalVerifyTime;
						lastRejectReasonCode = reasonCode;
					}
				}
			}
		}

		result.setQualifiedNumber(Integer.toString(qualifiedNumber));
		result.setHaohuanRejectNumber(haohuanRejectNumber);
		result.setPdlRejectNumber(pdlRejectNumber);

		if (lastRejectDate == null) {
			result.setLastRejectTime(FeatureResult.NULL.toString());
			result.setReason(FeatureResult.NULL.toString());
		} else {
			result.setReason(lastRejectReasonCode);
			result.setLastRejectTime(DateUtils.getFormatDate(lastRejectDate));
		}

		return result;

	}


}
