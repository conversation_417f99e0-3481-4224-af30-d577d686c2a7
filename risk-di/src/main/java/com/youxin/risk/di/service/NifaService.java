package com.youxin.risk.di.service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.TaskDataVo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class NifaService {
    private Logger logger = LoggerFactory.getLogger(NifaService.class);

    @Autowired
    private NifaServiceMongoDao nifaServiceMongoDao;

    public NifaOutstandVo search(String userKey) {

        NifaOutstandVo nifaOutstandVo = new NifaOutstandVo();

        nifaOutstandVo.setUserKey(userKey);
        nifaOutstandVo.setResult("未获取");
        nifaOutstandVo.setOutstandCount("null");
        nifaOutstandVo.setCreateTime(new Date());
        try {
            TaskDataVo dataVo = nifaServiceMongoDao.getByUserKey(userKey);
            if (dataVo != null) {
                nifaOutstandVo.setCreateTime(dataVo.getCreateTime());
                if (!StringUtils.isEmpty(dataVo.getData())) {
                    JSONObject jsonObject = JSONObject.parseObject(dataVo.getData());
                    String dataInner = jsonObject.getString("data");
                    if (!StringUtils.isEmpty(dataInner)) {
                        {
                            jsonObject = JSONObject.parseObject(dataInner);
                            if (jsonObject != null) {
                                dataInner = jsonObject.getString("data");
                            }
                            if (!StringUtils.isEmpty(dataInner)) {
                                {
                                    jsonObject = JSONObject.parseObject(dataInner);
                                    String s = jsonObject.getString("outstandcount");
                                    if (!StringUtils.isEmpty(s)) {
                                        s = s.trim();
                                        nifaOutstandVo.setOutstandCount(s);
                                        if (s.equals("0")) {
                                            nifaOutstandVo.setResult("否");
                                        } else {
                                            nifaOutstandVo.setResult("是");
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
                nifaOutstandVo.setCreateTime(dataVo.getCreateTime());
            }
        } catch (Exception ex) {
            logger.error("NifaService.search : userKey" + userKey, ex);
        }
        return nifaOutstandVo;
    }
}