package com.youxin.risk.di.service.hbase;

import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.UUID;


public class HbaseUtil {
	

	
	
	public static String getValue( byte[] value) {
		if(value==null){
			return null;
		}else{
			return getValue("string",value).toString();
		}
		
	}
	
	
	public static Object getValue(String type, byte[] value) {
		switch (type) {
		case "string":
			return value == null ? null : Bytes.toString(value);
		case "date":
			return value == null ? null : new Date(Bytes.toLong(value));
		case "bigdecimal":
			return value == null ? null : Bytes.toBigDecimal(value);
		case "boolean":
			return value == null ?null : Bytes.toBoolean(value);
		case "double":
			return value == null ? null : Bytes.toDouble(value);
		case "float":
			return value == null ? null : Bytes.toFloat(value);
		case "int":
			return value == null ? null : Bytes.toInt(value);
		case "short":
			return value == null ? null : Bytes.toShort(value);
		case "long":
			return value == null ? null : Bytes.toLong(value);
		default:
			return Bytes.toString(value);

		}
	}

	/**
	 * 获取rowKey
	 * 随机数 + year + month + day + hour + UUID
	 * @return rowKey
	 */
	public static String getRowKey() {
		DecimalFormat decimalFormat = new DecimalFormat("00");
		DateTime now = new DateTime();
		int year = now.getYear();
		int monthOfYear = now.getMonthOfYear();
		int dayOfMonth = now.getDayOfMonth();
		String random = decimalFormat.format(new Random().nextInt(99));
		return random + "-" + year + monthOfYear + dayOfMonth + "-" + UUID.randomUUID().toString().replaceAll("-","");
	}
	
	public static Date getValueDate( byte[] value) {
		return value == null ? null : new Date(Bytes.toLong(value));
	}
	
	public static BigDecimal getValueBigdecimal( byte[] value) {
		return value == null ? null : Bytes.toBigDecimal(value);
	}
	
	public static Boolean getValueBoolean( byte[] value) {
		return value == null ?null : Bytes.toBoolean(value);
	}
	
	public static Double getValueDouble( byte[] value) {
		return value == null ? null : Bytes.toDouble(value);
	}
	
    public static Float getValueFloat( byte[] value) {
    	return value == null ? null : Bytes.toFloat(value);
	}
    
    public static Integer getValueInt( byte[] value) {
    	return value == null ? null : Bytes.toInt(value);
	}
    
    public static Short getValueShort( byte[] value) {
    	return value == null ? null : Bytes.toShort(value);
	}
    
    public static Long getValueLong( byte[] value) {
    	return value == null ? null : Bytes.toLong(value);
	}

	/**
	 * 获取当天凌晨的时间戳
	 * @return
	 */
	public static long getEarlyMorningTimestamp() {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		return c.getTimeInMillis();
	}

	/**
	 * 根据指定的时间戳获取其当天凌晨的时间戳
	 * @param timestamp
	 * @return
	 */
	public static long getEarlyMorningTimestamp(long timestamp) {
		return getEarlyMorningTimestamp(timestamp, 0);
	}

	public static long getEarlyMorningTimestamp(long timestamp, long days) {
		Date date = new Date(timestamp);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date parse = null;
		try {
			parse = sdf.parse(sdf.format(date));
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return parse.getTime() + days * 60 * 60 * 24 * 1000;
	}


}
