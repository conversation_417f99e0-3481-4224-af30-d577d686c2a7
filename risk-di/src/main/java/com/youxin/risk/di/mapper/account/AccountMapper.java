package com.youxin.risk.di.mapper.account;

import com.ucredit.riskanalysis.data.vo.LoanAmount;
import com.ucredit.riskanalysis.data.vo.LoanPeriodNum;
import com.youxin.risk.di.model.RepayPlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AccountMapper {
    Long selectMaxOverdueDaysPayoff(@Param("userKey") String userKey,@Param("days") int days);

    Long selectMaxOverdueDaysNotPayoff(@Param("userKey") String userKey,@Param("days") int days);

    List<String> selectLoanKeysByUserInDays(@Param("userKey") String userKey, @Param("days") Integer days);

    Double selectPrincipalByLoan(@Param("loanKey") String loanKey);

    Double selectOverdueByLoan(@Param("loanKey") String loanKey);

    List<String> selectPayoffLoanKeysByUser(@Param("userKey") String userKey);

    Long selectPayoffCountByLoanInDays(@Param("loanKey") String loanKey, @Param("days") Integer days);

    Long selectAheadCountByLoan(@Param("loanKey") String loanKey);

    List<String> selectLoanKeysByUser(@Param("userKey") String userKey);

    Long selectAheadCountByUser(@Param("userKey") String userKey);

    Long selectShouldCountByLoan(String userKey);

    List<Long> selectMaxOverdueDayByLoanKeys(@Param("loanKeys")List<String> loanKeys);

    Long selectPayOffDealCnt(@Param("accountUserKey")String accountUserKey);

    Long selectTotalRepayTimes(@Param("accountUserKey")String accountUserKey,@Param("days")int days);

    Long selectTotalRepayTimesAfterHour(@Param("accountUserKey")String accountUserKey, @Param("days")int days, @Param("hour")int hour);

    List<LoanAmount> getLoanRepayAmountsInDays(@Param("userKey") String businessUserKey,  @Param("days")Integer days);

    Double b180PrepayPeriodCntAvg(@Param("userKey") String businessUserKey);

    /**
     * 提前1天以上还款金额
     */
    Double repaykeyB1dAmt(@Param("userKey") String accountUserKey);

    /**
     * 未到期结清订单数
     */
    Long repaykeyB0dCnt(@Param("userKey") String accountUserKey);

    /**
     * 提前3天以上还款次数
     */
    Long repaykeyB3dCnt(@Param("userKey") String accountUserKey);

    /**
     * 7天内结清订单数
     * @param accountUserKey
     * @return
     */
    Long repaykeyPay7Cnt(@Param("userKey") String accountUserKey);

    /**
     * 当天结清订单数
     * @param accountUserKey
     * @return
     */
    Long repaykey0dCnt(@Param("userKey") String accountUserKey);

    /**
     * 周末结清订单数
     * @param accountUserKey
     * @return
     */
    Long repaykeyWeekendCnt(@Param("userKey") String accountUserKey);

    /**
     * 放款3天内结清订单数
     * @param accountUserKey
     * @return
     */
    Long repaykeyA3sCnt(@Param("userKey") String accountUserKey);

    /**
     * 结清订单数
     * @param accountUserKey
     * @return
     */
    Long repaykeyPayedCnt(@Param("userKey") String accountUserKey);

    /**
     * 借款总金额
     */
    Double repaykeyAmt(@Param("userKey") String accountUserKey);

    Long b90EalistPayDayCnt(String businessUserKey);

    List<LoanPeriodNum> getRepayPeriodNum(String businessUserKey);

    Double b90PrepayPrincipalSumAvg(String businessUserKey);

    Double b90PrepayPrincipalSumSumRate(String businessUserKey);

    Double payoffTime914CntB180Rate(String businessUserKey);

    List<Long> selectAccountMaxDpdPrepay(@Param("userKey")String userKey,@Param("days")int days);
    Integer isClear(@Param("userKey")String userKey);
    Integer clearTimestamp(@Param("userKey")String userKey);
    Integer clear2now(@Param("userKey")String userKey);
    Integer everMaxDpd(@Param("userKey")String userKey);
    Integer aheadRepayCntInst(@Param("userKey")String userKey);
    Integer loanCntInst(@Param("userKey")String userKey);
    Integer loanCntTm1(@Param("userKey")String userKey);
    Integer billCnt(@Param("userKey")String userKey);
    Integer beginClear2now(@Param("userKey")String userKey);
    Integer inlast1mRepayCntInst(@Param("userKey")String userKey);
    Integer inlast3mDueCntInst(@Param("userKey")String userKey);
    Integer inlast3mDueCntTm1(@Param("userKey")String userKey);
    Integer inlast6mRepayCnt(@Param("userKey")String userKey);
    Integer recentDue2now(@Param("userKey")String userKey);
    Integer beginLoan2nowInst(@Param("userKey") String businessUserKey);
    Integer beginLoan2nowTm1(@Param("userKey") String businessUserKey);
    Integer inlast180dLoanCntInst(@Param("userKey") String businessUserKey);
    Integer inlast180dLoanCntTm1(@Param("userKey") String businessUserKey);
    Integer inlast30dLoanCnt(@Param("userKey") String businessUserKey);
    Integer inlast90dLoanCnt(@Param("userKey") String businessUserKey);
    Integer recentLoan2nowInst(@Param("userKey") String businessUserKey);
    Integer recentLoan2nowTm1(@Param("userKey") String businessUserKey);
    Float aheadRepayPctTm1(@Param("userKey") String accountUserKey);
    Integer inlast1mRepayCntTm1(String accountUserKey);
    List<Map<String, Object>> getRepayPlan(String accountUserKey);
    List<Map<String, Object>> getLoan(String accountUserKey);
    Integer inlast30d_loan_cnt_inst(String accountUserKey);
    Integer inlast15d_loan_cnt_inst(String accountUserKey);
    Integer ahead_repay_pct_inst(String accountUserKey);
    Integer inlast6m_due_cnt_inst(String accountUserKey);
    Integer inlast180d_avg_ahead_days_inst(String accountUserKey);

    Integer queryNoOverDueTimes(String accountUserKey);
    Integer queryOverDueCountForNow(String accountUserKey);
    Integer queryOverDueCountForHistory(String accountUserKey);
    Integer queryOverDueUserCountNow(@Param("userKeyList")List<String> userKeyList);//查询list中当前为逾期状态的用户数量
    Integer queryOverdueMaxDaysUserCount(@Param("userKeyList")List<String> userKeyList, @Param("days")int days);//查询最大历史逾期天数超过days的人数
    Integer queryOverdueMaxDaysUserCountNear90D(@Param("userKeyList")List<String> userKeyList, @Param("days")int days);//查询近90天还款中最大逾期天数超过days的人数
}
