package com.youxin.risk.di.service.adapter.smsfeature;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 打标 processor
 * <AUTHOR>
 * @since 2021/12/15 20:05
 */
@Component
public class SmsFeatureTaggerProcessor {

    private static final Map<String,BaseTagger> TAGGER_MAP = new HashMap<>();

    /**
     * 打标器注册
     * <AUTHOR>
     * @since 2022/3/11 10:06
     */
    public void registerTagger(String taggerName, BaseTagger baseTagger){
        TAGGER_MAP.putIfAbsent(taggerName,baseTagger);
    }

    /**
     * 短信打标结果
     * @param body 短信内容
     * @param level 标签等级
     * @param preTag 依赖的上级标签
     * <AUTHOR>
     * @since 2021/12/15 20:07
     */
    public String process(String body,Integer level,String preTag){
        // 排序
        String resultTag = null;
        List<BaseTagger> syncBaseTaggers = new ArrayList<>(TAGGER_MAP.values());
        AnnotationAwareOrderComparator.sort(syncBaseTaggers);
        List<BaseTagger> taggers = syncBaseTaggers.stream().filter(t -> t.level().equals(level)).collect(Collectors.toList());
        for(BaseTagger baseTagger : taggers){
            resultTag = baseTagger.tagger(body,preTag);
            // 已有结果,则不需要后续的打标器操作
            if(StringUtils.isNotBlank(resultTag)){
                break;
            }
        }
        return resultTag;
    }

}
