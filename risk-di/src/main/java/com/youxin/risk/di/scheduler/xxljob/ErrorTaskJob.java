package com.youxin.risk.di.scheduler.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.di.scheduler.ErrorTaskScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ErrorTaskJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(ErrorTaskJob.class);

    @Autowired
    private ErrorTaskScheduler errorTaskScheduler;

    @XxlJob(value="errorTaskJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        try {
            logger.info("xxljob:errorTaskJob execute start");
            errorTaskScheduler.doJob();
            logger.info("xxljob:errorTaskJob execute end");
        }catch (Exception e){
            XxlJobLogger.log("执行{}失败，失败信息为：","errorTaskJob",e);
            return  ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


}
