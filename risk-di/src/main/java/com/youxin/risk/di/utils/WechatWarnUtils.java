package com.youxin.risk.di.utils;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @Desc 企业微信告警服务
 * @Auth linchongbin
 * @Date 2023/5/22 20:39
 */
@Component
public class WechatWarnUtils {

    private static Logger logger = LoggerFactory.getLogger(WechatWarnUtils.class);

    @Value("${wechat.credit.alert.url}")
    private String wechatCreditAlertUrl;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 征信告警信息发送
     */
    public void sendCreditAlertMsg(String warnContent) {
        try {
            restTemplate.postForEntity(wechatCreditAlertUrl, buildWechatWarnBody(warnContent), String.class);
        } catch (Exception ex) {
            logger.error("sendCreditAlertMsg error", ex);
        }
    }

    /**
     * 构造企业微信告警信息
     *
     * @param msg
     * @return
     */
    private JSONObject buildWechatWarnBody(String msg) {
        JSONObject content = new JSONObject();
        content.put("mentioned_mobile_list", new String[]{"18668229835", "13716014930", "18703691283", "15764384067", "18652888033", "13241129767"});
        content.put("content", msg);
        JSONObject requestBody = new JSONObject();
        requestBody.put("msgtype", "text");
        requestBody.put("text", content);
        return requestBody;
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
