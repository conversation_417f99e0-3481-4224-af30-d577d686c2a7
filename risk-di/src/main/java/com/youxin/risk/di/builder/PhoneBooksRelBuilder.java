package com.youxin.risk.di.builder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jodd.util.collection.SortedArrayList;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 通讯录相关构造器
 * <AUTHOR>
 * @since 2021/12/2 15:37
 */
public class PhoneBooksRelBuilder {
    /**
     * 获取地区统计后的map key地区，value频次
     * <AUTHOR>
     * @since 2021/12/2 15:33
     */
    public static Map<String,Integer> getAreaCountMap(JSONArray phoneBooks, String area){
        Map<String,Integer> cityCount = new LinkedHashMap<>();
        for (Object book : phoneBooks) {
            JSONObject phoneBook = (JSONObject) book;
            String address = phoneBook.getString(area);
            if (StringUtils.isNotBlank(address)) {
                cityCount.compute(address, (key, val) -> val == null ? 1 : val + 1);
            }
        }
        return cityCount;
    }

    /**
     * 获取排序后的地区map key频次，value地区
     * 并列的以逗号隔开
     * <AUTHOR>
     * @since 2021/12/2 15:52
     */
    public static Map<Integer,String> getCountAreaMap(Map<String,Integer> areaCountMap){
        // 排序
        List<String> areaCountList = new SortedArrayList<>(Comparator.comparingInt(o -> Integer.parseInt(o.split(":")[1])));
        areaCountMap.forEach((key,val) -> areaCountList.add(key + ":" + val));
        // 转为 次数 + 城市 map
        Map<Integer,String> countAreaMap = new LinkedHashMap<>();
        // 反向读取
        for(int i = areaCountList.size() - 1;i >= 0;i --){
            String[] line = areaCountList.get(i).split(":");
            countAreaMap.compute(Integer.parseInt(line[1]),(key,val) -> val == null ? line[0] : val + "," + line[0]);
        }
        return countAreaMap;
    }
}
