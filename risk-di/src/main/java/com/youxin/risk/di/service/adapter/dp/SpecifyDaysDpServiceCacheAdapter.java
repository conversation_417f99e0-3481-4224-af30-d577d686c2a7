package com.youxin.risk.di.service.adapter.dp;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * 数据源指定缓存天数
 *
 * <AUTHOR>
 * @since 2023/4/23 10:18
 */
public class SpecifyDaysDpServiceCacheAdapter extends ServiceAdapter {

    private static final Logger logger = LoggerFactory.getLogger(SpecifyDaysDpServiceCacheAdapter.class);

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();

        Map<String, Object> params = this.generatorParams(request);
        if(params.get("systemid") == null || params.get("recordType") == null ){
            LoggerProxy.error("SpecifyDaysDpServiceCacheAdapter.serviceResponseError", logger, "service response status error, request={}", JacksonUtil.toJson(request));
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        String systemId = (String) params.get("systemid");
        String recordType = (String) params.get("recordType");
        String serviceUrl = diService.getServiceUrl().endsWith("/") ? diService.getServiceUrl() : diService.getServiceUrl() + "/";
        String url = serviceUrl + recordType + String.format("?systemid=%s&userkey=%s",systemId ,request.getUserKey());
        String result = SyncHTTPRemoteAPI.get(url, diService.getServiceTimeout().intValue());
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("SpecifyDaysDpServiceCacheAdapter.serviceResponseError", logger, "dp response null, request={}", JacksonUtil.toJson(request));
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        if(params.get("days") != null){
            int days = Integer.parseInt(params.get("days").toString());
            JSONObject object = JSON.parseObject(result);
            JSONArray records = Optional.ofNullable(object).map(t -> t.getJSONArray("records")).orElse(null);
            boolean isDataOverdue = true;
            if(records != null && records.size() > 0){
                // 数据创建时间
                long timestamp = ((JSONObject)records.get(0)).getLong("timestamp");
                Date createDate = new Date(timestamp);
                Date nowDate = new Date();
                // 时间差,精确到秒
                long timeGap = DateUtil.between(createDate,nowDate, DateUnit.SECOND);
                if(timeGap <= (days * 24 * 60 *60)){
                    isDataOverdue = false;
                    LoggerProxy.info("SpecifyDaysDpServiceCacheAdapter", logger, "data not overdue, request={}", JacksonUtil.toJson(request));
                }
            }
            if(isDataOverdue){
                // 数据获取时间超过days限制,抛弃
                LoggerProxy.info("SpecifyDaysDpServiceCacheAdapter", logger, "data overdue or data null, request={}", JacksonUtil.toJson(request));
                response.setRetCode(RetCodeEnum.NO_DATA.getValue());
                response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
                return response;
            }
        }
        // 数据未过期
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("data", result);
        response.setResult(resultMap);
        response.setData(result);
        return response;
    }
}
