package com.youxin.risk.di.service.adapter.dp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.remote.model.datacenter.DcRequest;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringManager;
import com.youxin.risk.commons.vo.DpRecordVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.cookie.SM;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

/**
 * 获取短信记录
 */
public class SyncSmsRecordServiceAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(SyncSmsRecordServiceAdapter.class);

    private final static StringManager SM = StringManager.getManager("conf/conf");

    private static String dcInsideUrl = SM.getString("dc.inside.url");

    private static final String QUERY_URL = "/dc/queryServices";

    private static int TIMEOUT_MILLIS = 60000;

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        Map<String, Object> params = request.getParams();
        String systemId = (String) params.get("systemId");
        params.put("systemid",params.get("systemId"));
        String userKey = request.getUserKey();
        String jobId = getSmsReportJobIdFromDataCenter(userKey, systemId);

        LOGGER.info("deal SmsReport,get jobId from dataCenter again,jobId:{},", jobId);
        if (StringUtils.isBlank(jobId)){
            LOGGER.info("deal SmsReport,jobId is null finally,return nodata response,userKey:{},", userKey);
            return buildDefaultDataResponse(request.getRequestId());
        }
        request.getParams().put("jobid", jobId);

        ServiceResponse response = new ServiceResponse();
        try {
            //优先查dp
            response = responseFromCallDp(jobId, request);
            long start = System.currentTimeMillis();
            if (RetCodeEnum.SUCCESS.equals(response.getRetCode())) {
                LOGGER.info("deal SmsReport from dp success cost:{} ms,userKey:{},jobId:{}", System.currentTimeMillis() - start, request.getUserKey(), jobId);
                return response;
            }
            LOGGER.info("deal SmsReport from dp error cost:{} ms,userKey:{},jobId:{}", System.currentTimeMillis() - start, request.getUserKey(), jobId);
        }catch (Exception e){
            LOGGER.error("SyncSmsRecordServiceAdapter",e);
        }

        return buildNoDataResponse(request.getRequestId());
    }

    public String getSmsReportJobIdFromDataCenter(String userKey, String systemId) {
        DcRequest request = buildParams(userKey, systemId);
        String response = SyncHTTPRemoteAPI.postJson(dcInsideUrl + QUERY_URL, JsonUtils.toJson(request), TIMEOUT_MILLIS);
        return resolveResponse(response);
    }

    private DcRequest buildParams(String userKey, String systemId) {
        ArrayList<DcRequestService> services = Lists.newArrayList();
        DcRequestService serviceReq = new DcRequestService();
        serviceReq.setServiceCode("SMS_REPORT");
        Map<String, Object> params = new HashMap<>();
        params.put("sourceSystem", systemId);
        params.put("userKey", userKey);
        serviceReq.setParams(params);
        services.add(serviceReq);
        DcRequest request = new DcRequest();
        request.setServices(services);
        request.setUserKey(userKey);
        return request;
    }

    private ServiceResponse responseFromCallDp(String jobId, ServiceRequest request) {
        String url = buildUrl(request);
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        String result = SyncHTTPRemoteAPI.get(url, diService.getServiceTimeout().intValue());
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        response.setOriginalStatus("DEFAULT");  //GET接口status为DEFAULT
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        String dataValue = "";
        DpRecordVo recordVo = JsonUtils.jsonToBean(result, DpRecordVo.class);
        if (CollectionUtils.isNotEmpty(recordVo.getRecords())) {
            dataValue = recordVo.getRecords().get(0).toJSONString();
        }

        resultMap.put("data", dataValue);
        response.setResult(resultMap);
        response.setData(dataValue);
        response.setJobId(jobId);
        return response;
    }

    private String buildUrl(ServiceRequest request) {
        StringBuilder url = new StringBuilder().append(diService.getServiceUrl());
        if (MapUtils.isEmpty(request.getParams())) {
            return url.toString();
        }
        url.append("?");
        for (Map.Entry<String, Object> entry : request.getParams().entrySet()) {
            url.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        return url.substring(0, url.length() - 1);
    }

    private String resolveResponse(String response) {
        if (StringUtils.isBlank(response)) {
            return null;
        }
        JSONObject responseJson = JSONObject.parseObject(response);
        String retCode = responseJson.getString("retCode");
        if (!RetCodeEnum.SUCCESS.equals(retCode)) {
            return null;
        }
        JSONObject resultJson = responseJson.getJSONObject("result");
        JSONArray servicesJson = resultJson.getJSONArray("services");
        if (servicesJson == null || servicesJson.size() == 0) {
            return null;
        }
        JSONObject serviceJson = servicesJson.getJSONObject(0);
        if (!RetCodeEnum.SUCCESS.equals(serviceJson.getString("retCode"))) {
            return null;
        }
        JSONObject dataResult = serviceJson.getJSONObject("result");
        if (dataResult == null) {
            return null;
        }
        String data = dataResult.getString("data");
        if (StringUtils.isBlank(data)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(data);
        String jobId = jsonObject.getString("jobId");
        return StringUtils.isBlank(jobId) ? null : jobId;
    }

    private ServiceResponse buildNoDataResponse(String requestId) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(requestId);
        response.setRetCode(RetCodeEnum.NO_DATA.getValue());
        response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
        return response;
    }

    /**
     * 借款前数据源没数据赋值默认值
     * @param requestId
     * @return
     */
    private ServiceResponse buildDefaultDataResponse(String requestId) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(requestId);
        //GET接口status为DEFAULT
        response.setOriginalStatus("DEFAULT");
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        String dataValue = "{}";
        resultMap.put("data", dataValue);
        response.setResult(resultMap);
        response.setData(dataValue);
        return response;
    }
}
