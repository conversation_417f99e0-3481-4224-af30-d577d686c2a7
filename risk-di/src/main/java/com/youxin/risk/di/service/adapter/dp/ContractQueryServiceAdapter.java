package com.youxin.risk.di.service.adapter.dp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.di.mapper.haohuan.HaohuanMapper;
import com.youxin.risk.di.service.contact.ConactQueryService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class ContractQueryServiceAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ContractQueryServiceAdapter.class);

    private ConactQueryService conactQueryService = (ConactQueryService) ContextUtil.getBean("conactQueryService");

    private HaohuanMapper haohuanMapper = (HaohuanMapper) ContextUtil.getBean("haohuanMapper");

    @Override
    protected ServiceResponse callService(ServiceRequest request) {

        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        List<DcSubmitContactInfo> contractInfos = conactQueryService.queryByUserKey(request.getParams());
        if (contractInfos.isEmpty()) {
            LoggerProxy.error("ContractQueryServiceAdapter contractInfo is empty", LOGGER, "service response status error, request={}", JacksonUtil.toJson(request));
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        // 有些紧急联系人只有一个
        if (contractInfos.size() < 2) {
            LoggerProxy.warn("ContractQueryServiceAdapter contractInfo size error", LOGGER, "service response status error, request={}", JacksonUtil.toJson(request));
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        request.getParams().put("mobile1", getMobile(contractInfos.get(0).getMobile()));
        request.getParams().put("mobile2", getMobile(contractInfos.get(1).getMobile()));
        // 兼容uid没值的情况
        try {
            String userKey = (String) request.getParams().get("userKey");
            if (StringUtils.isBlank((String) request.getParams().get("uid")) && StringUtils.isNotBlank(userKey)) {
                String uid = haohuanMapper.getUidByUserKey(userKey);
                if (StringUtils.isNotBlank(uid)) {
                    request.getParams().put("uid", uid);
                }
            }
        } catch (Exception ex) {
            LoggerProxy.error("ContractQueryServiceAdapter.getUid", LOGGER, "getUid error, request={}", JacksonUtil.toJson(request), ex);
        }
        String url = generatorUrl(request);
        String result = SyncHTTPRemoteAPI.get(url, diService.getServiceTimeout().intValue());
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("ContractQueryServiceAdapter.serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        JSONObject dataResult = JSON.parseObject(result);
        JSONArray records = dataResult.getJSONArray("records");
        if (records == null || records.isEmpty()) {
            LoggerProxy.error("ContractQueryServiceAdapter.serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        response.setOriginalStatus("DEFAULT");  //GET接口status为DEFAULT
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("data", records.getJSONObject(0));
        response.setResult(resultMap);
        response.setData(records.getJSONObject(0).toJSONString());
        return response;
    }

    private String getMobile(String mobile) {
        return StringUtils.isNotBlank(mobile) && CaesarUtil.isEncrypted(mobile) ? CaesarUtil.decode(mobile) : mobile;
    }

}
