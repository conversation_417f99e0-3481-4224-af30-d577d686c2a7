package com.youxin.risk.di.service.adapter.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.UploadTimeJudgeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.youxin.risk.commons.constants.DiConstant.NEED_LATEST_UPLOAD_DATA;
import static com.youxin.risk.commons.constants.DiConstant.UPLOAD_TIME;
import static com.youxin.risk.commons.utils.DateUtil.TIME_MILLIS;
import static com.youxin.risk.commons.utils.DateUtil.parsePlus;
import static com.youxin.risk.commons.utils.UploadTimeJudgeUtil.isNeedLatestUploadData;

public class LabelServiceAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(LabelServiceAdapter.class);

    private static final String CAPTURE_TIME = "capture_time";

    private static final String LABEL_OUTPUT_TIME = "label_output_time";

    private static final String ERROR_CODE = "500";

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());

        String loanKey = request.getLoanKey();
        String userKey = request.getUserKey();
        String step = request.getStep();
        String eventCode = request.getSystemEventCode();

        String serviceCode = request.getServiceCode();
        LoggerProxy.info("LabelServiceAdapter_callService", LOGGER,
                "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} url = {}",loanKey,eventCode,step,userKey,serviceCode,diService.getServiceUrl());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loanKey",loanKey);
        jsonObject.put("eventCode",eventCode);
        jsonObject.put("step",step);
        jsonObject.put("userKey",userKey);
        jsonObject.put("serviceCode", serviceCode);
        jsonObject.put("registerAccount","risk");

        String result = SyncHTTPRemoteAPI.postJson(diService.getServiceUrl(), JSON.toJSONString(jsonObject), diService.getServiceTimeout().intValue());
        if (ERROR_CODE.equals(result)){
            /** 标签数据源不可以用时候,返回错误 **/
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        if (StringUtils.isBlank(result)){
            LoggerProxy.info("LabelServiceAdapter_callService", LOGGER,
                    "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} is blank",loanKey,eventCode,step,userKey,serviceCode);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }else {

            LoggerProxy.info("LabelServiceAdapter_callService", LOGGER,
                    "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {}  is not blank"
                    ,loanKey,eventCode,step,userKey,serviceCode);
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("data", result);
            response.setResult(resultMap);
            response.setData(result);
        }
        return response;
    }

    @Override
    protected  boolean isInUploadTime(ServiceRequest request,ServiceResponse response){
        try {
            if(!isNeedLatestUploadData(request)){
                return true;
            }

            Date uploadTime=parsePlus(String.valueOf(request.getParams().get(UPLOAD_TIME)),TIME_MILLIS);
            LoggerProxy.info("LabelServiceAdapter_callService_isInUploadTime",LOGGER, "loanKey={} uploadTime ={}" ,request.getLoanKey(),uploadTime);

            JSONArray array = JSONObject.parseArray(response.getData());
            if (CollectionUtils.isEmpty(array)){
                return false;
            }
            Date captureTime = null;
            for (int i = 0 ; i<array.size(); i++){
                JSONObject jsonObject = array.getJSONObject(i);
                String labelName = jsonObject.getString("label_name");
                String labelValue = jsonObject.getString("label_value");
                if (CAPTURE_TIME.equals(labelName) && !StringUtils.isBlank(labelValue)){
                    captureTime = new Date(Long.valueOf(labelValue));
                    break;
                }
            }

            /** 是否是最新的数据 **/
            boolean flag = Objects.isNull(captureTime) || captureTime.compareTo(uploadTime) < 0 ? false : true;

            LoggerProxy.info("LabelServiceAdapter_callService_isInUploadTime",LOGGER, "loanKey={} uploadTime ={},captureTime = {} flag = {}" ,request.getLoanKey(),uploadTime,captureTime,flag);

            return flag;
        }catch (Exception e){
            LoggerProxy.error("LabelServiceAdapter_callService_isInUploadTime_error",LOGGER,"loanKey={} ",request.getLoanKey(),e);
        }
        return false;
    }
}
