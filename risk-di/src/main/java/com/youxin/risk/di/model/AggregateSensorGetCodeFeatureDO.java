package com.youxin.risk.di.model;

import java.io.Serializable;

/**
 * 神策GetCode类型 聚合特征
 * <AUTHOR>
 * @since 2022/2/28 19:06
 */
public class AggregateSensorGetCodeFeatureDO implements Serializable {
    private String event;
    private String distinctId;
    private String ostype;
    private String osversion;
    private String city;
    private String province;
    private String country;
    private Integer rebindingcardCnt;
    private Integer repayCnt;
    private Integer meslogCnt;
    private Integer entryBindingcardCnt;
    private Integer supermember;
    private Integer phonenomodifyCnt;
    private Integer marketDownpayCnt;
    private Integer bindingcardCnt;
    private Integer registerCnt;
    private Integer rightspuechaseCnt;
    private Integer findingPasswordCnt;
    private Integer cashCnt;
    private Integer savemoneyCnt;
    private Integer beforedownVerifyCnt;
    private Integer forenoonVerifyCnt;
    private Integer afternoonVerifyCnt;
    private Integer eveningVerifyCnt;
    private String beforedownService;
    private String forenoonService;
    private String afternoonService;
    private String eveningService;

    private String latestTime;
    private String latestRebindingcardTime;
    private String latestRepayTime;
    private String latestMeslogTime;
    private String latestEntryTime;
    private String latestSupermemberTime;
    private String latestPhonenomodifyTime;
    private String latestMarketTime;
    private String latestBindingcardTime;
    private String latestRegisterTime;
    private String latestRightspuechaseTime;
    private String latestFindingTime;
    private String latestCashTime;
    private String latestSavemoneyTime;
    private String time;
    private String createTime;
    private String updateTime;

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getDistinctId() {
        return distinctId;
    }

    public void setDistinctId(String distinctId) {
        this.distinctId = distinctId;
    }


    public Integer getSupermember() {
        return supermember;
    }

    public void setSupermember(Integer supermember) {
        this.supermember = supermember;
    }

    public String getOstype() {
        return ostype;
    }

    public void setOstype(String ostype) {
        this.ostype = ostype;
    }

    public String getOsversion() {
        return osversion;
    }

    public void setOsversion(String osversion) {
        this.osversion = osversion;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getRebindingcardCnt() {
        return rebindingcardCnt;
    }

    public void setRebindingcardCnt(Integer rebindingcardCnt) {
        this.rebindingcardCnt = rebindingcardCnt;
    }

    public Integer getRepayCnt() {
        return repayCnt;
    }

    public void setRepayCnt(Integer repayCnt) {
        this.repayCnt = repayCnt;
    }

    public Integer getMeslogCnt() {
        return meslogCnt;
    }

    public void setMeslogCnt(Integer meslogCnt) {
        this.meslogCnt = meslogCnt;
    }

    public Integer getEntryBindingcardCnt() {
        return entryBindingcardCnt;
    }

    public void setEntryBindingcardCnt(Integer entryBindingcardCnt) {
        this.entryBindingcardCnt = entryBindingcardCnt;
    }

    public Integer getPhonenomodifyCnt() {
        return phonenomodifyCnt;
    }

    public void setPhonenomodifyCnt(Integer phonenomodifyCnt) {
        this.phonenomodifyCnt = phonenomodifyCnt;
    }

    public Integer getMarketDownpayCnt() {
        return marketDownpayCnt;
    }

    public void setMarketDownpayCnt(Integer marketDownpayCnt) {
        this.marketDownpayCnt = marketDownpayCnt;
    }

    public Integer getBindingcardCnt() {
        return bindingcardCnt;
    }

    public void setBindingcardCnt(Integer bindingcardCnt) {
        this.bindingcardCnt = bindingcardCnt;
    }

    public Integer getRegisterCnt() {
        return registerCnt;
    }

    public void setRegisterCnt(Integer registerCnt) {
        this.registerCnt = registerCnt;
    }

    public Integer getRightspuechaseCnt() {
        return rightspuechaseCnt;
    }

    public void setRightspuechaseCnt(Integer rightspuechaseCnt) {
        this.rightspuechaseCnt = rightspuechaseCnt;
    }

    public Integer getFindingPasswordCnt() {
        return findingPasswordCnt;
    }

    public void setFindingPasswordCnt(Integer findingPasswordCnt) {
        this.findingPasswordCnt = findingPasswordCnt;
    }

    public Integer getCashCnt() {
        return cashCnt;
    }

    public void setCashCnt(Integer cashCnt) {
        this.cashCnt = cashCnt;
    }

    public Integer getSavemoneyCnt() {
        return savemoneyCnt;
    }

    public void setSavemoneyCnt(Integer savemoneyCnt) {
        this.savemoneyCnt = savemoneyCnt;
    }

    public Integer getBeforedownVerifyCnt() {
        return beforedownVerifyCnt;
    }

    public void setBeforedownVerifyCnt(Integer beforedownVerifyCnt) {
        this.beforedownVerifyCnt = beforedownVerifyCnt;
    }

    public Integer getForenoonVerifyCnt() {
        return forenoonVerifyCnt;
    }

    public void setForenoonVerifyCnt(Integer forenoonVerifyCnt) {
        this.forenoonVerifyCnt = forenoonVerifyCnt;
    }

    public Integer getAfternoonVerifyCnt() {
        return afternoonVerifyCnt;
    }

    public void setAfternoonVerifyCnt(Integer afternoonVerifyCnt) {
        this.afternoonVerifyCnt = afternoonVerifyCnt;
    }

    public Integer getEveningVerifyCnt() {
        return eveningVerifyCnt;
    }

    public void setEveningVerifyCnt(Integer eveningVerifyCnt) {
        this.eveningVerifyCnt = eveningVerifyCnt;
    }

    public String getBeforedownService() {
        return beforedownService;
    }

    public void setBeforedownService(String beforedownService) {
        this.beforedownService = beforedownService;
    }

    public String getForenoonService() {
        return forenoonService;
    }

    public void setForenoonService(String forenoonService) {
        this.forenoonService = forenoonService;
    }

    public String getAfternoonService() {
        return afternoonService;
    }

    public void setAfternoonService(String afternoonService) {
        this.afternoonService = afternoonService;
    }

    public String getEveningService() {
        return eveningService;
    }

    public void setEveningService(String eveningService) {
        this.eveningService = eveningService;
    }

    public String getLatestTime() {
        return latestTime;
    }

    public void setLatestTime(String latestTime) {
        this.latestTime = latestTime;
    }

    public String getLatestRebindingcardTime() {
        return latestRebindingcardTime;
    }

    public void setLatestRebindingcardTime(String latestRebindingcardTime) {
        this.latestRebindingcardTime = latestRebindingcardTime;
    }

    public String getLatestRepayTime() {
        return latestRepayTime;
    }

    public void setLatestRepayTime(String latestRepayTime) {
        this.latestRepayTime = latestRepayTime;
    }

    public String getLatestMeslogTime() {
        return latestMeslogTime;
    }

    public void setLatestMeslogTime(String latestMeslogTime) {
        this.latestMeslogTime = latestMeslogTime;
    }

    public String getLatestEntryTime() {
        return latestEntryTime;
    }

    public void setLatestEntryTime(String latestEntryTime) {
        this.latestEntryTime = latestEntryTime;
    }

    public String getLatestSupermemberTime() {
        return latestSupermemberTime;
    }

    public void setLatestSupermemberTime(String latestSupermemberTime) {
        this.latestSupermemberTime = latestSupermemberTime;
    }

    public String getLatestPhonenomodifyTime() {
        return latestPhonenomodifyTime;
    }

    public void setLatestPhonenomodifyTime(String latestPhonenomodifyTime) {
        this.latestPhonenomodifyTime = latestPhonenomodifyTime;
    }

    public String getLatestMarketTime() {
        return latestMarketTime;
    }

    public void setLatestMarketTime(String latestMarketTime) {
        this.latestMarketTime = latestMarketTime;
    }

    public String getLatestBindingcardTime() {
        return latestBindingcardTime;
    }

    public void setLatestBindingcardTime(String latestBindingcardTime) {
        this.latestBindingcardTime = latestBindingcardTime;
    }

    public String getLatestRegisterTime() {
        return latestRegisterTime;
    }

    public void setLatestRegisterTime(String latestRegisterTime) {
        this.latestRegisterTime = latestRegisterTime;
    }

    public String getLatestRightspuechaseTime() {
        return latestRightspuechaseTime;
    }

    public void setLatestRightspuechaseTime(String latestRightspuechaseTime) {
        this.latestRightspuechaseTime = latestRightspuechaseTime;
    }

    public String getLatestFindingTime() {
        return latestFindingTime;
    }

    public void setLatestFindingTime(String latestFindingTime) {
        this.latestFindingTime = latestFindingTime;
    }

    public String getLatestCashTime() {
        return latestCashTime;
    }

    public void setLatestCashTime(String latestCashTime) {
        this.latestCashTime = latestCashTime;
    }

    public String getLatestSavemoneyTime() {
        return latestSavemoneyTime;
    }

    public void setLatestSavemoneyTime(String latestSavemoneyTime) {
        this.latestSavemoneyTime = latestSavemoneyTime;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
