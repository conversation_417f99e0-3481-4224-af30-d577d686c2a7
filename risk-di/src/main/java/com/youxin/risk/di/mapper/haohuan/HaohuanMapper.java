package com.youxin.risk.di.mapper.haohuan;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface HaohuanMapper {

    String getUidByUserKey(String businessUserKey);
    String getUidByMobile(String mobile);

    String getUidByMobiles(@Param("mobiles") List<String> mobiles);

    String getUserKeyByUid(String uid);
    List<Map<String,String>> getUserKeyByUidList(@Param("uidList")List<String> uidList);
    List<Map<String, String>> queryNotSuccLoanCountForUser(@Param("userKeyList")List<String> userKeyList);
    List<Map<String, Object>> querySuccLoanCountForUser(@Param("userKeyList")List<String> userKeyList);
    List<Map<String, Object>> querySuccLoanCountForUserWithOutBaoGuoCard(@Param("userKeyList")List<String> userKeyList);

    List<Map<String, Object>> getTemplateLog(String businessUserKey);
    List<Map<String, Object>> getTemplateLogWithOutBaoGuoCard(String businessUserKey);
    List<Map<String, Object>> getUserLevel(String businessUserKey);
    List<Map<String, Object>> getDailyActiveUser(String businessUserKey);
    Integer lst1m_login_daycnt_da_inst(String businessUserKey);
    Integer lst3m_login_daycnt_da_inst(String businessUserKey);
    Integer lst6m_login_daycnt_da_inst(String businessUserKey);
    Integer lst1m_login_daycnt_da_tm1(String businessUserKey);
    Integer lst3m_login_daycnt_da_tm1(String businessUserKey);
    Integer lst6m_login_daycnt_da_tm1(String businessUserKey);
    Integer login_device_count(String businessUserKey);

    Map<String, Object> getLineDataFromTemplateLog(Integer loanId);
    Map<String, Object> getLineDataFromTemplateLogWithOutBaoGuoCard(Integer loanId);

    List<Map<String, Object>> getThreeYearsDailyActiveUser(String businessUserKey);

    //查询紧急联系人
    List<Map<String, Object>> queryEmergencyContactList(String uid);

    //按参数扫所有用户
    List<Map<String, Object>> queryUserList(@Param("lastUserKey")String lastUserKey, @Param("queryNum")String queryNum);
}
