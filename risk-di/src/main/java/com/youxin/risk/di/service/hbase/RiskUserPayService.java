package com.youxin.risk.di.service.hbase;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MD5Util;
import com.youxin.risk.commons.utils.MapUtils;
import com.youxin.risk.di.mapper.accountProxy.AccountProxyMapper;
import com.youxin.risk.di.mapper.analysis.AnalysisMapper;
import com.youxin.risk.di.model.PayMessage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.youxin.risk.di.common.Constant.*;

/**
 * <AUTHOR>
 * @date 2022/2/16
 * @desc 从hbase库取还款计划表数据，代替从MySQL表取数据的业务逻辑类AccountProxyMapper的对应方法
 */
@Service
public class RiskUserPayService {

    private static final Logger logger = LoggerFactory.getLogger(RiskUserPayService.class);

    private static final String RESPONSE_STATUS_SUCCESS_000000 = "000000";

    @Autowired
    private AccountProxyMapper accountProxyMapper;
    @Autowired
    private HbaseService hbaseService;
    @Autowired
    AnalysisMapper analysisMapper;

    private final String[] payMessages = new String[]{"产品不支持部分还款，申请还款金额与实际应还金额不符", "借款已还清，无需还款",
            "后台系统正在进行清算，请耐心等待, 同步处理, 不需要回调业务", "在申请还款的期次之前有未还期次，不允许还款",
            "已逾期，不允许提前还款", "当前已进入最后一期的还款周期，不允许提前还款", "总还款金额不能小于等于零",
            "支付失败，原因，订单管理器：系统出错", "普通任务,执行时间已过期", "普通还款还款项不能为空", "未找到合适的通道",
            "校验存在债转前还款失败", "汇款还款入账驳回", "申请还款的期次已还清，不可重复还款", "申请还款金额与实际应还金额不一致",
            "申请还款金额超出实际应还金额:0", "系统内部错误", "系统异常", "系统异常，请稍后重试", "系统正在清算",
            "系统繁忙，请稍后重试或换卡支付", "系统维护中，请稍后", "该卡已被系统限制，请联系银联手机支付客服", "请求账务还款检查失败",
            "逾期还款计划的最近清算时间不存在", "逾期还款计划的清算时间不是当天，不允许还款", "银行系统异常", "银行系统异常，请稍后重试",
            "银行系统繁忙，请等异步通知或发起查询确认订单状态", "银行系统鉴权失败"};
    private final List<String> payMessageList = Arrays.asList(payMessages);

    public List<Map<String, Object>> getHBaseByUserKey(String userKey, Integer partnerId, int days, String methodName) {
        if (StringUtils.isBlank(userKey)) {
            logger.error("getHBaseByUserKey userKey is blank");
            throw new RuntimeException("获取user_pay,user_key不能为空");
        }
        return mergeHbaseMysqlResults(methodName, userKey, partnerId, days);
    }

    /**
     * 从账务获取user_pay的数据
     *
     * @param userKey
     * @param partnerId
     * @throws Exception
     */
    public List<Map<String, Object>> getUserPayFromAccount(String userKey, Integer partnerId, long startDateTime) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        Map<String, Object> params = new HashMap<>();
        params.put("partner", partnerId.equals(3) ? "RRD" : "HAOHUAN");
        params.put("partnerUserId", userKey);
        params.put("serviceVersion", "1.0");
        params.put("requestTime", System.currentTimeMillis());
        params.put("startDate", sdf.format(startDateTime));
        params.put("endDate", sdf.format(System.currentTimeMillis()));

        Properties property = (Properties) ContextUtil.getBean("configProperties");
        String salt = partnerId.equals(3) ? property.getProperty("account.salt.RRD") : property.getProperty("account.salt.haohuan");
        String getPayBaseInfoUrl = property.getProperty("account.getPayBaseInfo.url");
        String sign = getSign(MapUtils.objectToMapString(params), salt);
        params.put("sign", sign);
        long start = System.currentTimeMillis();
        String result = SyncHTTPRemoteAPI.post(String.format("%s%s", getPayBaseInfoUrl, "/account/loan/getPayBaseInfo"), MapUtils.objectToMapString(params), 30000);
        logger.info("getUserPayFromAccount cost: {}, params: {}", System.currentTimeMillis() - start, JSONUtils.toJSONString(params));
        if (StringUtils.isBlank(result)) {
            logger.error("getUserPayFromAccount result is blank, request={}, result={}", JSONUtils.toJSONString(params), result);
            throw new Exception("调用账务获取user_pay,响应为空");
        }

        JSONObject resultJson = JSONObject.parseObject(result);
        if (!RESPONSE_STATUS_SUCCESS_000000.equals(resultJson.getString("status"))) {
            logger.error("getUserPayFromAccount status error, request={}, result={}", JSONUtils.toJSONString(params), result);
            throw new Exception("调用账务获取user_pay状态异常");
        }
        List<Map<String, Object>> results = Lists.newArrayList();
        JSONObject data = resultJson.getJSONObject("data");
        if (data == null) {
            logger.error("getUserPayFromAccount status error, request={}, result={}", JSONUtils.toJSONString(params), result);
            throw new Exception("调用账务获取user_pay,data为空异常");
        }
        JSONArray userPays = data.getJSONArray("userpays");
        if (CollectionUtils.isEmpty(userPays)) {
            return results;
        }
        userPays.forEach(userPay -> {
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("id", ((JSONObject) userPay).getLongValue("id"));
            resultMap.put("account_report_status", ((JSONObject) userPay).getInteger("accountReportStatus"));
            resultMap.put("pay_launch_type", ((JSONObject) userPay).getInteger("payLaunchType"));
            resultMap.put("pay_message", ((JSONObject) userPay).getString("payMessage"));
            resultMap.put("amount", ((JSONObject) userPay).getString("amount"));
            resultMap.put("create_time", ((JSONObject) userPay).getLong("createTime"));
            resultMap.put("update_time", ((JSONObject) userPay).getLong("updateTime"));
            resultMap.put("user_id", ((JSONObject) userPay).getLong("userId"));
            results.add(resultMap);
        });
        return results;
    }

    /**
     * 合并hbase和mysql的数据返回
     *
     * @param methodName
     * @param userKey
     * @param partnerId
     * @param days
     * @return
     */
    private List<Map<String, Object>> mergeHbaseMysqlResults(String methodName, String userKey, Integer partnerId, int days) {
        String compareConfig = ApolloClientAdapter.getStringConfig(ApolloNamespaceEnum.DI_SPACE, "compareWithMysql.differ", "{\"ltLog\": false,\"gteLog\": false,\"diffLog\": false,\"mergeDataLog\": false,\"hbaseDataLog\": false,\"days\":2}");
        final JSONObject jsonObject = JSONObject.parseObject(compareConfig);
        final Boolean printLtLog = jsonObject.getBoolean("ltLog");
        final Boolean printGteLog = jsonObject.getBoolean("gteLog");
        final Boolean diffLog = jsonObject.getBoolean("diffLog");
        final Boolean mergeDataLog = jsonObject.getBoolean("mergeDataLog");
        final Boolean hbaseDataLog = jsonObject.getBoolean("hbaseDataLog");
        final Integer minusDays = jsonObject.getInteger("days");
        long mysqlStartDateTime = LocalDateTime.of(LocalDateTime.now().minusDays(minusDays).toLocalDate(), LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8")) * 1000;
        // 调用账务接口获取近60天的数据
        List<Map<String, Object>> mysqlResult;
        try {
            mysqlResult = getUserPayFromAccount(userKey, partnerId, mysqlStartDateTime);
        } catch (Exception ex) {
            logger.error("getUserPayFromAccount error, methodName: {}, userKey: {}, partnerId: {}", methodName, userKey, partnerId, ex);
            throw new RuntimeException("调用账务获取user_pay异常", ex);
        }

        List<Map<String, Object>> hbaseResult = Lists.newArrayList();
        // days为0，需要根据user_key查询所有数据
        boolean slaveFlag = false;
        boolean slaveAllFlag = false;
        try {
            slaveFlag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.COMMON_SPACE, "userpay.slave.flag", false);
            slaveAllFlag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.RISK_ALL_SPACE, "hbase.slave.flag", false);
        } catch (Exception ex) {
            LoggerProxy.error("RiskUserPayServiceError", logger, "获取userpay.slave.flag配置异常", ex);
        }

        if (days == 0) {
            RiskUserPayHB riskUserPayHB = new RiskUserPayHB(userKey);

            try {
                if (slaveFlag || slaveAllFlag) {
                    LoggerProxy.info("RiskUserPayService", logger, "scanSlaveByRowKeyPrefix riskUserPayHB={} ", JSON.toJSONString(riskUserPayHB));
                    hbaseService.scanSlaveByRowKeyPrefix(riskUserPayHB);
                } else {
                    hbaseService.scanByRowKeyPrefix(riskUserPayHB);
                }
            } catch (Exception ex) {
                LoggerProxy.error("RiskUserPayServiceError", logger, "getHBaseByUserKey error获取备份集群错误 riskUserPayHB:{}",JSON.toJSONString(riskUserPayHB), ex);
            }

            hbaseResult = riskUserPayHB.getResultList();
            // startRow+stopRow查询
        } else if (days >= minusDays) {
            // 开始时间戳
            long startTime = LocalDateTime.of(LocalDateTime.now().minusDays(days).toLocalDate(), LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8")) * 1000;
            String startRow = String.format("%s%s", userKey, startTime);
            // 结束时间戳
            long stopTime = LocalDateTime.of(LocalDateTime.now().minusDays(minusDays - 1).toLocalDate(), LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8")) * 1000;
            String stopRow = String.format("%s%s", userKey, stopTime);
            RiskUserPayHB riskUserPayHB = new RiskUserPayHB(startRow, stopRow);
            try {
                if (slaveFlag || slaveAllFlag) {
                    LoggerProxy.info("RiskUserPayService", logger, "scanSlaveWithStartRowAndStopRow riskUserPayHB={} ", JSON.toJSONString(riskUserPayHB));
                    hbaseService.scanSlaveWithStartRowAndStopRow(riskUserPayHB);
                } else {
                    hbaseService.scanWithStartRowAndStopRow(riskUserPayHB);
                }
            } catch (Exception ex) {
                LoggerProxy.error("RiskUserPayServiceError", logger, "getHBaseByUserKey error获取备份集群错误 riskUserPayHB:{}",JSON.toJSONString(riskUserPayHB), ex);
            }
            hbaseResult = riskUserPayHB.getResultList();
            if (hbaseDataLog && hbaseResult != null && hbaseResult.size() > 0) {
                logger.info("query hbase, methodName: {}, userKey: {}, partnerId: {}, startRow: {}, stopRow: {}, size: {}, hbaseData: {}", methodName, userKey, partnerId, startRow, stopRow, hbaseResult == null ? 0 : hbaseResult.size(), JSONUtils.toJSONString(hbaseResult));
            }
        }
        Map<Object, Map<String, Object>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hbaseResult)) {
            for (Map<String, Object> objectMap : hbaseResult) {
                String idStr = objectMap.get("id") == null ? "" : objectMap.get("id").toString();
                if (StringUtils.isBlank(idStr)) {
                    continue;
                }
                // updateTime大的做替换
                final Map<String, Object> existHbaseMap = map.get(Long.parseLong(idStr));
                if (existHbaseMap != null) {
                    String oldUpdateTime = existHbaseMap.get("update_time") == null ? "" : existHbaseMap.get("update_time").toString();
                    String newUpdateTime = objectMap.get("update_time") == null ? "" : objectMap.get("update_time").toString();
                    if (newUpdateTime.compareTo(oldUpdateTime) > 0) {
                        map.put(Long.parseLong(idStr), objectMap);
                    }
                } else {
                    map.put(Long.parseLong(idStr), objectMap);
                }
            }
        }

        List<Map<String, Object>> notInHbaseLtList = Lists.newArrayList();
        List<Map<String, Object>> notInHbaseGteList = Lists.newArrayList();
        List<Map<String, Map<String, Object>>> diffList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(mysqlResult)) {
            for (Map<String, Object> mysqlObjectMap : mysqlResult) {
                String idStr = mysqlObjectMap.get("id") == null ? "" : mysqlObjectMap.get("id").toString();
                if (StringUtils.isBlank(idStr)) {
                    continue;
                }
                final Object mysqlCreateTimeObj = mysqlObjectMap.get("create_time");
                // hbase数据未获取到
                final Map<String, Object> hbaseObjectMap = map.get(Long.parseLong(idStr));
                if (hbaseObjectMap == null) {
                    long createTimeLong = 0L;
                    if (mysqlCreateTimeObj != null) {
                        String createTimeStr = mysqlCreateTimeObj.toString();
                        // mysql 获取的时间，类似：2021-10-27 17:49:02.0
                        if (createTimeStr.contains("-")) {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            try {
                                Date date = simpleDateFormat.parse(createTimeStr);
                                createTimeLong = date.getTime();
                            } catch (ParseException e) {
                                logger.info("dealWithTimeError, userKey: {}, partnerId: {}", userKey, e);
                            }
                        } else {
                            createTimeLong = Long.parseLong(createTimeStr);
                        }

                        if (createTimeLong < mysqlStartDateTime) {
                            notInHbaseLtList.add(mysqlObjectMap);
                        } else {
                            notInHbaseGteList.add(mysqlObjectMap);
                        }
                    }
                    map.put(Long.parseLong(idStr), mysqlObjectMap);
                } else {
                    // 存在的数据进行比较
                    final Map<String, Object> hbaseNewMap = convertToNewMap(hbaseObjectMap);
                    final Map<String, Object> mysqlNewMap = convertToNewMap(mysqlObjectMap);
                    final MapDifference<String, Object> diff = MapUtils.compareWithFlatten(mysqlNewMap, hbaseNewMap, true);
                    if (!(diff.entriesDiffering().isEmpty() && diff.entriesOnlyOnLeft().isEmpty() && diff.entriesOnlyOnRight().isEmpty())) {
                        Map<String, Map<String, Object>> diffMap = new HashMap(2);
                        diffMap.put("hbaseNewMap", hbaseNewMap);
                        diffMap.put("mysqlNewMap", mysqlNewMap);
                        diffList.add(diffMap);
                    }
                    // 存在的数据，如果时间大于mysqlStartDateTime，就取mysql的数据
                    if (mysqlCreateTimeObj != null) {
                        long createTimeLong = Long.parseLong(mysqlCreateTimeObj.toString());
                        if (createTimeLong >= mysqlStartDateTime) {
                            map.put(Long.parseLong(idStr), mysqlObjectMap);
                        }
                    }
                }
            }
        }
        if (diffLog && diffList.size() > 0) {
            logger.info("diffList, methodName: {}, userKey: {}, partnerId: {}, diffList: {}", methodName, userKey, partnerId, JSONUtils.toJSONString(diffList));
        }
        if (printLtLog && notInHbaseLtList.size() > 0) {
            logger.info("notInHbaseLtList, methodName: {}, userKey: {}, partnerId: {}, notInHbaseLtList: {}", methodName, userKey, partnerId, JSONUtils.toJSONString(notInHbaseLtList));
        }
        if (printGteLog && notInHbaseGteList.size() > 0) {
            logger.info("notInHbaseGteList, methodName: {}, userKey: {}, partnerId: {}, notInHbaseGteList: {}", methodName, userKey, partnerId, JSONUtils.toJSONString(notInHbaseGteList));
        }
        // 根据id大小排序
        Map<Object, Map<String, Object>> sortMap = new TreeMap<>(Comparator.comparing(o -> (long) o));
        sortMap.putAll(map);
        // 组装需要的字段
        List<Map<String, Object>> result = dealWithData(new ArrayList<>(sortMap.values()));
        if (mergeDataLog) {
            logger.info("mergeData, methodName: {}, userKey: {}, partnerId: {}, mergeData: {}", methodName, userKey, partnerId, JSONUtils.toJSONString(result));
        }
        return result;
    }

    private List<Map<String, Object>> dealWithData(List<Map<String, Object>> list) {
        List<Map<String, Object>> newList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> newMap = convertToNewMap(map);
            newList.add(newMap);
        }
        return newList;
    }

    private Map<String, Object> convertToNewMap(Map<String, Object> map) {
        Map<String, Object> newMap = new HashMap<>();
        newMap.put("id", Long.parseLong(map.get("id").toString()));
        Integer accountReportStatus = map.get("account_report_status") == null ? null : Integer.parseInt(map.get("account_report_status").toString());
        newMap.put("account_report_status", accountReportStatus);
        Integer payLaunchType = map.get("pay_launch_type") == null ? null : Integer.parseInt(map.get("pay_launch_type").toString());
        newMap.put("pay_launch_type", payLaunchType);
        newMap.put("pay_message", map.get("pay_message"));
        newMap.put("amount", map.get("amount"));
        newMap.put("create_time", map.get("create_time"));
        if (map.get("create_time") != null) {
            String createTime = map.get("create_time").toString();
            if (createTime.contains("-")) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date date = simpleDateFormat.parse(createTime);
                    newMap.put("create_time", String.valueOf(date.getTime()));
                } catch (ParseException e) {
                    LoggerProxy.error("dealWithTimeError", logger, "dealWithTimeError", e);
                }
            }
        }
        newMap.put("update_time", map.get("update_time"));
        if (map.get("update_time") != null) {
            String updateTime = map.get("update_time").toString();
            if (updateTime.contains("-")) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date date = simpleDateFormat.parse(updateTime);
                    newMap.put("update_time", String.valueOf(date.getTime()));
                } catch (ParseException e) {
                    LoggerProxy.error("dealWithTimeError", logger, "dealWithTimeError", e);
                }
            }
        }
        return newMap;
    }

    /**
     * 条件过滤
     *
     * @param result                该用户所有明细
     * @param days                  天数
     * @param beforeNow             是否小于当前日期
     * @param account_report_status 账务系统上报状态 -1:处理失败, 0:待处理, 1:处理中, 2:处理成功
     * @param pay_launch_type       类型：0:自动划扣, 1:主动还款
     * @param payMessageNotIn       pay_message 不在
     * @param payMessageNotNull     pay_message 不为null
     * @param ltrimPayMessage       pay_message 去空格
     * @param containsPayMessage    pay_message 包含
     * @param limit                 限制
     * @return
     */
    private List<Map<String, Object>> filterByCondition(List<Map<String, Object>> result, Integer days, Boolean beforeNow, Integer account_report_status, Integer pay_launch_type
            , Boolean payMessageNotIn, Boolean payMessageNotNull, Boolean ltrimPayMessage, Boolean containsPayMessage, Integer limit) {
        if (result != null) {
            List<Map<String, Object>> list = new ArrayList<>();
            // 当前时间-days天后的毫秒时间戳
            long dayMark = LocalDateTime.now().minusDays(days).toEpochSecond(ZoneOffset.of("+8")) * 1000;
            // 当前时间零点的毫秒时间戳
            long nowDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8")) * 1000;
            for (Map<String, Object> map : result) {
                // create_time 是否大于days天前的时间，没送不需要限制
                boolean daysFlag = false;
                if (days != null) {
                    daysFlag = map.get("create_time") != null && Long.parseLong(map.get("create_time").toString()) > dayMark;
                } else {
                    daysFlag = true;
                }

                // create_time 是否小于当前日期，没送不需要限制
                boolean beforeNowFlag = false;
                if (beforeNow) {
                    beforeNowFlag = map.get("create_time") != null && Long.parseLong(map.get("create_time").toString()) < nowDate;
                } else {
                    beforeNowFlag = true;
                }

                // 账务系统上报状态，没送不限制
                boolean accountReportStatusFlag = false;
                if (account_report_status != null) {
                    Integer accountReportStatus = (Integer) map.get("account_report_status");
                    accountReportStatusFlag = Objects.equals(accountReportStatus, account_report_status);
                } else {
                    accountReportStatusFlag = true;
                }

                // 还款类型，不送没限制
                boolean payLaunchTypeFlag = false;
                if (pay_launch_type != null) {
                    Integer payLaunchType = (Integer) map.get("pay_launch_type");
                    payLaunchTypeFlag = Objects.equals(payLaunchType, pay_launch_type);
                } else {
                    payLaunchTypeFlag = true;
                }

                String payMessage = map.get("pay_message") == null ? null : map.get("pay_message").toString();

                // pay_message 不在指定类型里面，false不限制; 注：not in 需要去除null值
                boolean payMessageNotInFlag = !payMessageNotIn || (payMessage != null && !payMessageList.contains(payMessage));

                // pay_message 不为空，false不限制
                boolean payMessageNotNullFlag = !payMessageNotNull || (payMessage != null);

                // pay_message去空格后是否有值
                boolean ltrimPayMessageFlag = !ltrimPayMessage || (StringUtils.isNotBlank(payMessage) && payMessage.trim().length() > 0);

                // pay_message是否包含：余额不足
                boolean containsPayMessageFlag = !containsPayMessage || (StringUtils.isNotBlank(payMessage) && payMessage.contains("余额不足"));

                if (daysFlag && beforeNowFlag && accountReportStatusFlag && payLaunchTypeFlag && payMessageNotInFlag && payMessageNotNullFlag && ltrimPayMessageFlag && containsPayMessageFlag)
                    list.add(map);
            }

            if (limit != null) {
                if (limit >= list.size()) {
                    return list;
                } else {
                    return list.subList(0, limit - 1);
                }
            } else {
                return list;
            }
        }
        return null;
    }

    private List<PayMessage> buildPayMessage(List<Map<String, Object>> result) {
        if (result != null) {
            List<PayMessage> list = new ArrayList<>();
            for (Map<String, Object> map : result) {
                PayMessage payMessage = new PayMessage();
                payMessage.setId(Long.parseLong(map.get("id").toString()));
                payMessage.setPayMessage(map.get("pay_message") == null ? null : map.get("pay_message").toString());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                payMessage.setCreateTime(map.get("create_time") == null ? null : sdf.format(new Date(Long.parseLong(map.get("create_time").toString()))));
                payMessage.setUpdateTime(map.get("update_time") == null ? null : sdf.format(new Date(Long.parseLong(map.get("update_time").toString()))));
                list.add(payMessage);
            }
            return list;
        }
        return null;
    }

    private Double buildSum(List<Map<String, Object>> result, Boolean pay_launch_type, Boolean account_report_status, Boolean pay_message) {
        double value = 0.0;
        if (result != null && result.size() > 0) {
            for (Map<String, Object> map : result) {
                boolean pay_launch_type_flag = false;
                if (pay_launch_type) {
                    if (map.get("pay_launch_type") == (Integer) 0)
                        pay_launch_type_flag = true;
                } else {
                    pay_launch_type_flag = true;
                }
                boolean account_report_status_flag = false;
                if (account_report_status) {
                    if (map.get("account_report_status") == (Integer) 2)
                        account_report_status_flag = true;
                } else {
                    account_report_status_flag = true;
                }
                boolean pay_message_flag = false;
                if (pay_message) {
                    // mysql rlike正则匹配余额不足,这里
                    if (map.get("pay_message") != null && map.get("pay_message").toString().contains("余额不足"))
                        pay_message_flag = true;
                } else {
                    pay_message_flag = true;
                }
                if (pay_launch_type_flag && account_report_status_flag && pay_message_flag)
                    value += Double.parseDouble(map.get("amount").toString());
            }
        }
        return result == null || result.size() == 0 ? null : value;
    }

    public Long buildCount(List<Map<String, Object>> result, Integer days, Boolean beforeNow, Integer account_report_status, Integer pay_launch_type
            , Boolean payMessageNotNull, Boolean ltrimPayMessage, Boolean containsPayMessage) {
        List<Map<String, Object>> list = filterByCondition(result, days, beforeNow, account_report_status, pay_launch_type, true, payMessageNotNull, ltrimPayMessage, containsPayMessage, null);
        return list == null ? 0 : (long) list.size();
    }

    public List<PayMessage> selectPayMessages(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "selectPayMessages");
        return buildPayMessage(filterByCondition(result, 180, false, null, null, true, true, false, false, 10000));
    }

    public List<PayMessage> selectInvalidPayMessages(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "selectInvalidPayMessages");
        return buildPayMessage(filterByCondition(result, 180, false, null, null, true, true, false, false, 10000));
    }

    public Long select6mChargeTotalCount(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "select6mChargeTotalCount");
        return buildCount(result, 180, false, null, null, true, true, false);
    }

    public Long select6mChargeTotalCountT1(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "select6mChargeTotalCountT1");
        return buildCount(result, 180, true, null, null, true, true, false);
    }

    public Long selectRrd3mChargeFailCount(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 3, 93, "selectRrd3mChargeFailCount");
        return buildCount(result, 93, true, null, null, false, false, true);
    }

    public List<PayMessage> selectSuccessPayMessages(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "selectSuccessPayMessages");
        return buildPayMessage(filterByCondition(result, 180, false, 2, null, true, false, false, false, 10000));
    }

    public List<PayMessage> selectSuccessInvalidPayMessages(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "selectSuccessInvalidPayMessages");
        return buildPayMessage(filterByCondition(result, 180, false, 2, null, null, false, false, false, 10000));
    }

    public Long select6mChargeSuccessCount(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "select6mChargeSuccessCount");
        return buildCount(result, 180, false, 2, null, true, true, false);
    }

    public Long select6mChargeSuccessCountT1(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 180, "select6mChargeSuccessCountT1");
        return buildCount(result, 180, true, 2, null, true, true, false);
    }

    public Double select3mChargeTotalAmount(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 90, "select3mChargeTotalAmount");
        List<Map<String, Object>> list = filterByCondition(result, 90, false, null, null, true, true, true, false, null);
        return buildSum(list, false, false, false);
    }

    public Double select3mChargeTotalAmountT1(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 90, "select3mChargeTotalAmountT1");
        List<Map<String, Object>> list = filterByCondition(result, 90, true, null, null, true, true, true, false, null);
        return buildSum(list, false, false, false);
    }

    public Double select3mChargeTotalFailAmount(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 90, "select3mChargeTotalFailAmount");
        List<Map<String, Object>> list = filterByCondition(result, 90, false, null, null, true, false, false, true, null);
        return buildSum(list, false, false, false);
    }

    public Double select3mChargeTotalFailAmountT1(String userKey) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, 90, "select3mChargeTotalFailAmountT1");
        List<Map<String, Object>> list = filterByCondition(result, 90, true, null, null, true, false, false, true, null);
        return buildSum(list, false, false, false);
    }

    public Double selectRepayAmount(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectRepayAmount");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, 1, true, true, true, false, null);
        return buildSum(list, false, false, false);
    }

    public Long selectSuccessRepayTimes(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectSuccessRepayTimes");
        return buildCount(result, days, false, 2, 1, true, true, false);
    }

    public Long selectTotalRepayTimes(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectTotalRepayTimes");
        return buildCount(result, days, false, null, null, true, true, false);
    }

    public Long selectTotalDeductionTimes(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectTotalDeductionTimes");
        return buildCount(result, days, false, null, null, true, true, false);
    }

    public Long selectSuccessDeductionTimes(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectSuccessDeductionTimes");
        return buildCount(result, days, false, 2, null, false, true, false);
    }

    public Double selectPayLaunchTypeHourAvg(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectPayLaunchTypeHourAvg");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, 1, true, true, true, false, null);
        double avg = 0.00;
        if (list != null && list.size() > 0) {
            double sum = 0.00;
            for (Map<String, Object> map : list) {
                long create_time = Long.parseLong(map.get("create_time").toString());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date(create_time));
                sum += calendar.get(Calendar.HOUR_OF_DAY);
            }
            double tmp = sum / list.size();
            avg = new BigDecimal(tmp).setScale(4, RoundingMode.HALF_UP).doubleValue();
        }
        return list == null || list.size() == 0 ? null : avg;
    }

    public Double selectNoBalanceAmountMax(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectNoBalanceAmountMax");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, null, true, true, true, true, null);
        TreeSet<Double> set = new TreeSet<>();
        if (list != null) {
            for (Map<String, Object> map : list) {
                Double amount = Double.parseDouble(map.get("amount").toString());
                DecimalFormat format = new DecimalFormat("#.00");
                set.add(Double.parseDouble(format.format(amount)));
            }
        }
        return list == null || list.size() == 0 ? 0.0 : set.last();
    }

    public Double selectPayLaunchTypeZeroAmountSum(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectPayLaunchTypeZeroAmountSum");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, null, true, true, true, false, null);
        return buildSum(list, true, false, false);
    }

    public Double selectPayLaunchTypeOneAmountMin(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectPayLaunchTypeOneAmountMin");
        List<Map<String, Object>> list = filterByCondition(result, 30, false, null, null, true, true, true, false, null);
        TreeSet<Double> set = new TreeSet<>();
        if (list != null) {
            for (Map<String, Object> map : list) {
                if (map.get("pay_launch_type") == (Integer) 1) {
                    Double amount = Double.parseDouble(map.get("amount").toString());
                    DecimalFormat format = new DecimalFormat("#.00");
                    set.add(Double.parseDouble(format.format(amount)));
                } else {
                    set.add(0.00);
                }
            }
        }
        return list == null || list.size() == 0 ? null : set.first();
    }

    public Double selectPayAmountAvg(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectPayAmountAvg");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, null, true, true, true, false, null);
        Double sum = buildSum(list, false, false, false);
        return sum == null ? null : sum / list.size();
    }

    public Double selectNoBalanceAmountAvg(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectNoBalanceAmountAvg");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, null, true, true, true, true, null);
        Double sum = buildSum(list, false, false, false);
        return sum == null ? null : sum / list.size();
    }

    public Double selectNoBalanceAmountSum(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectNoBalanceAmountSum");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, null, true, true, true, false, null);
        return buildSum(list, false, false, true);
    }

    public Long selectPaymentCtn(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectPaymentCtn");
        return buildCount(result, days, false, null, null, true, true, false);
    }

    public Double selectPayLaunchTypeOneSuccessAmountSum(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectPayLaunchTypeOneSuccessAmountSum");
        List<Map<String, Object>> list = filterByCondition(result, days, false, 2, 1, true, true, true, false, null);
        return buildSum(list, false, false, false);
    }

    public Double selectSuccessNoSystemPaymentAmountSum(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectSuccessNoSystemPaymentAmountSum");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, null, true, true, true, false, null);
        return buildSum(list, false, true, false);
    }

    public Long selectSuccessNoSystemPaymentCount(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectSuccessNoSystemPaymentCount");
        return buildCount(result, days, false, 2, null, true, true, false);
    }

    public Long selectTotalNoSystemPaymentCount(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectTotalNoSystemPaymentCount");
        return buildCount(result, days, false, null, null, true, true, false);
    }

    public Long selectSuccessNoSystemPaymentAmountSumRate(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectSuccessNoSystemPaymentAmountSumRate");
        Double sum = buildSum(filterByCondition(result, days, false, 2, null, true, true, true, false, null), false, false, false);
        return sum == null ? 0 : sum.longValue();
    }

    public Long selectTotalNoSystemPaymentAmountSumRate(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "selectTotalNoSystemPaymentAmountSumRate");
        Double sum = buildSum(filterByCondition(result, days, false, null, null, true, true, true, false, null), false, false, false);
        return sum == null ? null : sum.longValue();
    }

    public Map<String, Long> getRepayRateInWeekend(String userKey, int days) {
        List<Map<String, Object>> result = getHBaseByUserKey(userKey, 5, days, "getRepayRateInWeekend");
        List<Map<String, Object>> list = filterByCondition(result, days, false, null, 1, true, true, true, false, null);
        Map<String, Long> map = new HashMap<>();
        map.put("D180_pay_launch_type_1_cnt", (long) (list == null ? 0 : list.size()));
        if (list == null) {
            map.put("D180_pay_launch_type_1_weekday_cnt", (long) 0);
        } else {
            Long count = 0L;
            for (Map<String, Object> objectMap : list) {
                if (objectMap.get("create_time") != null) {
                    long create_time = Long.parseLong(objectMap.get("create_time").toString());
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(new Date(create_time));
                    int weekday = calendar.get(Calendar.DAY_OF_WEEK);
                    if (weekday == 1 || weekday == 7) {
                        count++;
                    }
                }
            }
            map.put("D180_pay_launch_type_1_weekday_cnt", count);
        }
        return map;
    }

    public List<Map<String, Object>> getUserPay(String userKey) {
        List<Map<String, Object>> list = getHBaseByUserKey(userKey, 5, 0, "getUserPay");
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(map -> {
                Map<String, Object> newMap = new HashMap<>();
                // 将时间转成date类型，mysql是date类型
                newMap.put("update_time", map.get("update_time") == null ? null : new Timestamp(Long.parseLong(map.get("update_time").toString())));
                newMap.put("create_time", map.get("create_time") == null ? null : new Timestamp(Long.parseLong(map.get("create_time").toString())));
                newMap.put("pay_launch_type", map.get("pay_launch_type"));
                newMap.put("account_report_status", map.get("account_report_status"));
                if (map.containsKey("pay_message") && map.get("pay_message") != null) {
                    newMap.put("pay_message", map.get("pay_message"));
                }
                newMap.put("id", map.get("id"));
                return newMap;
            }).sorted((o1, o2) -> {
                Long id1 = (Long) o1.get("id");
                Long id2 = (Long) o2.get("id");
                return id1.compareTo(id2);
            }).collect(Collectors.toList());
        }
        return null;
    }

    public void compareMysqlAndHbase(Object mysqlData, String methodName, Object... params) {
        try {
            //判断是否开启比较
            boolean flag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.DI_SPACE, "compareWithMysql.flag", false);
            boolean userPayLog = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.DI_SPACE, "compareWithMysql.userPay.log", false);
            LoggerProxy.info("compareWithMysql", logger, "methodName : {}, flag : {}", methodName, flag);
            if (flag) {
                Integer limitNumber = ApolloClientAdapter.getIntConfig(ApolloNamespaceEnum.DI_SPACE, "compareWithMysql.limit", 10);
                if (new Random().nextInt(100) > limitNumber) {
                    return;
                }
                //获取hbase结果
                Object hbaseData = getNewUserPayData(methodName, params);
                //不分类型，一律放进map中拍平比较
                Map<String, Object> mysqlTempMap = new HashMap<>();
                Map<String, Object> hbaseTempMap = new HashMap<>();
                if ("getUserPay".equals(methodName)) {
                    // 获取原数据源数据
                    List<Map<String, Object>> mysqlUserPayList = getUserPayOrderBy((String) params[0]);
                    mysqlTempMap.put("data", mysqlUserPayList);
                } else {
                    mysqlTempMap.put("data", mysqlData);
                }
                hbaseTempMap.put("data", hbaseData);

                MapDifference<String, Object> diff = MapUtils.compareWithFlatten(mysqlTempMap, hbaseTempMap, false);
                if (diff.entriesDiffering().isEmpty() && diff.entriesOnlyOnLeft().isEmpty() && diff.entriesOnlyOnRight().isEmpty()) {
                    LoggerProxy.info("compareWithMysql", logger, "isEquals, methodName={}", methodName);
                } else {
                    if ("getUserPay".equals(methodName) && userPayLog) {
                        LoggerProxy.info("compareWithMysql", logger, "notEquals, methodName={}, userKey={}", methodName, params[0]);
                    } else {
                        LoggerProxy.info("compareWithMysql", logger, "notEquals, methodName={}, userKey={}, mysqlData={}, hbaseData={}", methodName, params[0], JSONUtils.toJSONString(mysqlTempMap.get("data")), JSONUtils.toJSONString(hbaseTempMap.get("data")));
                        // 打印差集
                        LoggerProxy.info("compareWithMysql", logger, "methodName={}, userKey={}, entry:{}, onlyOnLeft:{}, onlyOnRight:{}", methodName, params[0], diff.entriesDiffering(), JSONObject.toJSONString(diff.entriesOnlyOnLeft()), JSONObject.toJSONString(diff.entriesOnlyOnRight()));
                    }
                }
            }
        } catch (Exception e) {
            LoggerProxy.error("compareWithMysqlError", logger, "methodName= {}, error: ", methodName, e);
        }
    }

    /**
     * 获取切换后的user_pay数据
     *
     * @param methodName
     * @param params
     * @return
     */
    public Object getNewUserPayData(String methodName, Object... params) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Object hbaseData = getByMethodName(methodName, params);
        LoggerProxy.info("riskUserPayService", logger, "getNewUserPayData costTime: methodName={}, costTime={}", methodName, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        return hbaseData;
    }

    public List<Map<String, Object>> getUserPayOrderBy(String userKey) {
        List<Map<String, Object>> userPayList = accountProxyMapper.getUserPay(userKey);
        List<String> userIdByUserKey = accountProxyMapper.getUserIdByUserKey(userKey, 5);
        List<Map<String, Object>> userPayOldList = analysisMapper.getUserPay(userIdByUserKey);
        if (!org.springframework.util.CollectionUtils.isEmpty(userIdByUserKey)) {
            userPayList.addAll(userPayOldList);
        }
        Map<Long, Map<String, Object>> newMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userPayList)) {
            userPayList.forEach(map -> {
                String idStr = map.get("id") == null ? "" : map.get("id").toString();
                if (StringUtils.isBlank(idStr)) {
                    return;
                }
                map.putIfAbsent("pay_message", "");
                // updateTime大的做替换
                Map<String, Object> existMap = newMap.get(Long.parseLong(idStr));
                if (existMap != null) {
                    Timestamp oldUpdateTime = existMap.get("update_time") == null ? null : (Timestamp) existMap.get("update_time");
                    Timestamp newUpdateTime = map.get("update_time") == null ? null : (Timestamp) map.get("update_time");
                    if (oldUpdateTime != null && newUpdateTime != null && oldUpdateTime.before(newUpdateTime)) {
                        newMap.put(Long.parseLong(idStr), map);
                    }
                } else {
                    newMap.put(Long.parseLong(idStr), map);
                }
            });
        }
        return new ArrayList<>(newMap.values()).stream().sorted((o1, o2) -> {
            Long id1 = (Long) o1.get("id");
            Long id2 = (Long) o2.get("id");
            return id1.compareTo(id2);
        }).collect(Collectors.toList());
    }

    public Object getByMethodName(String methodName, Object... params) {
        switch (methodName) {
            case SELECT_6_M_CHARGE_TOTAL_COUNT:
                return select6mChargeTotalCount((String) params[0]);
            case SELECT_6_M_CHARGE_TOTAL_COUNT_T_1:
                return select6mChargeTotalCountT1((String) params[0]);
            case SELECT_RRD_3_M_CHARGE_FAIL_COUNT:
                return selectRrd3mChargeFailCount((String) params[0]);
            case SELECT_6_M_CHARGE_SUCCESS_COUNT:
                return select6mChargeSuccessCount((String) params[0]);
            case SELECT_6_M_CHARGE_SUCCESS_COUNT_T_1:
                return select6mChargeSuccessCountT1((String) params[0]);
            case SELECT_3_M_CHARGE_TOTAL_AMOUNT:
                return select3mChargeTotalAmount((String) params[0]);
            case SELECT_3_M_CHARGE_TOTAL_AMOUNT_T_1:
                return select3mChargeTotalAmountT1((String) params[0]);
            case SELECT_3_M_CHARGE_TOTAL_FAIL_AMOUNT:
                return select3mChargeTotalFailAmount((String) params[0]);
            case SELECT_3_M_CHARGE_TOTAL_FAIL_AMOUNT_T_1:
                return select3mChargeTotalFailAmountT1((String) params[0]);
            case SELECT_REPAY_AMOUNT:
                return selectRepayAmount((String) params[0], (int) params[1]);
            case SELECT_SUCCESS_REPAY_TIMES:
                return selectSuccessRepayTimes((String) params[0], (int) params[1]);
            case SELECT_TOTAL_REPAY_TIMES:
                return selectTotalRepayTimes((String) params[0], (int) params[1]);
            case SELECT_TOTAL_DEDUCTION_TIMES:
                return selectTotalDeductionTimes((String) params[0], (int) params[1]);
            case SELECT_SUCCESS_DEDUCTION_TIMES:
                return selectSuccessDeductionTimes((String) params[0], (int) params[1]);
            case SELECT_PAY_LAUNCH_TYPE_HOUR_AVG:
                return selectPayLaunchTypeHourAvg((String) params[0], (int) params[1]);
            case SELECT_NO_BALANCE_AMOUNT_MAX:
                return selectNoBalanceAmountMax((String) params[0], (int) params[1]);
            case SELECT_PAY_LAUNCH_TYPE_ZERO_AMOUNT_SUM:
                return selectPayLaunchTypeZeroAmountSum((String) params[0], (int) params[1]);
            case SELECT_PAY_LAUNCH_TYPE_ONE_AMOUNT_MIN:
                return selectPayLaunchTypeOneAmountMin((String) params[0], (int) params[1]);
            case SELECT_PAY_AMOUNT_AVG:
                return selectPayAmountAvg((String) params[0], (int) params[1]);
            case SELECT_NO_BALANCE_AMOUNT_AVG:
                return selectNoBalanceAmountAvg((String) params[0], (int) params[1]);
            case SELECT_NO_BALANCE_AMOUNT_SUM:
                return selectNoBalanceAmountSum((String) params[0], (int) params[1]);
            case SELECT_PAYMENT_CTN:
                return selectPaymentCtn((String) params[0], (int) params[1]);
            case SELECT_PAY_LAUNCH_TYPE_ONE_SUCCESS_AMOUNT_SUM:
                return selectPayLaunchTypeOneSuccessAmountSum((String) params[0], (int) params[1]);
            case SELECT_SUCCESS_NO_SYSTEM_PAYMENT_AMOUNT_SUM:
                return selectSuccessNoSystemPaymentAmountSum((String) params[0], (int) params[1]);
            case SELECT_SUCCESS_NO_SYSTEM_PAYMENT_COUNT:
                return selectSuccessNoSystemPaymentCount((String) params[0], (int) params[1]);
            case SELECT_TOTAL_NO_SYSTEM_PAYMENT_COUNT:
                return selectTotalNoSystemPaymentCount((String) params[0], (int) params[1]);
            case SELECT_SUCCESS_NO_SYSTEM_PAYMENT_AMOUNT_SUM_RATE:
                return selectSuccessNoSystemPaymentAmountSumRate((String) params[0], (int) params[1]);
            case SELECT_TOTAL_NO_SYSTEM_PAYMENT_AMOUNT_SUM_RATE:
                return selectTotalNoSystemPaymentAmountSumRate((String) params[0], (int) params[1]);
            case GET_REPAY_RATE_IN_WEEKEND:
                return getRepayRateInWeekend((String) params[0], (int) params[1]);
            case GET_USER_PAY:
                return getUserPay((String) params[0]);
            default:
                throw new RuntimeException("methodName unknown: " + methodName);
        }
    }

    /**
     * 加盐
     *
     * @param map
     * @param salt
     * @return
     */
    private String getSign(Map<String, String> map, String salt) {
        List<Map.Entry<String, String>> entryList = MapUtils.sortMapList(map);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : entryList) {
            sb.append(entry.getKey());
            sb.append("=");
            sb.append(entry.getValue());
            sb.append("&");
        }
        String sign = sb.substring(0, sb.length() - 1);
        return MD5Util.md5Hex(sign + salt);
    }

    public static void main(String[] args) throws Exception {
        String mysql = "";
        List<Map<String, Object>> result1 = (List<Map<String, Object>>) JSON.parse(mysql);
        System.out.println(JSONUtils.toJSONString(result1));
        String hbase = "";
        List<Map<String, Object>> result2 = (List<Map<String, Object>>) JSON.parse(hbase);
        RiskUserPayService service = new RiskUserPayService();
        service.getUserPayFromAccount("", 3, 100L);
        Map<String, Object> mysqlTempMap = new HashMap<>();
        Map<String, Object> hbaseTempMap = new HashMap<>();
        mysqlTempMap.put("data", result1);
        hbaseTempMap.put("data", result2);
        MapDifference<String, Object> diff = MapUtils.compareWithFlatten(mysqlTempMap, hbaseTempMap, true);
        System.out.println(diff.entriesDiffering().isEmpty() && diff.entriesOnlyOnLeft().isEmpty() && diff.entriesOnlyOnRight().isEmpty());
        System.out.println(JSONObject.toJSONString(diff.entriesInCommon()));
        System.out.println(diff.entriesDiffering());
        service.compareMysqlAndHbase(result1, "selectTotalDeductionTimes", "", 0);
        System.out.println(result1.toString());

    }
}
