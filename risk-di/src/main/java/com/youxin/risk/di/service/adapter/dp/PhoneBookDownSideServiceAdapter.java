package com.youxin.risk.di.service.adapter.dp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.dao.datacenter.DcWordStockMapper;
import com.youxin.risk.commons.model.datacenter.DcWordStock;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SpringContext;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Desc 通讯录反面特征
 * @Auth linchongbin
 * @Date 2021/12/01 13:17
 */
public class PhoneBookDownSideServiceAdapter extends ServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(PhoneBookDownSideServiceAdapter.class);

    private DcWordStockMapper dcWordStockMapper = SpringContext.getBean(DcWordStockMapper.class);

    private static final String HIGHT_TAG = "负面标签_高风险";

    private static final String MID_TAG = "负面标签_中风险";

    private static final String HIGHT_RISK_COUNT_KEY = "hightRiskCount";

    private static final String MID_RISK_COUNT_KEY = "midRiskCount";

    private static final String HIT_DOWN_SIDE_WORD_KEY = "hitDownSideWord";

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        String url = generatorUrl(request);
        Map<String, Object> params = this.generatorParams(request);
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        String requestBody = JacksonUtil.toJson(params);
        LoggerProxy.info("PhoneBookDownSideServiceAdapter.callServiceParams", LOGGER, "before call service, serviceCode={},params={}", request.getServiceCode(), requestBody);
        String result = SyncHTTPRemoteAPI.get(url, diService.getServiceTimeout().intValue());
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("PhoneBookDownSideServiceAdapter.serviceResponseError", LOGGER, "service response status error, request={}", JacksonUtil.toJson(request));
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject dataResult = JSON.parseObject(result);
        JSONArray records = dataResult.getJSONArray("records");
        if (records == null || records.isEmpty()) {
            LoggerProxy.warn("PhoneBookDownSideServiceAdapter.serviceResponseError", LOGGER, "records is empty, request={}", JacksonUtil.toJson(request));
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }
        // 获取通讯录详单：https://babel.we.cn/babel/v1/record/PHONE_BOOK?jobid=haohuan-c5194917-b3f4-a071-c9a6-7c1c200bbe6f&systemid=HAO_HUAN&idType=jobId
        JSONArray phoneBooks = records.getJSONObject(0).getJSONArray("phoneBooks");
        // 获取风险类词库
        final List<DcWordStock> dcWordStocks = dcWordStockMapper.selectAll();
        final Map<String, List<DcWordStock>> wordMap = dcWordStocks.stream().collect(Collectors.groupingBy(DcWordStock::getTagName));
        AtomicInteger hitHighCount = new AtomicInteger(0);
        AtomicInteger hitMidCount = new AtomicInteger(0);
        phoneBooks.parallelStream().forEach((phoneBook) -> {
            JSONObject ph = (JSONObject) phoneBook;
            final String name = ph.getString("name");
            final String remark = ph.getString("remark");
            // 命中单个关键字的高风险
            boolean hitHight = hitTag(name, remark, wordMap.get(HIGHT_TAG));
            if (hitHight) {
                hitHighCount.getAndAdd(1);
                return;
            }
            boolean hitMid = hitTag(name, remark, wordMap.get(MID_TAG));
            if (hitMid) {
                hitMidCount.getAndAdd(1);
            }
        });
        response.setRetCode(RetCodeEnum.SUCCESS.getValue());
        response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(HIGHT_RISK_COUNT_KEY, hitHighCount.get());
        resultMap.put(MID_RISK_COUNT_KEY, hitMidCount.get());
        response.setResult(resultMap);
        response.setData(JSON.toJSONString(resultMap));
        return response;
    }

    /**
     * 是否命中
     *
     * @param name
     * @param remark
     * @param wordList
     * @return
     */
    private boolean hitTag(String name, String remark, List<DcWordStock> wordList) {
        boolean isHit = false;
        for (DcWordStock word : wordList) {
            // 单个关键字
            if (StringUtils.isBlank(word.getKeyword2())) {
                isHit = StringUtils.contains(name, word.getKeyword1()) || StringUtils.contains(remark, word.getKeyword1());
            }
            if (!isHit) {
                isHit = (StringUtils.contains(name, word.getKeyword1()) && StringUtils.contains(name, word.getKeyword2()))
                        || (StringUtils.contains(remark, word.getKeyword1()) && StringUtils.contains(remark, word.getKeyword2()));
            }
            if (isHit) {
                break;
            }
        }
        return isHit;
    }
}
