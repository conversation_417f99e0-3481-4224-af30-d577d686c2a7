package com.youxin.risk.di.service.hbase;

import org.apache.hadoop.hbase.client.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public interface HbaseTable extends Serializable{

    String getTableName();

    String getRowKey();

    Put buildPut();

    void readValue(Result value);

    void readValues(ResultScanner resultScanner);

    void readSimpleValue(Result value);

    Get buildGet(String rowkey);

    Scan buildScan();

    default Scan buildScanWithStartRowAndStopRow() {
        //do nothing
        return null;
    }

}
