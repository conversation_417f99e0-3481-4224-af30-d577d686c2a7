package com.youxin.risk.di.service.adapter.tongdun;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.OsPlatform;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

public class TongdunServiceV2Adapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(TongdunServiceV2Adapter.class);

    @Override
    protected ServiceResponse callService(ServiceRequest request) {

        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        Map<String, Object> params = Maps.newHashMap();
        params.putAll(request.getParams());
        String cardNumber = (String)params.get("card_number");
        try{
            if(CaesarUtil.isEncrypted(cardNumber)){
                LoggerProxy.info("youxinTongdunBankNoEncrypt",LOGGER,"bankNo={}",cardNumber);
                cardNumber = CaesarUtil.decode(cardNumber);
                LoggerProxy.info("youxinTongdunBankNoDecrypt",LOGGER,"bankNo={}",cardNumber);
                params.put("card_number",cardNumber);
            }
        }catch (Exception e){
            LoggerProxy.error("decodeBankNo",LOGGER,"同盾银行卡号解密失败,bankNo={}",cardNumber, e);
            throw e;
        }
        generatorParams(request, params);
        String result = SyncHTTPRemoteAPI.postJson(diService.getServiceUrl(), JacksonUtil.toJson(params), diService.getServiceTimeout().intValue());

        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject resultJson = JSONObject.parseObject(result);
        response.setOriginalStatus("1");
        String data = resultJson.getString("data");
        if (StringUtils.isNotBlank(data)) {
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            Map<String, Object> resultMap = Maps.newHashMap();
            JSONObject dataJson = JSONObject.parseObject(data);
            JSONObject newDataJson = MapUtils.underLineKeyToCamelKey(dataJson);
            resultMap.put("data", newDataJson.toJSONString());
            response.setResult(resultMap);
            response.setData(newDataJson.toJSONString());
        }
        return response;
    }

    private void generatorParams(ServiceRequest request, Map<String, Object> params) {
        HermesConstants hermesConstants = ContextUtil.getBean(HermesConstants.class);
        String platform = (String) request.getParams().get("platform");
        if (OsPlatform.IOS.toString().equals(platform)) {
            params.put("secret_key", hermesConstants.getTongdunSecretKeyIOS());
            params.put("event_id", hermesConstants.getTongdunEventIdIOS());
        } else if (OsPlatform.ANDROID.toString().equals(platform)) {
            params.put("secret_key", hermesConstants.getTongdunSecretKeyAndroid());
            params.put("event_id", hermesConstants.getTongdunEventIdAndroid());
        }
    }
}
