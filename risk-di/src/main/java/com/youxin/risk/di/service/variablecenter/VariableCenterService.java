package com.youxin.risk.di.service.variablecenter;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.DiTask;
import com.youxin.risk.commons.service.di.DiTaskService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.di.controller.VariableCenterController;
import com.youxin.risk.di.service.handler.DiTaskHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: juxiang
 * @create: 2024-01-09 10:55
 **/

@Service
public class VariableCenterService {
    private static final Logger LOGGER = LoggerFactory.getLogger(VariableCenterService.class);
    @Autowired
    DiTaskService diTaskService;

    @Autowired
    DiTaskHandler diTaskHandler;
    public VariableRes variableCallBack(VariableReq variableReq){
        if(!variableReq.allVarStatusCalSuccess()){
            LoggerProxy.error("variableCallBack",LOGGER,"requestId:{},var:{},status error",variableReq.getRequestId(),variableReq.getErrorVar());
            return VariableRes.failed(String.format("var:%s status error!", variableReq.getErrorVar()));
        }
        List<DiTask> tasks = diTaskService.getTasksByRequestId(variableReq.getRequestId());
        if(CollectionUtils.isEmpty(tasks)){
            return VariableRes.failed("not find this task!");
        }
       boolean dealSuccess= diTaskHandler.dealTaskMessageFromVariableCenter(tasks.get(0),variableReq);
        if(!dealSuccess){
            return VariableRes.failed("message deal failed!");
        }
        return VariableRes.success();
    }

    public static class VariableReq{
        private String requestId;

        private List<Variable> variableList;
        /**
         * 用来记录异常的变量
         */
        private String errorVar;

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public List<Variable> getVariableList() {
            return variableList;
        }

        public void setVariableList(List<Variable> variableList) {
            this.variableList = variableList;
        }

        public String getErrorVar() {
            return errorVar;
        }

        public boolean allVarStatusCalSuccess(){
            if(CollectionUtils.isNotEmpty(this.variableList)){
                Variable failedVar=this.variableList.stream().filter(e->e.failed()).findFirst().orElse(null);
                if(Objects.isNull(failedVar)){
                    return true;
                }
                this.errorVar=failedVar.getCode();
            }
            return false;
        }
    }

    public static class VariableRes{
        private Integer status;
        private String message;
        public static Integer SUCCESS_CODE=0;
        public static Integer FAILED_CODE=-1;

        public VariableRes(Integer status, String message) {
            this.status = status;
            this.message = message;
        }

        public static VariableRes success(){
            return new VariableRes(SUCCESS_CODE,"success");
        }

        public static VariableRes failed(String message){
            return new VariableRes(FAILED_CODE,message);
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

    }


    public static class Variable{
        private String value;
        private Integer status;
        private String message;
        private String code;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public boolean failed(){
            return this.status!=0;
        }
    }
}
