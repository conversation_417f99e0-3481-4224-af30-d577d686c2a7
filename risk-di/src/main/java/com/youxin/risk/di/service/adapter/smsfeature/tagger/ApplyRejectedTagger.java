package com.youxin.risk.di.service.adapter.smsfeature.tagger;

import cn.hutool.core.util.ReUtil;
import com.youxin.risk.commons.constants.SmsFeatureConstant;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 申请被拒
 * <AUTHOR>
 * @since 2022/3/11 10:18
 */
@Component
@Order(20)
public class ApplyRejectedTagger extends AbstractTagger implements InitializingBean {

    private static ApplyRejectedTagger instance = null;

    public static ApplyRejectedTagger getInstance(){
        return instance;
    }

    @Override
    public String tagger(String body,String preTag) {
        boolean regex1 = ReUtil.contains("借|贷|信用卡|授信|额度|提额|分期|放款",body);
        if(!regex1){
            return null;
        }
        boolean regex2 = ReUtil.contains("您|你",body);
        if(!regex2){
            return null;
        }
        if(!getRegex3(body)){
            return null;
        }
        boolean regex4 = ReUtil.contains("系统繁忙|审批故障|系统异常|系统故障|收款失败|账户限额",body);
        if(regex4){
            return null;
        }
        return SmsFeatureConstant.APPLY_REJECTED;
    }

    private boolean getRegex3(String body){
        boolean lowQualityFlag1 = ReUtil.contains("(综合)(评分|资质|信用|评估)(较低|过低|不足|不佳|不良|不符)",body);
        boolean lowQualityFlag2 = ReUtil.contains("(申请|审批|审核|授信|放款|借款|贷款)(失败|拒绝|被拒|((不|不予|不能|无法|未|未能)(通过|核准|批准|成功)))",body);
        boolean lowQualityFlag3 = ReUtil.contains("(不|不予|不能|无法|未|未能)(申请|审批|审核|授信|放款|借款|贷款)(通过|核准|批准|成功)",body);
        boolean lowQualityFlag4 = ReUtil.contains("(不|不予|不能|无法|未|未能)(通过|核准|批准)((您的|你的)?)(申请|审批|审核|授信|放款|借款|贷款)",body);
        if(lowQualityFlag1 || lowQualityFlag2 || lowQualityFlag3 || lowQualityFlag4){
            return true;
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() {
        instance = this;
    }
}
