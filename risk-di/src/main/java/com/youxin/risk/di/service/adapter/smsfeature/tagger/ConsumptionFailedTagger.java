package com.youxin.risk.di.service.adapter.smsfeature.tagger;

import cn.hutool.core.util.ReUtil;
import com.youxin.risk.commons.constants.SmsFeatureConstant;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 消费失败
 * <AUTHOR>
 * @since 2022/3/11 10:19
 */
@Order(30)
@Component
public class ConsumptionFailedTagger extends AbstractTagger implements InitializingBean {

    private static ConsumptionFailedTagger instance = null;

    public static ConsumptionFailedTagger getInstance(){
        return instance;
    }

    @Override
    public String tagger(String body,String preTag) {
        boolean regex1 = ReUtil.contains("额度不足",body);
        if(!regex1){
            return null;
        }
        boolean regex2 = ReUtil.contains("交易|消费|刷卡|支付",body);
        if(!regex2){
            return null;
        }
        boolean regex3 = ReUtil.contains("失败|((不|不能|无法|未|未能)(成功|通过))",body);
        if(!regex3){
            return null;
        }
        boolean regex4 = ReUtil.contains("还款(失败|((不|不能|无法|未|未能)(成功|通过)))",body);
        if(regex4){
            return null;
        }
        return SmsFeatureConstant.CONSUMPTION_FAILED;
    }

    @Override
    public void afterPropertiesSet() {
        instance = this;
    }
}
