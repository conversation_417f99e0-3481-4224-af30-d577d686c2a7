package com.youxin.risk.di.service.adapter.hhbusiness;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.ucredit.riskanalysis.utils.StringUtils;
import com.youxin.risk.commons.dao.haofenqi.HaoHuanUserMapper;
import com.youxin.risk.di.mapper.haohuan.HaohuanMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class HhBusinessMysqlServiceAdapterTest {

    @Autowired
    private HaoHuanUserMapper haoHuanUserMapper;
    @Autowired
    private HaohuanMapper haohuanMapper;

    private static final Map<String, String> USER_STATUS = new ImmutableMap.Builder<String, String>()
            .put("0", "A")
            .put("1", "B")
            .put("2", "C")
            .put("3", "D")
            .put("4", "E")
            .put("5", "F")
            .put("6", "O")
            .put("7", "T")
            .put("8", "G")
            .put("9", "R")
            .build();

    @Test
    public void testCallService() {
        JSONObject data = new JSONObject();
        data.put("accountStatus", null);
        data.put("updatedAt", -999);
        String userKey = "ab2568fb3f8c54a87ab25e8fe2abf8d9";
        //uid
        String uid = haohuanMapper.getUidByUserKey(userKey);
        if (StringUtils.isEmpty(uid)) {
            System.out.println("no exists");
        }else {
            Map<String, Object> userLevel = haoHuanUserMapper.getUserAccountStatusByUid(uid);
            if(null != userLevel && !userLevel.isEmpty()){
                data.put("accountStatus", USER_STATUS.getOrDefault(String.valueOf(userLevel.get("account_status")), null));
                data.put("updatedAt", userLevel.get("updated_at"));
            }
        }
        assertEquals( "Unexpected value", "B", data.getString("accountStatus"));
    }
}
