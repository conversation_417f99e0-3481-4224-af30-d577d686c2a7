package com.ucredit.riskanalysis.data;

import com.youxin.risk.commons.model.DiTask;
import com.youxin.risk.commons.vo.DataPlatformMessageVo;
import com.youxin.risk.di.utils.CreditUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class DataformMessageListenerTest {

    @Autowired
    private CreditUtils creditUtils;

    @Test
    public void getValue() {
        DataPlatformMessageVo message = new DataPlatformMessageVo();
        message.setRecordType("FUNDS_CREDIT_RECORD");
        // 黑卡
        message.setData("{\n" +
                "    \"timestamp\": 1684289320014,\n" +
                "    \"idNumber\": \"PDMqwBHT3ZC6exBYDEAtgDu2rVOdVe4rR4xcIqYnkJE=\",\n" +
                "    \"job\": {\n" +
                "        \"jobID\": \"eeee9fc7-eb39-42a8-b2a6-240a6773bf83\",\n" +
                "        \"finished\": true,\n" +
                "        \"requested\": true,\n" +
                "        \"successful\": true,\n" +
                "        \"startTime\": 1684289320020,\n" +
                "        \"endTime\": 1684289320020,\n" +
                "        \"dataFile\": null,\n" +
                "        \"result\": null,\n" +
                "        \"systemID\": \"HAO_HUAN\",\n" +
                "        \"query\": \"qVm4/N/6kDjJLtLZ4bFxQYztUXsNHOfChHewMwXcey0=\",\n" +
                "        \"tryTimes\": 0,\n" +
                "        \"extra\": {}\n" +
                "    },\n" +
                "    \"timeInCache\": 1684289320052,\n" +
                "    \"userKey\": \"fa871c3ec7a378e15dfe3f7c3b2f530d\",\n" +
                "    \"loanId\": \"48854611\",\n" +
                "    \"data\": {\n" +
                "        \"result\": 1,\n" +
                "        \"reason\": null,\n" +
                "        \"creditScore\": {\n" +
                "            \"yxGrade8\": null,\n" +
                "            \"yxGrade9\": null,\n" +
                "            \"yxGrade6\": null,\n" +
                "            \"yxGrade7\": null,\n" +
                "            \"yxGrade10\": null,\n" +
                "            \"yxGrade4\": null,\n" +
                "            \"yxGrade5\": null,\n" +
                "            \"creditAmount\": null,\n" +
                "            \"yxGrade2\": null,\n" +
                "            \"yxGrade3\": null,\n" +
                "            \"yxGrade1\": null,\n" +
                "            \"queryCode\": null,\n" +
                "            \"errorMessages\": null,\n" +
                "            \"reportDetail\": null,\n" +
                "            \"code\": null,\n" +
                "            \"msg\": null,\n" +
                "            \"xzTrustScore\": null,\n" +
                "            \"ljTrustScore\": null,\n" +
                "            \"trustScore\": null,\n" +
                "            \"xinWangCreditScore\": null,\n" +
                "            \"heiKaCreditScore\": {\n" +
                "                \"pid\": null,\n" +
                "                \"reqId\": null,\n" +
                "                \"status\": null,\n" +
                "                \"data\": {\n" +
                "                    \"yxGrade1\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": null,\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade2\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"0.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade3\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"444.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade4\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"463.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade5\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"253.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade6\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"652.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade7\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"1.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade8\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"195.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade9\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"384.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade10\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"34.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade11\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"70\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"yxGrade12\": {\n" +
                "                        \"code\": \"200\",\n" +
                "                        \"score\": \"142.0\",\n" +
                "                        \"msg\": null\n" +
                "                    },\n" +
                "                    \"feature\": \"\"\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "        \"channel\": \"HEIKA\",\n" +
                "        \"funderRejectSubject\": null,\n" +
                "        \"allFunderSubject\": null,\n" +
                "        \"rejectReason\": null\n" +
                "    },\n" +
                "    \"channel\": \"HEIKA\",\n" +
                "    \"isReject\": 0,\n" +
                "    \"loanKey\": \"hh_b7763098-7959-433d-9e64-3b60152a03bb\",\n" +
                "    \"type\": \"FUNDS_CREDIT_RECORD\"\n" +
                "}");
        // 新网
//        message.setData("{\n" +
//                "    \"timestamp\":1684133542715,\n" +
//                "    \"idNumber\":\"r7S0Sej0tMpipHkdWM2a6uVejojALX/RGozIcsW2B2A=\",\n" +
//                "    \"job\":{\n" +
//                "        \"jobID\":\"3c36cd0f-5410-4222-a729-a4716c83c39d\",\n" +
//                "        \"finished\":true,\n" +
//                "        \"requested\":true,\n" +
//                "        \"successful\":true,\n" +
//                "        \"startTime\":\"2023-05-15T06:52:22.715+00:00\",\n" +
//                "        \"endTime\":\"2023-05-15T06:52:22.715+00:00\",\n" +
//                "        \"dataFile\":null,\n" +
//                "        \"result\":null,\n" +
//                "        \"systemID\":\"HAO_HUAN\",\n" +
//                "        \"query\":\"EOtqgaIG2o6xUuV5/dZ/y7FqhuARxUN8g3M2alPNah0=\",\n" +
//                "        \"tryTimes\":0,\n" +
//                "        \"extra\":{\n" +
//                "\n" +
//                "        }\n" +
//                "    },\n" +
//                "    \"timeInCache\":\"2023-05-15T06:52:22.913+00:00\",\n" +
//                "    \"userKey\":\"ef6ad23835ad3ba8c70848a2b2a0deac\",\n" +
//                "    \"loanId\":\"35133664\",\n" +
//                "    \"data\":{\n" +
//                "        \"result\":1,\n" +
//                "        \"reason\":null,\n" +
//                "        \"creditScore\":{\n" +
//                "            \"yxGrade8\":null,\n" +
//                "            \"yxGrade9\":null,\n" +
//                "            \"yxGrade6\":null,\n" +
//                "            \"yxGrade7\":null,\n" +
//                "            \"yxGrade10\":null,\n" +
//                "            \"yxGrade4\":null,\n" +
//                "            \"yxGrade5\":null,\n" +
//                "            \"creditAmount\":null,\n" +
//                "            \"yxGrade2\":null,\n" +
//                "            \"yxGrade3\":null,\n" +
//                "            \"yxGrade1\":null,\n" +
//                "            \"queryCode\":null,\n" +
//                "            \"errorMessages\":null,\n" +
//                "            \"reportDetail\":null,\n" +
//                "            \"code\":null,\n" +
//                "            \"msg\":null,\n" +
//                "            \"xzTrustScore\":null,\n" +
//                "            \"ljTrustScore\":null,\n" +
//                "            \"trustScore\":null,\n" +
//                "            \"xinWangCreditScore\":{\n" +
//                "                \"pid\":\"177weicai20230505165612843027\",\n" +
//                "                \"status\":\"0000\",\n" +
//                "                \"data\":{\n" +
//                "                    \"yxGrade1\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":null,\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade2\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.110131111\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade3\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.11353522\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade4\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.520131107\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade5\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.112116011\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade6\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.060711102\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade7\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"52.0\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade8\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.530676851\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade9\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.439064622\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade10\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.016328089\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade11\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.017424731\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade12\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0.*********\",\n" +
//                "                        \"msg\":null\n" +
//                "                    }\n" +
//                "                }\n" +
//                "            },\n" +
//                "            \"heiKaCreditScore\":null\n" +
//                "        },\n" +
//                "        \"channel\":\"XW_BANK_NEW\",\n" +
//                "        \"funderRejectSubject\":null,\n" +
//                "        \"allFunderSubject\":null,\n" +
//                "        \"rejectReason\":null\n" +
//                "    },\n" +
//                "    \"channel\":\"XW_BANK_NEW\",\n" +
//                "    \"isReject\":0,\n" +
//                "    \"loanKey\":\"hh_20230505165412_A1665686629747752783\",\n" +
//                "    \"type\":\"FUNDS_CREDIT_RECORD\"\n" +
//                "}");
        // 爱建
//        message.setData("{\n" +
//                "    \"timestamp\":*************,\n" +
//                "    \"idNumber\":\"52222619920924601X\",\n" +
//                "    \"job\":{\n" +
//                "        \"jobID\":\"41ca7da7-e4c2-4c55-9f8a-115e8767102b\",\n" +
//                "        \"finished\":true,\n" +
//                "        \"requested\":true,\n" +
//                "        \"successful\":true,\n" +
//                "        \"startTime\":\"2022-12-20T05:51:08.380+00:00\",\n" +
//                "        \"endTime\":\"2022-12-20T05:51:08.380+00:00\",\n" +
//                "        \"dataFile\":null,\n" +
//                "        \"result\":null,\n" +
//                "        \"systemID\":\"HAO_HUAN\",\n" +
//                "        \"query\":\"18476057989\",\n" +
//                "        \"tryTimes\":0,\n" +
//                "        \"extra\":{\n" +
//                "\n" +
//                "        }\n" +
//                "    },\n" +
//                "    \"timeInCache\":\"2022-12-20T05:51:08.387+00:00\",\n" +
//                "    \"userKey\":\"9e163dcc7ed345c6f93dbd67e592959d\",\n" +
//                "    \"loanId\":\"44767725\",\n" +
//                "    \"data\":{\n" +
//                "        \"result\":1,\n" +
//                "        \"reason\":null,\n" +
//                "        \"creditScore\":{\n" +
//                "            \"yxGrade8\":null,\n" +
//                "            \"yxGrade9\":null,\n" +
//                "            \"yxGrade6\":null,\n" +
//                "            \"yxGrade7\":null,\n" +
//                "            \"yxGrade10\":null,\n" +
//                "            \"yxGrade4\":null,\n" +
//                "            \"yxGrade5\":null,\n" +
//                "            \"creditAmount\":null,\n" +
//                "            \"yxGrade2\":null,\n" +
//                "            \"yxGrade3\":null,\n" +
//                "            \"yxGrade1\":null,\n" +
//                "            \"queryCode\":null,\n" +
//                "            \"errorMessages\":null,\n" +
//                "            \"reportDetail\":null,\n" +
//                "            \"code\":null,\n" +
//                "            \"msg\":null,\n" +
//                "            \"xzTrustScore\":null,\n" +
//                "            \"ljTrustScore\":null,\n" +
//                "            \"trustScore\":{\n" +
//                "                \"reqId\":\"AJ_20221220xcPIpErw\",\n" +
//                "                \"pid\":\"a68ee476c60b45a487607eb21ceda12d\",\n" +
//                "                \"status\":\"0000\",\n" +
//                "                \"data\":{\n" +
//                "                    \"yxGrade1\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade2\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade3\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"29\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade4\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"895\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade5\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"451\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade6\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"618\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade7\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"1\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade8\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"778\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade9\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"377\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade10\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"488\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade11\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"59\",\n" +
//                "                        \"msg\":null\n" +
//                "                    },\n" +
//                "                    \"yxGrade12\":{\n" +
//                "                        \"code\":\"200\",\n" +
//                "                        \"score\":\"0\",\n" +
//                "                        \"msg\":null\n" +
//                "                    }\n" +
//                "                }\n" +
//                "            },\n" +
//                "            \"xinWangCreditScore\":null,\n" +
//                "            \"heiKaCreditScore\":null\n" +
//                "        },\n" +
//                "        \"channel\":\"\",\n" +
//                "        \"funderRejectSubject\":null,\n" +
//                "        \"allFunderSubject\":null,\n" +
//                "        \"rejectReason\":null\n" +
//                "    },\n" +
//                "    \"channel\":\"AJ_TRUST\",\n" +
//                "    \"isReject\":0,\n" +
//                "    \"loanKey\":\"hh_20221220135008_A8441121652892878238\",\n" +
//                "    \"type\":\"FUNDS_CREDIT_RECORD\"\n" +
//                "}");
        message.setJobID("41ca7da7-e4c2-4c55-9f8a-115e8767102b");
        DiTask task = new DiTask();
        task.setLoanKey("hh_aabbccdd");
        boolean creditRetry = creditUtils.isCreditResultStructError(message.getJobID(), message.getRecordType(), message.getData(), task.getLoanKey(), true);
        System.out.println(creditRetry);
    }
}
