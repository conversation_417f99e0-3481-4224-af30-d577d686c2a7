package com.youxin.risk.gateway.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cacheloader.AdminAgencyCacheLoader;
import com.youxin.risk.commons.dao.admin.AdminAgencyMapper;
import com.youxin.risk.commons.dao.gw.GwMiniRequestMapper;
import com.youxin.risk.commons.dao.gw.GwRequestMapper;
import com.youxin.risk.commons.model.GwRequest;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.gateway.service.GwRequestModelService;
import com.youxin.risk.verify.vo.VerifyUserLineManagementBakVo;
import com.youxin.risk.verify.vo.VerifyUserLineManagementVo;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class GatewayTest {

    @Resource
    private AdminAgencyMapper adminAgencyMapper;
    @Resource
    private AdminAgencyCacheLoader adminAgencyCacheLoader;

    @Resource
    private GwRequestMapper gwRequestMapper;

    @Resource
    private GwRequestModelService gwRequestModelService;

    @Autowired
    private GwMiniRequestMapper gwMiniRequestMapper;

    @Test
    public void startTest() {
        System.out.println(adminAgencyMapper.selectAllInMaster());
        adminAgencyCacheLoader.load();
    }

    @Test
    public void startGwReqMapperTest() {
        System.out.println(gwRequestMapper);

        GwRequest gwRequest = new GwRequest();
        gwRequest.setIcode("0");
        gwRequest.setRequestId("r");
        gwRequest.setSessionId("s");
        gwRequest.setRequestMessage("");
        gwRequest.setCallbackMessage("");

        gwRequestMapper.insertIgnore(gwRequest);

        gwRequestModelService.selectLockTimeout(10);

    }

    @Test
    public void testQuery(){
        Long startId=1L;
        int limit=10;
        int count=0;
        List<GwRequest> result = new ArrayList<>();
        List<GwRequest> notCallbackReqs = gwMiniRequestMapper.selectByIdLimit(startId,limit);
        if(CollectionUtils.isEmpty(notCallbackReqs)){
            return ;
        }
        result.addAll(notCallbackReqs);
        count=notCallbackReqs.size();
        while (count>0){
            startId=notCallbackReqs.get(notCallbackReqs.size()-1).getId();
            notCallbackReqs=gwMiniRequestMapper.selectByIdLimit(startId,limit);
            count=notCallbackReqs.size();
            result.addAll(notCallbackReqs);
        }
        return ;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException {
        String callbackJson = "{\"transId\":\"e9e6305ad52e08cb387b16fb9e94b2bd\",\"amountPeriodRateManagement\":\"\",\"btActualLine\":\"0.0\",\"loanLine\":\"5600.0\",\"accountStatus\":\"C\",\"shopAvailLine\":\"28.49\",\"shopActualLine\":\"28.49\",\"userLevel\":\"C\",\"creditLine\":\"5600.0\",\"btRate\":\"0.03\",\"loanUtilLine\":\"5571.51\",\"lineType\":\"AMOUNT_LEND_A\",\"loanKey\":\"hh_20200813165448_A6073127261937201053\",\"reasonCode\":{\"repay_info\":{\"mob\":8,\"his_max_overdue_days\":0.0,\"last_payoff_time\":**********,\"max_repay_amount\":604.*************,\"cur_overdue_cnt\":0,\"survive_days\":244.0,\"payoff_flag\":0,\"payoff_cnt\":0,\"loan_unpaid_cnt\":10,\"cur_overdue_days\":-9999,\"payoff_time_max\":-1,\"last_pay_mob\":8,\"loan_time\":\"2019-12-13\"},\"stress_max\":\"0.0\",\"mid_repay_step\":\"HH_REPAY_A\",\"ABC_step_reject_rules\":{},\"closed_rules\":{},\"loan_actual_line_last\":337.71,\"member_info\":{\"member_flag\":0},\"payoff_activeness_model_202007\":{\"payoff_activeness_level\":\"\",\"model_feats\":{\"payoff_cnt_cur\":0,\"if_first_payoff\":0,\"mob_new\":8,\"last_2m_apply_times\":3,\"last_3m_apply_times\":4,\"payoff_flag\":0,\"last_payoff_cnt\":0,\"last_1m_apply_times\":2}},\"paid_user_level\":{\"user_risk_level\":\"unpaid\",\"cap\":\"unpaid\"},\"mid_third_party_model\":{\"prob\":0.0683762025926231,\"model_feats\":{\"fenqifen_v6_score_woe\":-0.045220081,\"tianji_action_score_woe\":0.230772766,\"jiao_consume_score\":-1.0,\"jiao_cash_score\":-1.0,\"os_platform\":\"ANDROID\",\"tianji_action_score\":550,\"tcloud_risk_score\":75,\"tcloud_risk_score_woe\":0.236785589,\"tianji_location_score\":-1,\"os_platform_int\":1,\"td_score_woe\":-0.7209086,\"td_feats\":{},\"frg_list_level\":-4,\"td_score\":-1.0,\"id_province_flag_woe\":0.024421456,\"gender_int\":0,\"fenqifen_v6_score\":550,\"age\":47.0,\"id_province_flag\":3}},\"maintain_rate_result\":{\"loan\":{},\"shop\":{}},\"activity_level\":\"A\",\"mid_repay_behavior_mdoel\":{\"prob\":0.08042508654124114,\"model_feats\":{\"repay_h_0_14_mean_woe\":-0.06083933,\"last_diff_days_max_woe\":-0.13336469,\"last_diff_days_max\":0.0,\"diff_days_max\":0.0,\"diff_days_max_woe\":-0.02274222,\"repay_d0_h_12_mean\":0.0,\"repay_d0_h_12_mean_woe\":-0.21980957,\"repay_h_0_14_mean\":0.8620689655172413}},\"record\":{},\"loan_maintain_result\":{\"loan_lift\":{\"lift_level\":{\"mob\":\"mob6+\",\"prob\":0.053782539616922566,\"income_level\":\"3-\",\"user_level\":\"E\",\"user_level_old\":\"U\"}}},\"offline_feat\":{\"user_feat\":\"L\",\"new_mob\":\"mob3-8\",\"feat_score\":41,\"stress_max\":\"0.0\",\"income_level_new\":\"3-\",\"is_member\":0,\"payoff_cnt\":0,\"loan_unpaid_cnt\":8,\"user_level_diff2\":1,\"apply_cnt_date\":\"3\"},\"personal_asset\":{\"income_level_new\":\"3-\",\"zhishu_income\":\"3\",\"income_level\":\"3-\",\"br_income\":{\"br_cust_miss_flag\":1,\"flag_datacust1\":-1,\"dc1_pc_rcnt_income\":-1}},\"events\":{\"0025\":{\"tag\":\"shop_add_line_001_geta\"},\"0011\":{\"tag\":\"loan_add_line_001_geta\"}},\"last_mid_result\":{\"last_mid_verify\":0},\"periods_details\":{\"reloan_periods\":[],\"reloan_periods_time\":[],\"add_periods\":[12],\"add_periods_time\":[\"2020-07-28\"]},\"mid_repay_step_new\":\"HH_REPAY_A\",\"repay_mon_limit_tag\":{\"loan\":false,\"shop\":false},\"is_member\":\"0\",\"f_class_ensemble_model_v1\":{\"prob\":0.10534747437530799,\"model_feats\":{\"td_repaya_prob_woe\":-0.0745272078186565,\"settled_inadvance_order_cnt_last60days\":0,\"settled_inadvance_order_cnt_last60days_woe\":0.632562804750086,\"td_repaya_feats\":{},\"mid_unpaid_model_feats\":{\"repay_h_0_14_mean_woe\":-0.06083933,\"last_diff_days_max_woe\":-0.13336469,\"last_diff_days_max\":-1.0,\"diff_days_max\":-1.0,\"diff_days_max_woe\":-0.50949805,\"repay_d0_h_12_mean\":0.0,\"repay_d0_h_12_mean_woe\":-0.21980957,\"repay_h_0_14_mean\":0.7777777777777778},\"mid_unpaid_model_prob_woe\":-0.237882001724241,\"td_repaya_prob\":-1.0,\"mid_unpaid_model_prob\":0.0585382945926753}},\"cust_class\":\"unpaid\",\"payoff_mid_verify\":\"False\",\"maintain_amt_result\":{\"loan\":{},\"shop\":{}},\"reject_rules\":{},\"last_mid_verify\":0,\"user_level_mid\":\"C\",\"small_flag\":0,\"not_click_rules\":{},\"cust_code\":\"unpaid_normal\",\"is_passed\":false,\"test_random\":0.17660293659147241,\"manual_tag\":\"normal\",\"paid_user_level_202007\":{\"score\":-999,\"user_risk_level\":\"unpaid\",\"mob_flag\":\"mob2+\",\"cap\":\"unpaid\",\"single_lift_cap\":0},\"mid_unpaid_model_mob\":{\"mob\":\"mob8\",\"beha_feats\":{\"repay_h_0_14_mean_woe\":-0.06083933,\"last_diff_days_max_woe\":-0.13336469,\"last_diff_days_max\":0.0,\"diff_days_max\":0.0,\"diff_days_max_woe\":-0.02274222,\"repay_d0_h_12_mean\":0.0,\"repay_d0_h_12_mean_woe\":-0.21980957,\"repay_h_0_14_mean\":0.8620689655172413},\"beha_score\":0.08042508654124114,\"version\":\"V001_mob_version\"},\"increased_details\":{\"add_increased_level\":[\"fix\",\"fix\"],\"reloan_increased_limit\":[],\"add_increased_time\":[\"2020-04-17\",\"2020-07-28\"],\"add_increased_limit\":[600.0,1000],\"reloan_increased_time\":[],\"reloan_increased_level\":[]},\"step\":\"HH_MID_ALL_C\",\"tag_details\":{\"add_shop_flag\":true,\"reloan_decreased_rate\":0,\"add_loan_flag\":true,\"reloan_increased_limit\":0,\"add_loan_increased_limit\":0,\"add_shop_increased_limit\":0,\"add_loan_decreased_rate\":0},\"maintain_period_result\":{\"loan\":{},\"shop\":{}},\"mid_ensembel_model\":{\"prob\":0.05199658118792723,\"model_feats\":{\"b_score\":0.08042508654124114,\"a_score\":0.0683762025926231,\"income_level\":\"3-\"}},\"rates_details\":{\"reloan_rates_time\":[],\"add_rates\":[],\"reloan_rates\":[],\"add_rates_time\":[]}},\"shopRate\":\"0.03\",\"loanActualLine\":\"28.49\",\"loanAvailLine\":\"28.49\",\"ext1\":\"[{\\\"tmp_line_status\\\":0,\\\"tmp_actual_line\\\":0.0,\\\"tmp_line_end_time\\\":\\\" \\\",\\\"fix_line\\\":5600.0,\\\"tmp_line\\\":0,\\\"tmp_avail_line\\\":0.0,\\\"type\\\":1,\\\"tmp_util_line\\\":0.0},{\\\"tmp_line_status\\\":0,\\\"tmp_actual_line\\\":0.0,\\\"tmp_line_end_time\\\":\\\" \\\",\\\"fix_line\\\":4000.0,\\\"tmp_line\\\":0.0,\\\"tmp_avail_line\\\":0.0,\\\"type\\\":2,\\\"tmp_util_line\\\":0.0}]\",\"lineAssignTime\":\"1597280092000\",\"userPoint\":\"551.0\",\"btLine\":\"0.0\",\"utilLine\":\"5571.51\",\"sessionId\":\"20200813165448_A6073127261937201053\",\"btAvailLine\":\"0.0\",\"shopLine\":\"4000.0\",\"userKey\":\"7bc5a2d6f926552f5fed19490d2f676e\",\"eventCode\":\"haoHuanAmountLend\",\"btUtilLine\":\"0.0\",\"isClosed\":\"false\",\"shopUtilLine\":\"0.0\",\"shopPeriod\":\"6\",\"availLine\":\"28.49\",\"btPeriod\":\"0\",\"loanRate\":\"0.03\",\"periodLineRate\":\"[{\\\"period\\\":9,\\\"min\\\":300.0,\\\"max\\\":5600.0,\\\"rate\\\":0.03,\\\"rate_yn\\\":0.03,\\\"lh_rate\\\":0.022,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.016},{\\\"period\\\":3,\\\"min\\\":300.0,\\\"max\\\":5600.0,\\\"rate\\\":0.03,\\\"rate_yn\\\":0.03,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.019},{\\\"period\\\":12,\\\"min\\\":300.0,\\\"max\\\":5600.0,\\\"rate\\\":0.03,\\\"rate_yn\\\":0.03,\\\"lh_rate\\\":0.025,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.018},{\\\"period\\\":6,\\\"min\\\":300.0,\\\"max\\\":5600.0,\\\"rate\\\":0.03,\\\"rate_yn\\\":0.03,\\\"lh_rate\\\":0.02,\\\"type\\\":\\\"loan\\\",\\\"guarantee_rate\\\":0.014},{\\\"period\\\":6,\\\"min\\\":1.0,\\\"max\\\":4000.0,\\\"rate\\\":0.03,\\\"rate_yn\\\":0.03,\\\"lh_rate\\\":0.02,\\\"type\\\":\\\"shop\\\",\\\"guarantee_rate\\\":0.006}]\",\"loanId\":\"14736231\",\"loanPeriod\":\"12\",\"status\":\"1\",\"flowType\":\"splitAmount\"}";
        JSONObject param = JSONObject.parseObject(callbackJson);
//        JSONObject message = param.getJSONObject("data").getJSONObject("message");
        VerifyUserLineManagementBakVo verifyUserLineManagementVo = JSON.parseObject(param.toString(), VerifyUserLineManagementBakVo.class);
        VerifyUserLineManagementVo result = ObjectTransferUtils.transferObject(verifyUserLineManagementVo, VerifyUserLineManagementVo.class);
        System.out.println(JSON.toJSONString(result));
    }
}