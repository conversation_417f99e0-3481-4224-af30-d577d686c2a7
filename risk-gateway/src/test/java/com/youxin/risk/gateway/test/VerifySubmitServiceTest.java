package com.youxin.risk.gateway.test;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.model.verify.VerifySubmit;
import com.youxin.risk.verify.service.VerifySubmitService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class VerifySubmitServiceTest {

    @Autowired
    private VerifySubmitService verifySubmitService;

    @Test
    public void findSubmitByLoan(){
//        VerifySubmit verifySubmit = verifySubmitService.findSubmitByLoan("2ddd1e89204e8a3683", 111199, "012dbd33d1e8bd49204e8a3236c48387");
        VerifySubmit verifySubmit = verifySubmitService.findSubmitByLoan(null, 111199, "012dbd33d1e8bd49204e8a3236c48387");
        System.out.println(JSON.toJSONString(verifySubmit));
    }

    @Test
    public void saveOrUpdateVerifySubmit(){
        VerifySubmit verifySubmit = verifySubmitService.findSubmitByLoan("2ddd1e89204e8a3683", 111199, "012dbd33d1e8bd49204e8a3236c48387");
        verifySubmit.setId(null);
        verifySubmit.setVersion(null);
        verifySubmit.setCreateTime(new Date());
        verifySubmitService.saveOrUpdateVerifySubmit(verifySubmit);
    }


}
