package com.youxin.risk.gateway.test;

import com.youxin.risk.commons.model.Event;
import com.youxin.risk.gateway.service.GatewayEventLimitService;
import com.youxin.risk.gateway.task.xxljob.limit.GatewayBatchMessageLogProcessJob;
import com.youxin.risk.gateway.task.xxljob.limit.GatewayMessageLogProcessJob;
import com.youxin.risk.gateway.vo.GatewayMessageLog;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class GwMessageLogTest {

    @Resource(name = "gatewayMessageLogService")
    private GatewayEventLimitService gatewayMessageLogService;

    @Resource(name = "gatewayBatchMessageLogService")
    private GatewayEventLimitService gatewayBatchMessageLogService;

    @Autowired
    private GatewayBatchMessageLogProcessJob gatewayBatchMessageLogProcessJob;

    @Autowired
    private GatewayMessageLogProcessJob gatewayMessageLog;

    @Test
    public void insert(){
        Event event = new Event();
        event.setSessionId("4548940609768472950_3903868232204214363");
        event.setUserKey("b7a6390c22664e6805fbbebb752a30eb");
        event.setEventCode("hhVerify");
        event.setLoanKey("hh_45489408472950_390386");
//        gatewayMessageLogService.insert(event,"message");
        gatewayBatchMessageLogService.insert(event,"message");
    }


    @Test
    public void getList(){
//        List<GatewayMessageLog> list = gatewayMessageLogService.getList(10);
        List<GatewayMessageLog> list = gatewayBatchMessageLogService.getList(10);
        list.forEach(gatewayMessageLog -> {
            System.out.println(gatewayMessageLog.getSessionId());
        });
    }

    @Test
    public void deleteById(){
//        gatewayMessageLogService.deleteById(1L);
        gatewayBatchMessageLogProcessJob.execJobHandler("");
    }

    @Test
    public void deleteById2(){
//        gatewayMessageLogService.deleteById(1L);
        gatewayMessageLog.execJobHandler("");
    }
}
