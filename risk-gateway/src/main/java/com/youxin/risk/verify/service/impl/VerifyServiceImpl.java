package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.model.verify.VerifySubmit;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.service.verify.VerifyResultService;
import com.youxin.risk.commons.utils.*;
import com.youxin.risk.verify.enums.*;
import com.youxin.risk.verify.model.OperationLog;
import com.youxin.risk.verify.model.VerifyStrategyResultVo;
import com.youxin.risk.verify.mongodao.VerifyStrategyResultDao;
import com.youxin.risk.verify.service.*;
import com.youxin.risk.verify.utils.UUIDUtil;
import com.youxin.risk.commons.utils.UnderscoreFieldMapper;
import com.youxin.risk.verify.vo.*;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;


@Service
@Transactional
public class VerifyServiceImpl implements VerifyService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyServiceImpl.class);


    private final static String FLOW_TYPE_SPLIT = "split";


    @Autowired
    private VerifyNotifyService notifyService;

    @Autowired(required = false)
    @Qualifier("cacheRedisService")
    private RedisService redisService;

    @Autowired
    private VerifyLibraryService verifyLibraryService;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private VerifyResultService verifyResultService;

    @Autowired
    private VerifySubmitService verifySubmitService;
    @Autowired
    private SubmitService submitService;

    @Autowired
    private GatewaySystemService gatewaySystemService;

    @Autowired
    private VerifyStrategyResultDao verifyStrategyResultDao;

    @Autowired
    private VerifyOptionalAuthService optionalAuthService;

    @Autowired
    private PreAResultService preResultService;

    private final static String EVENT_CODE = "haoHuanVerify";

    public static String DATE_TIME_FORMAT_PATTERN_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS";

    @Override
    public void gatewayCallback(JSONObject param,
                                String callBackDomain,
                                String callBackUrl) {
        String flowType = param.getString("flowType");
        //judge flow type
        if (FLOW_TYPE_SPLIT.equals(flowType)) {
            //deal split flow
            this.splitFlowCallback(param, callBackDomain, callBackUrl);
        } else {
            LOG.warn("gateway callback flow type error,msg={}", param.toJSONString());
        }

    }

    @Override
    public void gatewayAmountCallback(JSONObject param,
                                      String callBackDomain,
                                      String callBackUrl) {
        String flowType = param.getString("flowType");
        //judge flow type
        if (VerifyConstants.FLOW_TYPE_SPLIT_AMOUNT.equals(flowType)) {
            //deal split flow
            this.splitFlowAmountCallback(param, callBackDomain, callBackUrl);
        } else {
            LOG.warn("gateway amount callback flow type error,msg={}", param.toJSONString());
        }
    }

    private void splitFlowAmountCallback(JSONObject param, String callBackDomain, String callBackUrl) {
        // 回调上游
        try {
            notifyService.notifyLineMangResult(VerifyChannelConstants.HAO_HUAN,
                    getNotifyAmount(param),
                    callBackDomain,
                    callBackUrl);
        } catch (Exception e) {
            LOG.error("notify amount callback msg error, param={}", param.toJSONString(), e);
            throw new RuntimeException(e);
        }
    }

    public VerifyUserLineManagementVo getNotifyAmount(JSONObject param) {
        try {
            VerifyUserLineManagementBakVo verifyUserLineManagementVo = JSON.parseObject(param.toString(), VerifyUserLineManagementBakVo.class);
            VerifyUserLineManagementVo result = ObjectTransferUtils.transferObject(verifyUserLineManagementVo, VerifyUserLineManagementVo.class);
            result.setRequestId(result.getSessionId());
            fillApiField(result, param);
            return result;
        } catch (Exception e) {
            LOG.error("trans amount callback msg error, param={}", param.toJSONString(), e);
        }
        return null;
    }

    private void fillApiField(VerifyUserLineManagementVo result, JSONObject param) {
        try {
            result.setApiLine(param.getDouble("apiLine"));
            result.setApiAvailLine(param.getDouble("apiAvailLine"));
            result.setApiUtilLine(param.getDouble("apiUtilLine"));
            result.setApiOutputTag(param.getString("apiOutputTag"));
            result.setApiPeriodLineRate(param.getString("apiPeriodLineRate"));
        }catch (Exception e){
            LOG.error("fillApiField error:", e);
        }
    }

    private void splitFlowCallback(JSONObject obj, String callBackDomain, String callBackUrl) {

        ResultAppNotifyVo notifyVo = JsonUtils.toObject(obj.toJSONString(), ResultAppNotifyVo.class);
        if (Boolean.TRUE.equals(obj.getBoolean("isManual"))) {
            delSubmitAndaddManualFlag(notifyVo.getLoanId() + "");
            //进入人工，人工通知后回调
            LOG.info("gatewayCallback enter manual,loanKey={}", obj.get("loanKey"));
            return;
        }

        JSONObject currLineManagement = obj.getJSONObject("currLineManagement");
        if (currLineManagement != null) {
            VerifyUserLineManagement management = JSON.parseObject(currLineManagement.toString(), VerifyUserLineManagement.class);
            try {
                VerifyUserLineManagementVo managementVo = ObjectTransferUtils.transferObject(management, VerifyUserLineManagementVo.class);

                notifyVo.setCurrLineManagement(managementVo);
            } catch (Exception e) {
                LOG.error("parse userlineManagement error,message={}", obj, e);
            }
        }

        Boolean isPassed = obj.getBoolean("isPassed");
        if (isPassed == null) {
            isPassed = false;
        }

        // 从策略输出的reasonCode下，取userLevel，然后设置到第一层级；不管通过还是拒绝，业务方使用第一层级的userLevel
        notifyVo.setUserLevel((String) notifyVo.getReasonCode().get("userLevel"));
        if(JsonGrayFeatureUtil.checkGrayFeature(TypeUtils.castToString(obj.get("userKey")))){
            if (notifyVo.getReason_code() != null && notifyVo.getReason_code().containsKey("user_level")) {
                notifyVo.setUser_level((String) notifyVo.getReason_code().get("user_level"));
            }
        }

        this.notifyService.notifyVerifyResult(isPassed, notifyVo, VerifyChannelConstants.HAO_HUAN, callBackDomain, callBackUrl);
    }

    private void delSubmitAndaddManualFlag(String loanId) {
        String hashKey = RedisKeyConstants.submit_apply_loanId_hash.name();
        String manualHashKey = RedisKeyConstants.manual_loanId_hash.name();
        for (int i = 0; i < 3; i++) {
            try {
                redisService.hdel(hashKey, loanId);
                String time = String.valueOf(System.currentTimeMillis());
                redisService.hset(manualHashKey, loanId, time);
                LOG.info(hashKey + " delete flag, loanId=" + loanId);
                break;
            } catch (Exception e) {
                LOG.warn(hashKey + "Exception, loanId=" + loanId, e);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e1) {
                    // nothing
                }
            }
        }
    }

    @Override
    public List<String> submitApply(final VerifySubmitVo submitVo) throws Exception {
        // 用户提交审核
        //1、生成操作log
        //2、数据库记录
        //3、检查进件的有效性
        //4、返回重新进件的列表
        //5、如果进件列表是空的，发布提交审核通知
        List<String> result = new ArrayList<>();

        // 获取运营商标识
        boolean mobileFlag = false;
        // 检查是否所有需要提交的全部都提交
        for (OperationType type : OperationType.values()) {
            if (OperationType.getNoCheckType().contains(type)) {
                continue;
            }
            if (OperationType.MOBILE_OPERATOR.equals(type)) {
                if (submitVo.getUserStatusInfo() != null) {
                    VerifyOptionalAuthVo optvo = JsonUtils.toObject(submitVo.getUserStatusInfo(), VerifyOptionalAuthVo.class);
                    if (optvo != null) {
                        if (!StringUtils.isEmpty(optvo.getPhoneInfoStatus())) {
                            if ("1".equals(optvo.getPhoneInfoStatus())) {
                                mobileFlag = true;
                            }
                        }
                    }
                }
                // 判断运营商标志特异处理
                if (mobileFlag) {
                    this.verifyLibraryService.sendLibraryOutB001(submitVo.getUserKey());
                    continue;
                }
            }
            // 从dc获取上传记录
            DcOperationLog dcOperationLog = operationLogService.findLastOperationLogByUserAndOperationType(submitVo.getUserKey(), submitVo.getSourceSystem(), type.name());
            if (Objects.isNull(dcOperationLog)) {
                // 银行卡少量2018年的数据，dc查不到
                OperationLogVo verifyVo = operationLogService.getLastOperationLogByUserKeyAndTypeOld(submitVo.getUserKey(), submitVo.getSourceSystem(), type);
                if (Objects.nonNull(verifyVo)) {
                    dcOperationLog = new DcOperationLog();
                    dcOperationLog.setCreateTime(verifyVo.getCreateTime());
                } else {
                    LOG.info("dcOperationLogVo is null,userKey={},type={}", submitVo.getUserKey(), type.name());
                }
            }
            Boolean queryLogFromDc = NacosClientAdapter.getBooleanConfig("query.log.from.dc", false);
            if (!queryLogFromDc) {
                OperationLogVo vo = this.operationLogService
                        .getLastOperationLogByUserKeyAndType(submitVo.getUserKey(),
                                submitVo.getSourceSystem(), type);
                if (Objects.isNull(vo)) {
                    vo = operationLogService.getLastOperationLogByUserKeyAndTypeOld(submitVo.getUserKey(), submitVo.getSourceSystem(), type);
                }
                LOG.info("operationLogVo ={},userKey={},type={}", JSON.toJSONString(vo), submitVo.getUserKey(), type.name());
                if (vo == null) {
                    result.add(type.name());
                } else {
                    Date now = new Date();
                    Date lastOperationTime = vo.getCreateTime();
                    //已经超时
                    Long validateTime = type.getValidationTime();
                    // 如枚举中没有重写获取超时时间的方法，则不校验 0704
                    if (validateTime == null) {
                        continue;
                    }
                    if (now.getTime() - lastOperationTime.getTime() > validateTime) {
                        result.add(type.name());
                    }
                }
            } else {
                if (Objects.isNull(dcOperationLog)) {
                    LOG.info("dcOperationLogVo is null,userKey={},type={}", submitVo.getUserKey(), type.name());
                    result.add(type.name());
                } else {
                    LOG.info("dcOperationLogVo fetched,userKey={},type={}", submitVo.getUserKey(), type.name());
                    Date now = new Date();
                    Date lastOperationTime = dcOperationLog.getCreateTime();
                    //已经超时
                    Long validateTime = type.getValidationTime();
                    // 如枚举中没有重写获取超时时间的方法，则不校验 0704
                    if (validateTime == null) {
                        continue;
                    }
                    if (now.getTime() - lastOperationTime.getTime() > validateTime) {
                        result.add(type.name());
                    }
                }
            }
        }
        if (!mobileFlag) {
            // 根据上次结果寻找所有需要重新提交的部分。
            Set<String> opSet = this.verifyResultService.checkResubmitByUserKey(submitVo
                    .getUserKey());
            if (!opSet.isEmpty()) {
                VerifyResult lastResult = this.verifyResultService
                        .findLastVerifyResultByUserKey(submitVo.getUserKey());
                VerifySubmit submit = this.verifySubmitService
                        .findSubmitByLoan(lastResult.getLoanKey(), lastResult.getLoanId(), submitVo.getUserKey());

                Integer submitOpId = null;
                if (submit != null) {
                    submitOpId = Integer.valueOf(String.valueOf(submit.getOperationLogId()));
                }
                List<OperationLog> opLogList = this.operationLogService
                        .getOperationLogListAfterId(submitOpId, submitVo.getUserKey());
                if (opLogList == null) {
                    opLogList = Lists.newArrayList();
                }
                Set<String> newOpSet = new HashSet<>();
                for (OperationLog opLog : opLogList) {
                    newOpSet.add(opLog.getOperationType());
                }
                opSet.removeAll(newOpSet);
                result.addAll(opSet);
            }
        }
        //验证通过保存提交信息并调用RA
        if (result.isEmpty()) {
            boolean isPassed = true;// 记录当前进件是否在preA是否已经被拒
            VerifyStrategyResultVo resultVo = null;
            //生成loanKey
            if (StringUtils.isBlank(submitVo.getLoanKey())) {
                LOG.info("loanKey is null ,userKey={},loanId={}", submitVo.getUserKey(), submitVo.getLoanId());
                if (StringUtils.isNotBlank(submitVo.getRequestId())) {
                    submitVo.setLoanKey("hh_" + submitVo.getRequestId());
                } else {
                    submitVo.setLoanKey(this.genLoanKey(submitVo.getSourceSystem()));
                }
            } else {
                resultVo = this.findVerifyStrategyResult(submitVo.getLoanKey());
                LOG.info("mongo preA verify result={}", JSON.toJSONString(resultVo));
                // 如果pre A结果不为空，并且拒绝，发送kafka消息
                if (resultVo != null && !resultVo.getIsAutoPassed()) {
                    resultVo.setLoanId(submitVo.getLoanId());
                    isPassed = false;
                }
            }


            OperationLogVo opLogVo = this.operationLogService.getOpLogVoFromInput(
                    submitVo, OperationType.SUBMIT);
            Integer opLogId = this.operationLogService.saveOperationLog(opLogVo);
            if (opLogId == null) {
                return result;
            }

            if (VerifyChannelConstants.HAO_HUAN.name().equals(submitVo.getSourceSystem())) {
                submitVo.setStep(VerifyNode.A.name());
            }
            if (submitVo.getWifiLevel() != null) {
                submitVo.setWifiLevel(String.format("%s_%s", submitVo.getPlatform(), submitVo.getWifiLevel()));
            }
            if (submitVo.getBatteryPlugType() != null) {
                submitVo.setBatteryPlugType(String.format("%s_%s", submitVo.getPlatform(), submitVo.getBatteryPlugType()));
            }
            submitVo.setOperationLogId(Long.valueOf(String.valueOf(opLogId)));

            //过滤表情
            submitVo.setWifiSSID(StringUtils.filterEmoji(submitVo.getWifiSSID()));
            submitVo.setDeviceName(StringUtils.filterEmoji(submitVo.getDeviceName()));

            //保存可选认证
            this.saveOptionalAuth(submitVo);

            VerifySubmit verifySubmit = ObjectTransferUtils.transferObject(
                    submitVo, VerifySubmit.class);
            verifySubmit.setOperationLogId(Long.valueOf(String.valueOf(opLogId)));
            this.verifySubmitService.saveOrUpdateVerifySubmit(verifySubmit);

            if (!isPassed) {
                // 在preA拒件后，如果发生进件，将preA审核结果回调给业务方
                preResultService.sendPreAResultByThread(resultVo);
                // save to mysql
                this.savePreaRejectResultToDB(resultVo, submitVo.getSourceSystem());
                this.saveStrategyResultToMongo(resultVo);// 更新mongodb记录中的loanId
                // todo pa拒绝去黑
                verifyLibraryService.sendLibraryOutPaReject(resultVo);
                return result;
            }
            // 流量全部打到risk-gateway
            submitVo.setFlowType(FLOW_TYPE_SPLIT);
            String retMessage = this.callGateway(submitVo);
            if (retMessage != null) {
                try {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                } catch (Exception ex) {
                    LOG.error("setRollbackOnly error", ex);
                }
                result.add(retMessage);
                return result;
            }
            //推数据到dc
            LOG.info("submitInformationToDc startTime={}", DateFormatUtils.format(new Date(), DATE_TIME_FORMAT_PATTERN_MILLIS));
            submitService.submitInformationToDc(submitVo, SubmitDataType.VERIFY_SUBMIT, submitVo.getUserKey());
            LOG.info("submitInformationToDc endTime={}", DateFormatUtils.format(new Date(), DATE_TIME_FORMAT_PATTERN_MILLIS));
        }
        return result;
    }

    private String genLoanKey(String sourceSystem) {
        if (VerifyChannelConstants.HAO_HUAN.name().equals(sourceSystem)) {
            return "hh_" + UUIDUtil.generateUUID();
        } else if (VerifyChannelConstants.PAY_DAY_LOAN.name().equals(sourceSystem)) {
            return "pdl_" + UUIDUtil.generateUUID();
        }

        return UUIDUtil.generateUUID();
    }


    /**
     * 查询策略结果
     */
    @Override
    public VerifyStrategyResultVo findVerifyStrategyResult(String loanKey) {

        int retryCount = 0;
        while (retryCount < 3) {
            try {
                return this.verifyStrategyResultDao.getByLoanKey(loanKey);
            } catch (Exception e) {
                if (retryCount == 2) {
                    LOG.error("query verify strategy result from mongo error,loanKey={}", loanKey, e);
                } else {
                    LOG.warn("query verify strategy result from mongo warning,loanKey={}", loanKey, e);
                }

            }
            retryCount++;
        }

        return null;
    }

    /**
     * 保存提交的可选认证信息
     *
     * @param submitVo
     */
    private void saveOptionalAuth(VerifySubmitVo submitVo) {

        if (org.apache.commons.lang3.StringUtils.isBlank(submitVo.getUserStatusInfo())) {
            return;
        }

        VerifyOptionalAuthVo vo = JsonUtils.toObject(submitVo.getUserStatusInfo(), VerifyOptionalAuthVo.class);
        if (vo != null) {
            this.optionalAuthService.save(submitVo.getUserKey(), vo);
            if (vo.getUninopayValid() == Boolean.TRUE) {
                submitVo.setUserStatusInfo(JsonUtils.toJson(vo));
            }
        }
    }

    private void savePreaRejectResultToDB(VerifyStrategyResultVo resultVo, String sourceSystem) {
        try {
            LOG.info("submitApply save prea reject result to db,loanKey={},loanId={},resultVo={}",
                    resultVo.getLoanKey(),
                    resultVo.getLoanId(), JSON.toJSONString(resultVo));
            VerifyResult result = new VerifyResult();
            result.setAutoVerifyTime(new Date());
            result.setIsAutoPassed(resultVo.getIsAutoPassed());
            result.setLoanId(resultVo.getLoanId());
            result.setLoanKey(resultVo.getLoanKey());
            result.setUserKey(resultVo.getUserKey());
            result.setSourceSystem(sourceSystem);
            result.setReasonCode(resultVo.getReasonCode());
            result.setStrategyId(resultVo.getStrategyId());
            result.setScore(resultVo.getScore());
            result.setScore2(resultVo.getScore2());
            result.setLevel(resultVo.getLevel());
            result.setSegment(resultVo.getSegment());
            result.setStep(resultVo.getStep());
            result.setLoanAmount(resultVo.getLoanAmount());
            result.setLoanPeriodNos(resultVo.getLoanPeriodNos());
            result.setLoanRate(resultVo.getLoanRate());
            result.setPeriodAmount(resultVo.getPeriodAmount());
            result.setBtAmount(resultVo.getBtAmount());
            result.setBtPeriodNos(resultVo.getBtPeriodNos());
            result.setBtRate(resultVo.getBtRate());
            result.setTotalAmount(resultVo.getTotalAmount());
            result.setIsManual(resultVo.getIsManual());
            result.setReasonCodeUser(resultVo.getReasonCodeUser());
            result.setUserLevelId(resultVo.getUserLevelId());
            result.setIsReduceAmountPass(resultVo.getIsReduceAmountPass());
            result.setNewAmount(resultVo.getNewAmount());
            result.setNewAmountExpiry(resultVo.getNewAmountExpiry());
            result.setAutoLockDays(resultVo.getAutoLockDays());
            result.setFinalVerifyTime(new Date());
            result.setIsFinalPassed(resultVo.getIsFinalPassed());

            this.verifyResultService.saveOrUpdateVerifyResult(result);
        } catch (Exception e) {
            LOG.error("save prea reject result to db error, loanKey={}, loanId={}", resultVo.getLoanKey(),
                    resultVo.getLoanId(), e);
        }
    }

    public Boolean saveStrategyResultToMongo(VerifyStrategyResultVo vo) {
        int retryCount = 0;
        while (retryCount < 3) {
            try {
                if (vo.getId() == null) {
                    LOG.info("save verify strategy result to mongo,data={}", JsonUtils.toJson(vo));
                    this.verifyStrategyResultDao.insert(vo);
                } else {
                    LOG.info("update verify strategy result to mongo,data={}", JsonUtils.toJson(vo));
                    this.verifyStrategyResultDao.update(vo);
                }

                return true;
            } catch (Exception e) {
                if (retryCount == 2) {
                    LOG.error("save verify strategy result to mongo error,data={}", JsonUtils.toJson(vo), e);
                } else {
                    LOG.warn("save verify strategy result to mongo warning,data={}", JsonUtils.toJson(vo), e);
                }

            }
            retryCount++;
        }

        return false;

    }

    private String callGateway(VerifySubmitVo submitVo) {
        String retMessage = null;
        try {
            submitVo.setEventCode(EVENT_CODE);
            retMessage = this.gatewaySystemService.callGatewayResult(submitVo, VerifyConstants.GW_ICODE_VERIFY);

            if (null == retMessage) {
                LOG.info("call gateway success,userKey={},loanId={}", submitVo.getUserKey(), submitVo.getLoanId());

            } else {
                LOG.warn("call gateway error,userKey={},loanId={}", submitVo.getUserKey(), submitVo.getLoanId());
            }
        } catch (Exception e) {
            LOG.warn("call gateway exception,userKey={},loanId={}", submitVo.getUserKey(), submitVo.getLoanId());
        }
        return retMessage;
    }

}
