package com.youxin.risk.verify.enums;


public class VerifyConstants {

    public static final String INFORMATION_VALIDATE_TIME = "INFORMATION_VALIDATE_TIME";

    public static final String SOURCE_SYSTEM_ID = "SOURCE_SYSTEM_ID";

    public static final String HOLD_MINUTE = "HOLD_MINUTE";

    public static final String NEED_MANUAL_CHECK = "NEED_MANUAL_CHECK";

    public static final String AES_PASSWORD = "AES_PASSWORD";

    public static final String DEFAULT_VERIFY_LOCK_DAYS = "DEFAULT_VERIFY_LOCK_DAYS";

    public static final String ALL_REJECT = "ALL_REJECT";

    public static final String AB_TEST_ON = "AB_TEST_ON";

    public static final String NOTIFY_ON_HOLD = "NOTIFY_ON_HOLD";

    public static final String NOTIFY_HOLD_MINUTE = "NOTIFY_HOLD_MINUTE";

    public static final String DELAY_SWITCH = "DELAY_SWITCH";

    public static final String LINE_MIDDLE_SWITCH = "LINE_MIDDLE_SWITCH";

    public static final String TRANS_MID_VERIFY_XML_SWITCH = "TRANS_MID_VERIFY_XML_SWITCH";

    public static final String DELAY_PERIOD = "DELAY_PERIOD";

    public static final String STRATEGY_MIGRATE_RATE = "STRATEGY_MIGRATE_RATE";

    public static final String HAOHUAN_RM_STRATEGY_SWITCH = "HAOHUAN_RM_STRATEGY_SWITCH";

    public static final String STRATEGY_MIGRATE_BATCH_2_RATE = "STRATEGY_MIGRATE_BATCH_2_RATE";

    public static final String HAOHUAN_RM_STRATEGY_BATCH_2_SWITCH = "HAOHUAN_RM_STRATEGY_BATCH_2_SWITCH";

    public static final String STRATEGY_MIGRATE_BATCH_3_RATE = "STRATEGY_MIGRATE_BATCH_3_RATE";

    public static final String HAOHUAN_RM_STRATEGY_BATCH_3_SWITCH = "HAOHUAN_RM_STRATEGY_BATCH_3_SWITCH";

    public static final String REDIS_UPGRADE_STAGE = "REDIS_UPGRADE_STAGE";

    public static final String VERIFY_AMOUNT = "VERIFY_USER_AMOUNT_";

    public static final String VERIFY_AMOUNT_WRITE_BACK = "VERIFY_USER_AMOUNT_WRITE_BACK_";

    public static final Integer VERIFY_AMOUNT_LOCK_TIME = 300;

    public static final Integer DEFAULT_NEW_AMOUNT_VALID_DAYS = 30;

    //交易回调补偿锁定KEY
    public static final String REDIS_KEY_TRANS_LOCK = "VERIFY_TRANS_LOCK_KEY_";

    //交易回调补偿锁定时间
    public static final Integer REDIS_TRANS_LOCK_TIME = 600;

    public static final Integer LAST_FOUR_CHARS = 4;

    public static final Integer MOD = 5;


    // 线下额度策略结果提交默认秘钥
    public static final String DEFAULT_MANUAL_LINE_SIGN = "d49c2fcd4230d0a42e4a42f21ef1f85b";

    public static final String AMOUNT_FEATURE_STEP = "AMOUNT_A"; //新增额度fs生成feature step

	public static final String MID_AMOUNT_FEATURE_STEP = "MID_VERIFY_A"; //新增贷中审核fs生成feature step

	public static final String ASSIGN_AMOUNT_FEATURE_STEP = "HH_AMOUNT_ASSIGN_A"; //新增进件额度fs生成feature step

	public static final String AMOUNT_FEATURE_STEP_URL = "data"; //amountA data路径

	public static final String MID_AMOUNT_FEATURE_STEP_URL = "data.thirdPartyData"; //midVerifyA data路径

	public static final String AMOUNT_FEATURE_THIRD_URL = "thirdPartyData"; //amountA 三方路径

	public static final String AMOUNT_DATA_FEATURE_URL = "userLineService"; //用户额度数据 data路径

	public static final String LINE_MIDDLE_CREATE_ACCB_SWITCH = "LINE_MIDDLE_CREATE_ACCB_SWITCH";

    public static final Integer LINE_MIDDLE_CREATE_ACCB_END_DAY = 28;

    public static final Integer LINE_MIDDLE_CREATE_PAY_OFF_MONTH = 3; //查询T-3月对应日期已结清用户

    public static final Integer BAIRONG_DATA_CUST_VALID_DATE = 90; // 百融个人资质缓存数据有效期

	public static final String VERIFY_LIBRARY_COMMAND_IN = "VERIFY_LIBRARY_COMMAND_IN"; //名单请求类别,in

	public static final String VERIFY_LIBRARY_COMMAND_OUT = "VERIFY_LIBRARY_COMMAND_OUT"; //名单请求类别,out

	public static final String VERIFY_LIBRARY_POLICY_CODE_IN_B001 = "POLICY_010201"; //B001入名单策略码

	public static final String VERIFY_LIBRARY_POLICY_CODE_OUT_B001 = "POLICY_010202"; //B001出名单策略码

	public static final String VERIFY_LIBRARY_POLICY_CODE_IN_PA_REJECT = "POLICY_010203"; //PA入名单策略码

	public static final String VERIFY_LIBRARY_POLICY_CODE_OUT_PA_REJECT = "POLICY_010204"; //PA出名单策略码

	public static final String VERIFY_LIBRARY_POLICY_CODE_NAME = "policyCode"; // 策略码key

	public static final String VERIFY_LIBRARY_KEYWORD_B001 = "B001_RTEL_INVD"; // B001对应reasonCode key

	public static final String SEND_SHADOW_MSG_KEYWORD_USER_LINE = "UserLine"; // send分析库额度数据关键字

    public static final String FLOW_TYPE_IMAGE_AMOUNT = "imageAmount";

    public static final String FLOW_TYPE_SPLIT_AMOUNT = "splitAmount";

    public static final String EVENT_CODE_REFUND = "haoHuanAmountRefund"; // 退货额度eventCode

    public static final String EVENT_CODE_OVERDUE = "haoHuanAmountOverdue"; // 逾期额度eventCode

    public static final String EVENT_CODE_LEND = "haoHuanAmountLend"; // 放款额度eventCode

    public static final String GW_ICODE_VERIFY = "0100030001";

    public static final String GW_ICODE_AMOUNT = "0100030002";

    public static final String LOAD_ID_AMOUNT_KEY_SUFFIX = "_amount"; //额度标记后缀

}
