package com.youxin.risk.verify.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.verify.enums.MetricsPointEnum;
import com.youxin.risk.verify.enums.VerifyChannelConstants;
import com.youxin.risk.verify.service.VerifyService;
import com.youxin.risk.verify.vo.JsonResultVo;
import com.youxin.risk.verify.vo.VerifySubmitVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/verify")
public class VerifyController {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyController.class);
    public static final int DEVICE_NAME_LENGTH = 100;

    public static String DATE_TIME_FORMAT_PATTERN_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS";

    @Autowired
    private VerifyService verifyService;

    @RequestMapping("/submit")
    @ResponseBody
    public JsonResultVo verifySubmit(@RequestBody JSONObject param) {
        long start = System.currentTimeMillis();
        try {
            LOG.info("VerifyController_api_verifySubmit, verfiy submit : [{}]", param);

            VerifySubmitVo submitVo = JSON.toJavaObject(param, VerifySubmitVo.class);
            //参数校验 loan_id
            if(submitVo.getLoanId() == null){
                point(MetricsPointEnum.verify_review_response, JsonResultVo.ERROR, start);
                LOG.info("verifySubmitApplyFailed,loanId is null");
                return JsonResultVo.error().addData("missingParameters","loanId");
            }

            String deviceName = submitVo.getDeviceName();
            if (deviceName.length() > DEVICE_NAME_LENGTH) {
                LoggerProxy.warn("deviceNameTooLong", LOG, "truncate to {} char, deviceName: {}", DEVICE_NAME_LENGTH, deviceName);
                submitVo.setDeviceName(deviceName.substring(0, DEVICE_NAME_LENGTH));
            }

            this.checkChannelCode(submitVo);
            LOG.info("submitApply startTime={}", DateFormatUtils.format(new Date(),DATE_TIME_FORMAT_PATTERN_MILLIS));
            List<String> resubmitList = this.verifyService.submitApply(submitVo);
            LOG.info("submitApply endTime={}", DateFormatUtils.format(new Date(),DATE_TIME_FORMAT_PATTERN_MILLIS));

            point(MetricsPointEnum.verify_review_response, JsonResultVo.SUCCESS, start);
            long end = System.currentTimeMillis();
            LOG.info("verifySubmit cost={}",end-start);

            if (!CollectionUtils.isEmpty(resubmitList)) {
                LOG.info("verifySubmitApplyFailed,needResubmit");
            } else {
                LOG.info("verifySubmitApplySuccess");
            }
            return JsonResultVo.success().addData("resubmitList", resubmitList);

        } catch (Exception e) {
            LOG.error("verify submit error param={}",param, e);
            LOG.info("verifySubmitApplyFailed,exception occur");
            point(MetricsPointEnum.verify_review_response, JsonResultVo.ERROR, start);
            return JsonResultVo.error(-1, e.getMessage());
        }
    }


    protected VerifyChannelConstants checkChannelCode(VerifyCommonData commonVo) {

        if(VerifyChannelConstants.HAO_HUAN.getChannelCode().equals(commonVo.getSourceSystem())){
            return VerifyChannelConstants.HAO_HUAN;
        }

        VerifyChannelConstants channel = VerifyChannelConstants
                .getConstantsByChannelCode(commonVo.getChannel());
        commonVo.setSourceSystem(channel.name());
        return channel;
    }

    private void point(MetricsPointEnum point, int status, long start) {
        if(null == point) {
            return;
        }
        Map<String, String> tags = new HashMap<>();
        tags.put("retCode", String.valueOf(status));
        MetricsAPI.point(point.name(), tags,System.currentTimeMillis()-start);
    }

}
