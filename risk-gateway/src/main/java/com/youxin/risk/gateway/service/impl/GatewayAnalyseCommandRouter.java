package com.youxin.risk.gateway.service.impl;

import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import com.youxin.risk.gateway.remote.client.EngineServiceClient;
import com.youxin.risk.gateway.service.GatewayEventLimitService;
import com.youxin.risk.gateway.service.GatewayRouter;
import com.youxin.risk.gateway.service.GwRequestModelService;
import com.youxin.risk.gateway.service.limiter.ApolloLimitParser;
import com.youxin.risk.gateway.task.xxljob.limit.GatewayBatchMessageLogProcessJob;
import com.youxin.risk.gateway.task.xxljob.limit.GatewayMessageLogProcessJob;
import com.youxin.risk.gateway.vo.GatewayVo;
import com.youxin.risk.verify.service.RedisService;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.GW_SPACE;

/**
 * <AUTHOR>
 */
@Service("gatewayAnalyseCommandRouter")
public class GatewayAnalyseCommandRouter implements GatewayRouter, InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(GatewayAnalyseCommandRouter.class);

    @Resource(name = "gatewayMessageLogService")
    private GatewayEventLimitService gatewayMessageLogService;
    @Resource(name = "gatewayBatchMessageLogService")
    private GatewayEventLimitService gatewayBatchMessageLogService;
    @Autowired
    private GwRequestModelService gwRequestModelService;
    @Autowired
    private EngineServiceClient engineServiceClient;
    @Autowired
    private RedisService redisService;

    private final Map<String, IEventLimiterHandler> eventHandlerMap = new HashMap<>();

    private final static String EVENT_LIMIT = "eventLimit";
    private final static String BATCH_EVENT_LIMIT = "batchEventLimit";
    private final static String NOT_LIMIT = "notLimit";

    @Override
    public void afterPropertiesSet() {
        eventHandlerMap.put(EVENT_LIMIT, new EventLimitHandler());
        eventHandlerMap.put(BATCH_EVENT_LIMIT, new BatchEventLimitHandler());
        eventHandlerMap.put(NOT_LIMIT, new NotLimitHandler());
    }

    @Override
    public void route(GatewayVo gwvo) {
        String eventJson = JacksonUtil.toJson(gwvo.getEvent());
        LoggerProxy.info("writeEventToEngine", LOGGER, "event={}", eventJson);
        String eventCode = gwvo.getEvent().getEventCode();
        String userKey = gwvo.getEvent().getUserKey();
        List<String> testUserKeyList = NacosClientAdapter.getListConfig("test.userKey", String.class);
        String limitType = SpecialEventHandler.isNeedSpecialHandle(eventCode) ?
                SpecialEventHandler.getLimitType(gwvo) : evaluatorLimitType(eventCode);
        LoggerProxy.info("eventLimitType", LOGGER, "eventCode={}, limitType={}", eventCode, limitType);
        IEventLimiterHandler eventHandler = eventHandlerMap.get(limitType);
        //特殊处理 为了上云正式测试
        if (!testUserKeyList.isEmpty() && testUserKeyList.contains(userKey)){
           eventHandler = eventHandlerMap.get(NOT_LIMIT);
        }
        eventHandler.handle(gwvo);
        buildRes(gwvo, RetCodeEnum.SUCCESS.getRetMsg());
    }

    /**
     * 限流类型判断
     * 限流方式: 将消息入库，等待任务将数据加载并发送至engine；可以根据不同事件限流，防止部分事件流量过大压垮engine。
     * <p>
     * 支持一下三种:
     * 1. {@link GatewayAnalyseCommandRouter#EVENT_LIMIT}  非跑批类事件限流
     * 2. {@link GatewayAnalyseCommandRouter#BATCH_EVENT_LIMIT} 跑批类事件限流
     * 3. {@link GatewayAnalyseCommandRouter#NOT_LIMIT} 不限流
     */
    private String evaluatorLimitType(String eventCode) {
        Set<String> eventLimitSet = NacosClientAdapter.getMapConfig("event.limit.map", Integer.class).keySet();
        Set<String> batchEventLimitSet = NacosClientAdapter.getMapConfig("batch.event.limit.map", Integer.class).keySet();
        if (eventLimitSet.contains(eventCode)) {
            return EVENT_LIMIT;
        } else if (batchEventLimitSet.contains(eventCode)) {
            return BATCH_EVENT_LIMIT;
        } else {
            return NOT_LIMIT;
        }
    }

    /**
     * 需要特殊处理的事件
     */
    private static class SpecialEventHandler {
        private static final List<String> EVENT_LIST = Arrays.asList("haoHuanAmountRepayPayOff", "haoHuanAmountRepay");

        private static boolean isNeedSpecialHandle(String eventCode) {
            return EVENT_LIST.contains(eventCode);
        }

        static String getLimitType(GatewayVo gwvo) {
            String eventCode = gwvo.getEvent().getEventCode();
            switch (eventCode) {
                case "haoHuanAmountRepayPayOff":
                case "haoHuanAmountRepay":
                    return handleAmountRepay(gwvo);
                default:
                    throw new UnsupportedOperationException("event [" + eventCode + "] no special handle");
            }
        }

        private static String handleAmountRepay(GatewayVo gwvo) {
            String isBatch = gwvo.getEvent().getString("isBatch");
            if ("1".equals(isBatch)) {
                return BATCH_EVENT_LIMIT;
            } else {
                return EVENT_LIMIT;
            }
        }
    }

    private interface IEventLimiterHandler {
        /**
         * 处理逻辑
         *
         * @param gwvo 入参
         */
        void handle(GatewayVo gwvo);
    }

    /**
     * 限流后通过定时任务控制流量
     *
     * @see GatewayMessageLogProcessJob
     */
    private class EventLimitHandler implements IEventLimiterHandler {

        @Override
        public void handle(GatewayVo gwvo) {
            gwvo.getEvent().set(EventVariableKeyEnum.isBatchEvent.name(), false);
            String eventCode = gwvo.getEvent().getEventCode();
            //云上处理
            List<String> notifyEventCodeList = NacosClientAdapter.getListConfig("cloud.eventcode.fund", String.class);
            int maxThreshold = NacosClientAdapter.getIntConfig("isCloudSplit", 99);
            if (notifyEventCodeList.contains(eventCode) && RandomUtils.nextInt(1, 101) > maxThreshold){
                engineServiceClient.sendEvent(gwvo.getEvent());
                gwRequestModelService.updateSended(gwvo.getSessionId());
                return;
            }
            // Redis限流机制：对所有配置了限流的事件生效
            Set<String> eventLimitSet = NacosClientAdapter.getMapConfig("event.limit.map", Integer.class).keySet();
            if (eventLimitSet.contains(eventCode)) {
                try {
                    long c = redisService.incrBy(GatewayMessageLogProcessJob.flowLimitPrefix + eventCode, -1);
                    LoggerProxy.info("remainLimit", LOGGER, "{}, eventCode={}", c, eventCode);

                    if (c >= 0) {
                        engineServiceClient.sendEvent(gwvo.getEvent());
                        gwRequestModelService.updateSended(gwvo.getSessionId());
                        return;
                    }
                } catch (Exception e) {
                    LoggerProxy.info("getRemainLimitFromRedisError", LOGGER, "eventCode={}", eventCode, e);
                    // Redis出错降级为写库模式
                }
            }

            gatewayMessageLogService.insert(gwvo.getEvent(), getEventJson(gwvo));
        }
    }

    /**
     * 限流后通过定时任务控制流量
     *
     * @see GatewayBatchMessageLogProcessJob
     */
    private class BatchEventLimitHandler implements IEventLimiterHandler {

        @Override
        public void handle(GatewayVo gwvo) {
            gwvo.getEvent().set(EventVariableKeyEnum.isBatchEvent.name(), true);
            gatewayBatchMessageLogService.insert(gwvo.getEvent(), getEventJson(gwvo));
        }
    }

    private class NotLimitHandler implements IEventLimiterHandler {

        @Override
        public void handle(GatewayVo gwvo) {
            ApolloLimitParser delayHandleEvents = new ApolloLimitParser(GW_SPACE, "delay.handle.events");
            if (delayHandleEvents.parse().containsKey(gwvo.getEvent().getEventCode())) {
                LoggerProxy.error("thisEventNotSupportDelay", LOGGER, "eventCode={}", gwvo.getEvent().getEventCode());
            }
            // 直接调用engine处理
            gwvo.getEvent().set(EventVariableKeyEnum.isBatchEvent.name(), false);
            engineServiceClient.sendEvent(gwvo.getEvent());
            gwRequestModelService.updateSended(gwvo.getEvent().getSessionId());
        }
    }


    private void buildRes(GatewayVo gwvo, String retMsg) {
        Map<String, String> res = new HashMap<>(2);
        res.put(EventVariableKeyEnum.retCode.name(), RetCodeEnum.SUCCESS.getValue());
        res.put(EventVariableKeyEnum.retMsg.name(), null == retMsg ? "" : retMsg);
        gwvo.setRetCode(RetCodeEnum.SUCCESS);
        gwvo.setRetMsg(null == retMsg ? "" : retMsg);
        gwvo.setResultPlaintext(JacksonUtil.toJson(res));
    }

    private String getEventJson(GatewayVo gwvo) {
        return JacksonUtil.toJson(gwvo.getEvent());
    }
}