package com.youxin.risk.gateway.service.sec;

import com.youxin.risk.gateway.vo.GatewayVo;

public class SecLevel10ServiceImpl implements SecService {

    private volatile static SecLevel10ServiceImpl instance;

    private SecLevel10ServiceImpl() {

    }

    public static SecLevel10ServiceImpl getInstance() {
        if (null == instance) {
            synchronized (SecLevel10ServiceImpl.class) {
                if (null == instance) {
                    instance = new SecLevel10ServiceImpl();
                }
            }
        }
        return instance;
    }

    @Override
    public void decrypt(GatewayVo gwvo) throws Exception {
        throw new AbstractMethodError();
    }

    @Override
    public void encrypt(GatewayVo gwvo) throws Exception {
        throw new AbstractMethodError();
    }
}