package com.youxin.risk.gateway.task.xxljob.limit;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.limiter.ApolloRateLimitParser;
import com.youxin.risk.commons.limiter.EventLimitTypeEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.gateway.contants.GwRequestStatus;
import com.youxin.risk.gateway.remote.client.EngineServiceClient;
import com.youxin.risk.gateway.service.GatewayEventLimitService;
import com.youxin.risk.gateway.service.GwRequestModelService;
import com.youxin.risk.gateway.service.SystemAutoLimiterSupport;
import com.youxin.risk.gateway.service.limiter.ApolloLimitParser;
import com.youxin.risk.gateway.service.limiter.impl.ApolloConfigLimiter;
import com.youxin.risk.gateway.service.limiter.impl.ApolloConfigRateLimiter;
import com.youxin.risk.gateway.service.limiter.impl.ApolloConfigSentLimiter;
import com.youxin.risk.gateway.service.limiter.impl.SystemAutoLimiter;
import com.youxin.risk.gateway.task.xxljob.limit.condition.EventDelayConditionBuilder;
import com.youxin.risk.gateway.task.xxljob.limit.condition.EventLimitQueryConditionBuilder;
import com.youxin.risk.gateway.task.xxljob.limit.condition.MessageLogQueryCondition;
import com.youxin.risk.gateway.vo.GatewayMessageLog;
import com.youxin.risk.verify.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.GW_SPACE;

/**
 * gateway接受到消息之后会把指定事件存到数据库，由任务来触发流程的执行。
 * 一旦下游系统出现问题，将任务关闭，消息直接入库，避免造成线上大量卡单及重试。
 *
 * <AUTHOR>
 */
@Component
public class GatewayMessageLogProcessJob implements XxlJobBase {
    public static final String flowLimitPrefix = "flowLimit-";
    private static final Logger LOGGER = LoggerFactory.getLogger(GatewayMessageLogProcessJob.class);

    @Resource(name = "gatewayMessageLogService")
    private GatewayEventLimitService gatewayMessageLogService;

    @Autowired
    private GwRequestModelService gwRequestModelService;
    @Autowired
    private RedisService redisService;

    @Autowired
    protected EngineServiceClient engineServiceClient;

    @Autowired
    private SystemAutoLimiterSupport systemAutoLimiterSupport;

    @Resource(name = "gatewayCallEngineThreadPool")
    private ThreadPoolTaskExecutor gatewayCallEngineThreadPool;
    @Resource(name = "messageLogQueryDbThreadPool")
    private ThreadPoolTaskExecutor queryDataFromDbThreadPool;


    @Override
    @XxlJob(value = "gatewayMessageLogProcessJob")
    public ReturnT<String> execJobHandler(String param) {

        LOGGER.info("process gatewayMessageLog start");

        try {
            //事件 eventCode 对应的限流条数
            ApolloLimitParser apolloLimitParser = new ApolloLimitParser(ApolloNamespaceEnum.GW_SPACE, "event.limit.map");
            Map<String, Integer> eventLimitMap = apolloLimitParser.parse();

            int defaultLimit = eventLimitMap.getOrDefault("default", 30);
            eventLimitMap.remove("default");
            //指定限流的事件以外的事件
            List<String> exclusionEventCodeList = new ArrayList<>();
            //如果没有专门指定限流的事件
            if (eventLimitMap.isEmpty()) {
                callEngine(gatewayMessageLogService.getList(defaultLimit));
            } else {
                ApolloConfigLimiter apolloConfigLimiter = new ApolloConfigLimiter(eventLimitMap);
                ApolloConfigRateLimiter apolloConfigRateLimiter=new ApolloConfigRateLimiter(new ApolloRateLimitParser(GW_SPACE, "event.limit.with.time.slot"),apolloConfigLimiter);
                //todo 在这插个队
                ApolloConfigSentLimiter apolloConfigSentLimiter = new ApolloConfigSentLimiter(apolloConfigRateLimiter,
                        gwRequestModelService.countGroupByEventCode(GwRequestStatus.SENDED.name()));
                SystemAutoLimiter systemAutoLimiter = new SystemAutoLimiter(systemAutoLimiterSupport.getSystemLimitMap(EventLimitTypeEnum.NON_BATCH),apolloConfigSentLimiter);
                Consumer<MessageLogQueryCondition> consumerChain = new EventLimitQueryConditionBuilder(systemAutoLimiter).andThen(new EventDelayConditionBuilder());
                CountDownLatch countDownLatch = new CountDownLatch(eventLimitMap.size());
                //如果指定了专门的事件进行限流
                for (Map.Entry<String, Integer> entry : eventLimitMap.entrySet()) {
                    try {
                        MessageLogQueryCondition queryCondition = new MessageLogQueryCondition(entry.getKey());
                        consumerChain.accept(queryCondition);

                        Stopwatch stopwatch = Stopwatch.createStarted();
                        queryDataFromDbThreadPool.execute( () -> {
                            try {
                                List<GatewayMessageLog> list = gatewayMessageLogService.getListByCondition(queryCondition);
                                callEngine(list);
                                LoggerProxy.info("eventLimitHandleCost", LOGGER, "handle cost: {} ms, eventCode={}, count={}, size={}",
                                        (stopwatch.stop().elapsed(TimeUnit.MILLISECONDS)), entry.getKey(), queryCondition.getLimit(), list.size());

                                try {
                                    int remainLimit = queryCondition.getLimit() - list.size();
                                    String redisKey = flowLimitPrefix + entry.getKey();
                                    String redisValue = redisService.get(redisKey);
                                    int newLimit;
                                    if (StringUtils.isNotBlank(redisValue)) {
                                        int old = Integer.parseInt(redisValue);
                                        // 有些时候按秒计是超限的，按分钟计不超限， 所以这里留一定的缓冲
                                        newLimit = Math.min(10 * queryCondition.getLimit(), old + remainLimit);
                                    } else {
                                        // Redis key 不存在时，初始化为剩余限制数
                                        newLimit = remainLimit;
                                        LoggerProxy.info("initRedisLimit", LOGGER, "eventCode={}, initLimit={}", entry.getKey(), newLimit);
                                    }
                                    redisService.setex(redisKey, 60 * 10, String.valueOf(newLimit));
                                } catch (Exception e) {
                                    LoggerProxy.error("updateRedisLimitError", LOGGER, "eventCode={}", entry.getKey(), e);
                                }
                            } finally {
                                countDownLatch.countDown();
                            }
                        });
                        //将这个事件加入到排除列表中
                        exclusionEventCodeList.add(entry.getKey());
                    } catch (Exception e){
                        LoggerProxy.error("handleEventLimitError", LOGGER, "eventCode={}", entry.getKey(), e);
                        countDownLatch.countDown();
                    }
                }

                try {
                    countDownLatch.await(600, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    LOGGER.warn("interruptedException", e);
                }
                if (CollectionUtils.isNotEmpty(exclusionEventCodeList)) {
                    callEngine(gatewayMessageLogService.getListByExclusionEventCodeAndLimit(exclusionEventCodeList, defaultLimit));
                }
            }
        } catch (Exception e) {
            LoggerProxy.error("gatewayMessageLogProcessJobError", LOGGER, "error:",e);
        }
        return ReturnT.SUCCESS;
    }

    private void callEngine(List<GatewayMessageLog> list) {
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (GatewayMessageLog gatewayMessageLog : list) {
            try {
                gatewayCallEngineThreadPool.execute(() -> {
                    try {
                        LogUtil.bindLogId(gatewayMessageLog.getSessionId());
                        Event event = JSON.parseObject(gatewayMessageLog.getGatewayMessage(), Event.class);
                        engineServiceClient.sendEvent(event);
                        gatewayMessageLogService.deleteById(gatewayMessageLog.getId());
                        gwRequestModelService.updateSended(gatewayMessageLog.getSessionId());
                    } catch (Exception e) {
                        LoggerProxy.error("gatewayCallEngineHandleEventError_GatewayMessageLogProcessJob", LOGGER, "eventCode={},sessionId:{}",
                                gatewayMessageLog.getEventCode(), gatewayMessageLog.getSessionId(), e);
                    } finally {
                        countDownLatch.countDown();
                        LogUtil.unbindLogId();
                    }
                });
            } catch (Exception e) {
                LoggerProxy.warn(LOGGER, "submit gatewayCallEngineThreadPool error");
                countDownLatch.countDown();
            }
        }
        try {
            countDownLatch.await(600, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.warn("interruptedException", e);
        }
    }
}
