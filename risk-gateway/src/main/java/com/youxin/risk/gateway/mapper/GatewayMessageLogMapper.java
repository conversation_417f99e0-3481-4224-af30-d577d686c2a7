package com.youxin.risk.gateway.mapper;

import com.youxin.risk.gateway.task.xxljob.limit.condition.MessageLogQueryCondition;
import com.youxin.risk.gateway.vo.GatewayMessageLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GatewayMessageLogMapper {

    /**
     * 插入一条gatewayMessageLog记录
     * @param gatewayMessageLog gateway消息
     */
    void insert(GatewayMessageLog gatewayMessageLog);

    /**
     * 查询消息列表
     * @param limit 查询的条数
     * @return 消息列表
     */
    List<GatewayMessageLog> selectList(int limit);



    /**
     * 查询limit条消息
     *
     * @param eventCode 事件编码
     * @param limit 查询的数量
     * @return 消息集合
     */
    List<GatewayMessageLog> getListByEventCodeAndLimit(@Param("eventCode") String eventCode, @Param("limit") int limit);

    /**
     * 查询limit条消息
     *
     * @param eventCodeList 被排除的事件编码
     * @param limit 查询的数量
     * @return 消息集合
     */
    List<GatewayMessageLog> getListByExclusionEventCodeAndLimit(@Param("eventCodeList") List<String> eventCodeList, @Param("limit") int limit);



    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 删除的行数
     */
    int deleteById(Long id);

    List<GatewayMessageLog> getUnDelayListByEventCodeAndLimit(@Param("eventCode") String eventCode, @Param("limit") int limit, @Param("delayTime") int delayTime);

    List<GatewayMessageLog> getListByCondition(@Param("condition") MessageLogQueryCondition condition);
}
