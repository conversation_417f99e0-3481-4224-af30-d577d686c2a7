#!/bin/sh

export VERSION=MODULE_VERSION
export NAME=MODULE_NAME
export LANG=zh_CN.UTF-8

NAME=${app.name}
if [ "$1x" == "versionx" ]; then
	echo "module is $NAME, version is $VERSION"
	exit 0
fi

APP_HOME=${app.home}
APP_LOG_HOME=${app.log.home}

TOMCAT_HOME=${tomcat.home}
TOMCAT_CONF=$TOMCAT_HOME/conf
TOMCAT_BIN=$TOMCAT_HOME/bin

if [ ! -d $APP_HOME ];then
    echo -e "\033[31m Missing app home($APP_HOME), please check!!! \033[0m"
    exit -1
fi

if [ ! -d $APP_LOG_HOME ];then
    echo -e "\033[31m Missing app log home($APP_LOG_HOME), please check!!! \033[0m"
    exit -1
fi

refresh_tomcat(){
    cd $APP_HOME/WEB-INF/classes/deploy/tomcat
    cp server.xml $TOMCAT_HOME/conf/
    cp web.xml $TOMCAT_HOME/conf/
    cp setenv.sh $TOMCAT_HOME/bin/
}

start(){
    refresh_tomcat
    cd $TOMCAT_BIN/ && sh startup.sh
}

stop(){
    cd $TOMCAT_BIN/ && sh shutdown.sh
    sleep 3s
    ps -ef | grep $TOMCAT_HOME | grep -v grep | awk -F ' ' '{print $2}' | while read line
    do
        eval "kill $line"
    done
}

MODE=${mode.name}
echo $MODE
#删除jsp文件使流量关闭
flowoff(){
    mv $APP_HOME/index.jsp $APP_HOME/index.jsp_bak
    if [ "x$MODE" != "xdev" ]
    then
        t=55
    else
        t=3
    fi
    echo "sleep $t sec"
    sleep $t
    echo "flow off done!"
}

case C"$1" in
    Cstart)
        start
        echo "start Done!"
        ;;
    Cstop)
        #flowoff
        stop
        echo "stop Done!"
        ;;
    C*)
        echo "Usage: $0 {start|stop}"
        ;;
esac