mode.name=prod
app.name=risk-gateway

home.base=/opt/app/tomcat

app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}

tomcat.port=8001
tomcat.shutdown.port=8002
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx3550m -Xms3550m -Xmn1500m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider

console.log.level=OFF

risk.service.url.engine=http://antifraud-risk-process-engine.rrdbg.com

risk.service.url.engine.batch=http://antifraud-risk-process-engine-batch.weicai.com.cn

#admin ????
datasource.admin.maxActive=10
datasource.admin.initialSize=2
datasource.admin.minIdle=2

datasource.maxActive=30
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;allowMultiQueries=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=${SEC_RISK_ADMIN_DB_USERNAME}
admin.datasource.pwd=${SEC_RISK_ADMIN_DB_PASSWORD}

gw.datasource.url=******************************************?${datasource.url.params}
gw.datasource.username=${SEC_RISK_GATEWAY_DB_USERNAME}
gw.datasource.pwd=${SEC_RISK_GATEWAY_DB_PASSWORD}

verify.datasource.url=***********************************************?${datasource.url.params}
verify.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_USERNAME}
verify.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY_DB_PASSWORD}

verify.sharding.datasource.url=**************************************************?${datasource.url.params}
verify.sharding.datasource.username=${SEC_RISK_PAYDAYLOAN_VERIFY2_DB_USERNAME}
verify.sharding.datasource.pwd=${SEC_RISK_PAYDAYLOAN_VERIFY2_DB_PASSWORD}

datacenter.datasource.url=**********************************************?${datasource.url.params}
datacenter.datasource.username=${SEC_RISK_DATACENTER_DB_USERNAME}
datacenter.datasource.pwd=${SEC_RISK_DATACENTER_DB_PASSWORD}

redis.maxTotal=300
redis.maxIdle=30
redis.minIdle=10
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=1c1zOc9cHCGE9xmOZ51jzM
redis.cluster.nodes=***********:6398,***********:6395,***********:6396,************:6393,************:6394,***********:6399

#redis cluster
verify.redis.cluster.nodes=************:6389,************:6389,************:6389,************:6390,************:6390,\
  ************:6390
verify.redis.cluster.password=Wu2tDFYIUlLGnmiU

metrics.influxdb.server=http://antifraud-influxdb1.rrdbg.com
metrics.influxdb.server.username=admin
metrics.influxdb.server.password=admin


mongo.host=rs1.riskmongo.app.rrd:27017,rs2.riskmongo.app.rrd:27017,arbiter1.riskmongo.app.rrd:27017
mongo.username=${SEC_RISK_GATEWAY_14_92_TRANSFER_MONGODB_USERNAME}
mongo.password=${SEC_RISK_GATEWAY_14_92_TRANSFER_MONGODB_PASSWORD}
mongo.database=transfer
mongo.credentials=${mongo.username}:${mongo.password}@${mongo.database}


kafka.dp.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.mirror.dp.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
kafka.library.host=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.library.verify.group=antifraud_verify_library
kafka.calculate.host=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.calculate.group=antifraud_verify_calculate
kafka.verify.calculate.topic=risk.engine.event.topic


metrics.point.kafka.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
metrics.point.kafka.topic=metrics.point.kafka.topic.gateway
metrics.point.kafka.group.id=metrics.point.kafka.group
metrics.point.mirror.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092


metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

risk.di.url=http://antifraud-risk-di.rrdbg.com
risk.dc.url=http://antifraud-risk-datacenter.rrdbg.com
risk.dc.inside.url=http://antifraud-risk-datacenter-inside.weicai.com.cn

risk.engine.url=http://antifraud-risk-process-engine.rrdbg.com
risk.alert.send.url=http://antifraud-risk-alert.rrdbg.com/alert/api/event/riskAlert/v1
rrd.web.base.url=http://credit.rrdbg.com

app.url=http://api.m.kuaisujiekuan.com
app.submit.result=/user/audit-result
app.amount.result=/user/update-loan-amount
app.user.result=/user/verify-notice
app.haohuan.url=http://api-m.haohuan.com
app.haohuan.submit.result=/internal/v1/audit/audit-notice
app.haohuan.user.result=/internal/v1/audit/verify-notice
app.haohuan.amount.result=/internal/v1/audit/increase-amount-notice
app.haohuan.payoff.result=/internal/v1/audit/reloan-notice
app.haohuan.amount.update.result=/internal/v1/audit/update-account-info

kafka.library.group=antifraud_risk_gateway_library
kafka.prea.group=antifraud_verify_prea

risk-datacenter.url=http://antifraud-risk-datacenter.rrdbg.com

youxin.env=PROD

xxl.job.admin.addresses=http://risk-xxl-job-manager.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=risk-gateway
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

transfer.mongo.host=************:27017,***********:27017,***********:27017

gw.service.url=http://antifraud-risk-gateway.rrdbg.com/risk/api/analyse/v2

wechat.robot.url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=