package com.youxin.risk.datacenter.client;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.remote.model.RpcResponse;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.hadoop.yarn.webapp.hamlet.Hamlet;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @description: Gateway客户端
 * @author: juxiang
 * @create: 2023-11-22 10:57
 **/
@Component
public class GatewayClient {
    protected final Logger LOGGER = LoggerFactory.getLogger(getClass());
    @Value("${risk.gateway.url}")
    private String gatewayHost;
    private static final String eventExitingByLoanKeyUrl="/risk/event/exiting/";
    private static final String eventStatusIsFinalByLoanKeyUrl="/risk/event/final/";

    public Map<String,Object> eventExitingByLoanKey(String loanKey){
        String url=gatewayHost+eventExitingByLoanKeyUrl+loanKey;
        Map<String, Object> result=new HashMap();
        try {
            LoggerProxy.info("eventExitingByLoanKey", LOGGER, "url:{}", url);
            String response = SyncHTTPRemoteAPI.get(url, 60000);
            RpcResponse<Boolean> rpcResponse = JSONObject.parseObject(response, new TypeReference<RpcResponse<Boolean>>() {
            });
            if(rpcResponse.getResult()){
                result.put("hasData",true);
            }
            return result;
        } catch (Exception e) {
            LoggerProxy.warn("eventExitingByLoanKey", LOGGER, "url:{}, e", url, e);
        }
        return result;
    }


    public boolean eventStatusIsFinalByLoanKey(String loanKey){
        String url=gatewayHost+eventStatusIsFinalByLoanKeyUrl+loanKey;
        try {
            LoggerProxy.info("eventStatusIsFinalByLoanKey", LOGGER, "url:{}", url);
            String response = SyncHTTPRemoteAPI.get(url, 60000);
            RpcResponse<Boolean> rpcResponse = JSONObject.parseObject(response, new TypeReference<RpcResponse<Boolean>>() {
            });
            if(rpcResponse.getResult()){
                return true;
            }
            return false;
        } catch (Exception e) {
            LoggerProxy.warn("eventStatusIsFinalByLoanKey", LOGGER, "url:{}, e", url, e);
        }
        return false;
    }
}
