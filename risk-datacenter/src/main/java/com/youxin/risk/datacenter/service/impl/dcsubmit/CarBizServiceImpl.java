package com.youxin.risk.datacenter.service.impl.dcsubmit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.DcSubmitCar;
import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.commons.model.datacenter.vo.SubmitCarVo;
import com.youxin.risk.datacenter.service.SubmitCarService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 车产
 *
 * <AUTHOR>
 * @since 2022/09/21 15:28
 */
@Service
public class CarBizServiceImpl extends AbstractSubmitBizService {

    @Resource
    private SubmitCarService submitCarService;

    @Override
    protected SubmitDataType getSubmitDataType() {
        return SubmitDataType.SUBMIT_CAR;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitData(JSONObject msgObj) {
        SubmitCarVo submitVo = JSON.toJavaObject(msgObj, SubmitCarVo.class);
        submitCar(submitVo);
    }

    public void submitCar(SubmitCarVo submitVo) {
        DcSubmitCar submitCar = new DcSubmitCar();
        BeanUtils.copyProperties(submitVo, submitCar);
        submitCar.setOperationLogId(1L);
        submitCarService.insertItem(submitCar);
    }
}
