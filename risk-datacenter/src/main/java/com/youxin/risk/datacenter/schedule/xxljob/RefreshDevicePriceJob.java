package com.youxin.risk.datacenter.schedule.xxljob;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.datacenter.constants.SpiderDataSource;
import com.youxin.risk.datacenter.mapper.DcDeviceGenerationMapper;
import com.youxin.risk.datacenter.mapper.DcDevicePriceMapper;
import com.youxin.risk.datacenter.model.DcDeviceGeneration;
import com.youxin.risk.datacenter.model.DcDevicePrice;
import com.youxin.risk.verify.redis.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class RefreshDevicePriceJob implements XxlJobBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefreshDevicePriceJob.class);

    @Value("${risk.spider.url}")
    private String url;

    @Resource
    private DcDevicePriceMapper dcDevicePriceMapper;

    @Resource
    private DcDeviceGenerationMapper dcDeviceGenerationMapper;

    @Resource(name = "cacheRedisService")
    private RedisService cacheRedisService;

    private final static String DEVICE_PRICE_INC_KEY = "risk_datacenter_device_price_inc_key";

    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1,1,0,
            TimeUnit.SECONDS,new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.AbortPolicy());

    @Override
    @XxlJob(value = "refreshDevicePriceJob")
    public ReturnT<String> execJobHandler(String param) {
        LOGGER.info("refreshDevicePriceJob start");
        threadPoolExecutor.execute(() -> {
            while (true) {
                try {
                    String stop = CacheApi.getDictSysConfig("refreshDevicePriceJob", "false", ApolloNamespace.DATACENTER_NAMESPACE);
                    if (Boolean.parseBoolean(stop)) {
                        return;
                    }
                    String idstr = cacheRedisService.get(DEVICE_PRICE_INC_KEY);
                    long id = Long.parseLong(idstr);
                    List<DcDevicePrice> devicePrices = dcDevicePriceMapper.getList(id,500);
                    if (devicePrices.isEmpty()){
                        return;
                    }
                    for (DcDevicePrice devicePrice : devicePrices) {
                        if (devicePrice.getDevicePrice() != null) {
                            continue;
                        }
                        LOGGER.info("refresh device priceJob app tag,id:{},appName:{}", devicePrice.getId(), devicePrice.getDeviceName());
                        submitCrawlerJobToDataFactory(devicePrice);
                        int refreshAppTagJobSleepTime = CacheApi.getIntFromApollo("refreshDevicePriceJobSleepTime", 1000, ApolloNamespace.DATACENTER_NAMESPACE);
                        Thread.sleep(refreshAppTagJobSleepTime);
                    }
                    cacheRedisService.set(DEVICE_PRICE_INC_KEY,devicePrices.get(devicePrices.size() - 1).getId().toString());
                } catch (Exception e) {
                    LOGGER.error("exec refreshDevicePriceJob eror",e);
                }
            }
        });
        return ReturnT.SUCCESS;
    }


    private void submitCrawlerJobToDataFactory(DcDevicePrice devicePrice) {
        String result = null;
        try {
            Map<Object, Object> params = new HashMap<>();
            List<String> dataSources = new ArrayList<>();
            dataSources.add(SpiderDataSource.ZOL);
            String keyword = covertDeviceName(devicePrice.getDeviceName());
            params.put("keyword", keyword);
            params.put("deviceName", devicePrice.getDeviceName());
            params.put("type", "devicePrice");
            params.put("dataSources", dataSources);
            params.put("callBackUrl", "/devicePrice/updateDevicePrice");
            result = SyncHTTPRemoteAPI.postJson(url + "/spider/produce", JSON.toJSONString(params), 10000);
        }catch (Exception e){
            LOGGER.info("submit crawlerJob error", e);
        }
        LOGGER.info("submit crawlerJob success,appName:{},result:{}", devicePrice.getDeviceName(),result);
    }


    private String covertDeviceName(String originName) {
        if (originName.startsWith("iPhone") || originName.startsWith("iPad") || originName.startsWith("iPod")){
            DcDeviceGeneration dcDeviceGeneration = dcDeviceGenerationMapper.getByIdentifier(originName);
            if (dcDeviceGeneration != null){
                return dcDeviceGeneration.getGeneration();
            }
        }
        return originName;
    }
}
