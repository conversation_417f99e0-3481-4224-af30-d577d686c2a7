package com.youxin.risk.datacenter.service;

import com.youxin.risk.commons.model.WhiteListDto;
import com.youxin.risk.datacenter.dao.DcWhiteListDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DcWhiteListService {
    private final static Logger logger = LoggerFactory.getLogger(DcWhiteListService.class);
    @Autowired
    private DcWhiteListDao dcWhiteListDao;

    public void insert(List<WhiteListDto> whiteListDto) {
        for (WhiteListDto dto : whiteListDto) {
            dcWhiteListDao.insert(dto);
        }
    }

    public List<WhiteListDto> query(String eventCode) {
        List<WhiteListDto> whiteListDtos = dcWhiteListDao.selectByEventCode(eventCode);
        if (whiteListDtos.size() == 0) {
            logger.warn("not found whitelist eventCode = {}", eventCode);
        }
        return whiteListDtos;
    }


}
