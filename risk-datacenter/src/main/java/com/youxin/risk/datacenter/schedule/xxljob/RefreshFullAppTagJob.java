package com.youxin.risk.datacenter.schedule.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.datacenter.mapper.AppTagMapper;
import com.youxin.risk.datacenter.model.AppTag;
import com.youxin.risk.datacenter.service.impl.AppTagServiceImpl;
import com.youxin.risk.verify.redis.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 刷新全量的appTag
 * 刷新dc_app_tag中的数据
 * <AUTHOR>
 */
@Component
public class RefreshFullAppTagJob implements XxlJobBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefreshFullAppTagJob.class);

    @Resource
    private AppTagMapper appTagMapper;

    @Resource
    private AppTagServiceImpl appTagService;

    @Resource(name = "cacheRedisService")
    private RedisService cacheRedisService;

    private final static String APP_TAG_FULL_KEY = "risk_datacenter_app_tag_full_key";

    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1,2,0,
            TimeUnit.SECONDS,new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());

    @Override
    @XxlJob(value = "refreshFullAppTagJob")
    public ReturnT<String> execJobHandler(String param) {
        LOGGER.info("refreshFullAppTagJob start");
        threadPoolExecutor.execute(() -> {
            try {
                String idstr = cacheRedisService.get(APP_TAG_FULL_KEY);
                long id = 0;
                if (idstr != null){
                    id = Long.parseLong(idstr);
                }
                AppTag appTag = appTagMapper.getOneIdGreaterThan(id);
                if (appTag == null){
                    LOGGER.info("appName is null,id:{}",id);
                    return;
                }
                LOGGER.info("refresh full app tag,id:{},appName:{}",id,appTag.getAppName());
                appTagService.submitCrawlerJobToSpider(appTag,"/appTag/updateAppTag");
                cacheRedisService.set(APP_TAG_FULL_KEY,String.valueOf(appTag.getId()));
            } catch (Exception e) {
                LOGGER.error("exec refreshFullAppTagJob eror",e);
            }
        });
        return ReturnT.SUCCESS;
    }



}
