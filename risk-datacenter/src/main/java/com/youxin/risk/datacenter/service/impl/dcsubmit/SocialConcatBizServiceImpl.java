package com.youxin.risk.datacenter.service.impl.dcsubmit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.datacenter.DcOperationLog;
import com.youxin.risk.commons.model.datacenter.DcSubmitSocialContact;
import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.model.datacenter.common.SubmitDataType;
import com.youxin.risk.commons.model.datacenter.vo.SubmitSocialContactVo;
import com.youxin.risk.datacenter.service.SubmitSocialContactService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * SOCIAL_CONCAT
 * <AUTHOR>
 * @since 2022/3/16 15:51
 */
@Service
public class SocialConcatBizServiceImpl extends AbstractSubmitBizService {

    @Resource
    private SubmitSocialContactService submitSocialContactService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitData(JSONObject msgObj) {
        SubmitSocialContactVo submitVo = JSON.toJavaObject(msgObj, SubmitSocialContactVo.class);
        submitSocialContact(submitVo);
    }

    @Override
    protected SubmitDataType getSubmitDataType() {
        return SubmitDataType.SUBMIT_SOCIAL_CONTACT;
    }

    public void submitSocialContact(SubmitSocialContactVo submitSocialContactVo) {
        DcOperationLog opLogVo = this.operationLogDbService.getOpLogVoFromInput(
                submitSocialContactVo, OperationType.SOCIAL_CONTACT);
        Long opLogId = this.operationLogDbService.insertItem(opLogVo);
        submitSocialContactVo.setOperationLogId(opLogId);
        DcSubmitSocialContact item = new DcSubmitSocialContact();
        BeanUtils.copyProperties(submitSocialContactVo, item);
        item.setOperationLogId(opLogId);
        this.submitSocialContactService.insertItem(item);
    }
}
