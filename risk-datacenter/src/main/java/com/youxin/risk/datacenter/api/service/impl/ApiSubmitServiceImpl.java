package com.youxin.risk.datacenter.api.service.impl;

import com.youxin.risk.commons.dao.datacenter.*;
import com.youxin.risk.commons.model.datacenter.DcVerifyGpsAddress;
import com.youxin.risk.commons.model.datacenter.api.*;
import com.youxin.risk.commons.model.datacenter.vo.VerifySubmitVo;
import com.youxin.risk.commons.model.datacenter.vo.api.*;
import com.youxin.risk.commons.service.datacenter.DcVerifyGpsAddressService;
import com.youxin.risk.datacenter.api.service.ApiSubmitService;
import com.youxin.risk.datacenter.service.RemoteDataService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Service("apiSubmitServImpl")
public class ApiSubmitServiceImpl implements ApiSubmitService {

    private static final Logger LOG = LoggerFactory.getLogger(ApiSubmitServiceImpl.class);

    private final ExecutorService gpsThreadPool = new ThreadPoolExecutor(10, 20, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10000), new ThreadPoolExecutor.DiscardPolicy());

    @Autowired
    private RiskApiSubmitIdcardMapper riskApiSubmitIdcardMapper;

    @Autowired
    private RiskApiSubmitRegisterMapper riskApiSubmitRegisterMapper;

    @Autowired
    private RiskApiSubmitBankCardMapper riskApiSubmitBankCardMapper;

    @Autowired
    private RiskApiSubmitContactMapper riskApiSubmitContactMapper;

    @Autowired
    private RiskApiSubmitOtherMapper riskApiSubmitOtherMapper;

    @Resource
    private RemoteDataService remoteDataService;

    @Resource
    private DcVerifyGpsAddressService dcVerifyGpsAddressService;

    @Override
    public Integer submitIdCard(RiskApiSubmitIdcardVo idCardVo) {
        RiskApiSubmitIdcard idCard = new RiskApiSubmitIdcard();
        BeanUtils.copyProperties(idCardVo, idCard);
        RiskApiSubmitIdcard info = riskApiSubmitIdcardMapper.getByUserKey(idCardVo.getUserKey(), idCardVo.getApiSource());
        if (Objects.isNull(info)) {
            idCard.setCreateTime(new Date());
            idCard.setUpdateTime(new Date());
            return riskApiSubmitIdcardMapper.insert(idCard);
        }
        idCard.setId(info.getId());
        return riskApiSubmitIdcardMapper.updateById(idCard);
    }

    @Override
    public Integer submitRegister(RiskApiSubmitRegisterVo idCardVo) {
        RiskApiSubmitRegister register = new RiskApiSubmitRegister();
        BeanUtils.copyProperties(idCardVo, register);
        RiskApiSubmitRegister info = riskApiSubmitRegisterMapper.getByUserKey(idCardVo.getUserKey(), idCardVo.getApiSource());
        if (Objects.isNull(info)) {
            register.setCreateTime(new Date());
            register.setUpdateTime(new Date());
            return riskApiSubmitRegisterMapper.insert(register);
        }
        register.setId(info.getId());
        return riskApiSubmitRegisterMapper.updateById(register);
    }

    @Override
    public Integer submitBankCard(RiskApiSubmitBankCardVo bankCardVo) {
        RiskApiSubmitBankCard bankCard = new RiskApiSubmitBankCard();
        BeanUtils.copyProperties(bankCardVo, bankCard);
        RiskApiSubmitBankCard info = riskApiSubmitBankCardMapper.getByUserKey(bankCardVo.getUserKey(), bankCardVo.getApiSource());
        if (Objects.isNull(info)) {
            bankCard.setCreateTime(new Date());
            bankCard.setUpdateTime(new Date());
            return riskApiSubmitBankCardMapper.insert(bankCard);
        }
        bankCard.setId(info.getId());
        return riskApiSubmitBankCardMapper.updateById(bankCard);
    }

    @Override
    public Integer submitContact(RiskApiSubmitContactVo contactVo) {
        List<RiskApiSubmitContact> contacts = new ArrayList<>();
        if (contactVo.getContactList() == null) {
            LOG.error("联系人提交信息为空, userKey: {}, apiSource: {}", contactVo.getUserKey(), contactVo.getApiSource());
            return 0;
        }
        List<RiskApiSubmitContact> info = riskApiSubmitContactMapper.getByUserKey(contactVo.getUserKey(), contactVo.getApiSource());
        if (CollectionUtils.isEmpty(info)) {
            String batchId = UUID.randomUUID().toString().replace("-", "");
            contactVo.getContactList().forEach(detailsVo -> {
                RiskApiSubmitContact contact = new RiskApiSubmitContact();
                contact.setBatchId(batchId);
                contact.setUserKey(contactVo.getUserKey());
                contact.setApiSource(contactVo.getApiSource());
                contact.setSourceSystem(contactVo.getSourceSystem());
                BeanUtils.copyProperties(detailsVo, contact);
                contact.setCreateTime(new Date());
                contact.setUpdateTime(new Date());
                contacts.add(contact);
            });
            return riskApiSubmitContactMapper.insertBatch(contacts);
        }
        int count = 0;
        for (int i = 0; i < info.size(); i++) {
            RiskApiSubmitContact detail = info.get(i);
            RiskApiSubmitContact contact = new RiskApiSubmitContact();
            contact.setBatchId(detail.getBatchId());
            contact.setUserKey(detail.getUserKey());
            contact.setApiSource(detail.getApiSource());
            contact.setSourceSystem(contactVo.getSourceSystem());
            if (contactVo.getContactList().size() >= i + 1) {
                contact.setContactName(contactVo.getContactList().get(i).getContactName());
                contact.setMobile(contactVo.getContactList().get(i).getMobile());
                contact.setOriRelation(contactVo.getContactList().get(i).getOriRelation());
                contact.setRelation(contactVo.getContactList().get(i).getRelation());
                contact.setId(detail.getId());
                Integer updateCount = riskApiSubmitContactMapper.updateById(contact);
                count += updateCount;
            }
        }
        return count;
    }

    public Integer submitOther(RiskApiSubmitOtherVo otherVo) {
        filterEmoji(otherVo);
        RiskApiSubmitOther other = new RiskApiSubmitOther();
        BeanUtils.copyProperties(otherVo, other);
        RiskApiSubmitOther info = riskApiSubmitOtherMapper.getByUserKey(otherVo.getUserKey(), otherVo.getApiSource());
        // 获取经纬度信息
        gpsThreadPool.execute(() -> submitVerifySubmitWithAddress(other));
        if (Objects.isNull(info)) {
            other.setCreateTime(new Date());
            other.setUpdateTime(new Date());
            return riskApiSubmitOtherMapper.insert(other);
        }
        other.setId(info.getId());
        return riskApiSubmitOtherMapper.updateById(other);
    }

    private void filterEmoji(RiskApiSubmitOtherVo otherVo) {
        // 过滤表情
        if (StringUtils.isNotBlank(otherVo.getWifiFinger())) {
            otherVo.setWifiFinger(com.youxin.risk.ra.utils.StringUtils.filterEmoji(otherVo.getWifiFinger()));
        }
        if (StringUtils.isNotBlank(otherVo.getCurrWifiFinger())) {
            otherVo.setCurrWifiFinger(com.youxin.risk.ra.utils.StringUtils.filterEmoji(otherVo.getCurrWifiFinger()));
        }
        if (StringUtils.isNotBlank(otherVo.getWifiSsid())) {
            otherVo.setWifiSsid(com.youxin.risk.ra.utils.StringUtils.filterEmoji(otherVo.getWifiSsid()));
        }
        // walletUserAgent长度截取
        if (StringUtils.isNotBlank(otherVo.getWalletUserAgent()) && otherVo.getWalletUserAgent().length() > 255) {
            otherVo.setWalletUserAgent(otherVo.getWalletUserAgent().substring(0, 255));
        }
    }

    public void submitVerifySubmitWithAddress(RiskApiSubmitOther other) {
        double longitude;
        double latitude;
        if (StringUtils.isBlank(other.getLongitude())) {
            longitude = 0d;
        } else {
            longitude = Double.parseDouble(other.getLongitude());
        }
        if (StringUtils.isBlank(other.getLatitude())) {
            latitude = 0d;
        } else {
            latitude = Double.parseDouble(other.getLatitude());
        }
        if (longitude == 0 && latitude == 0) {
            return;
        }
        //获取gps对应的地址信息
        VerifySubmitVo verifySubmitVo = new VerifySubmitVo();
        verifySubmitVo.setUserKey(other.getUserKey());
        verifySubmitVo.setLongitude(longitude);
        verifySubmitVo.setLatitude(latitude);
        Map<String, String> result = remoteDataService.getAddressByLatitudeAndLongitude(verifySubmitVo);
        //未获取（接口限制，或出错）就不存了
        if (Objects.isNull(result)) {
            return;
        }
        Date date = new Date();
        DcVerifyGpsAddress dcVerifyGpsAddress = new DcVerifyGpsAddress();
        dcVerifyGpsAddress.setUserKey(other.getUserKey());
        dcVerifyGpsAddress.setOperationLogId(1L);
        dcVerifyGpsAddress.setLatitude(latitude);
        dcVerifyGpsAddress.setLongitude(longitude);
        dcVerifyGpsAddress.setProvince(result.get("province"));
        dcVerifyGpsAddress.setCity(result.get("city"));
        dcVerifyGpsAddress.setAddress(result.get("address"));
        dcVerifyGpsAddress.setDt(date);
        dcVerifyGpsAddress.setCreateTime(date);
        dcVerifyGpsAddress.setUpdateTime(date);
        dcVerifyGpsAddressService.insert(dcVerifyGpsAddress);
    }
}
