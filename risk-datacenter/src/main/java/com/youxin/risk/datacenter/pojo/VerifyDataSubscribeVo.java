package com.youxin.risk.datacenter.pojo;



import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description: 审核相关数据订阅vo
 * @author: juxiang
 * @create: 2023-11-21 14:35
 **/
@Data
public class VerifyDataSubscribeVo {
    /**
     * loanKey
     */
    @NotBlank(message = "loanKey can not null!")
    private String loanKey;
    /**
     * 数据类型
     */
    @NotBlank(message = "dataType can not null!")
    private String dataType;

    @NotBlank(message = "requestId can not null!")
    private String requestId;

    /**
     * userKey
     */
    @NotBlank(message = "userKey can not null!")
    private String userKey;
    @NotBlank(message = "channel can not null!")
    private String channel;

}
