package com.youxin.risk.verify.vo;

import com.youxin.risk.ra.annotation.ParameterEnum;
import com.youxin.risk.verify.annotion.ParameterNullable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class VerifySubmitContactDetailsVo {
    private String relation;
    private String contactName;
    private String mobile;

    /**
     * 命中规则
     */
    private String hitRule;

    /**
     * 手机号状态及获得节点
     */
    private String statusAndNode;

    /**
     * 重新认证标志
     */
    private Boolean reCertificationFlag;

    @ParameterEnum
    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @ParameterNullable
    public String getHitRule() {
        return hitRule;
    }

    public void setHitRule(String hitRule) {
        this.hitRule = hitRule;
    }

    @ParameterNullable
    public String getStatusAndNode() {
        return statusAndNode;
    }

    public void setStatusAndNode(String statusAndNode) {
        this.statusAndNode = statusAndNode;
    }

    @ParameterNullable
    public Boolean getReCertificationFlag() {
        return reCertificationFlag;
    }

    public void setReCertificationFlag(Boolean reCertificationFlag) {
        this.reCertificationFlag = reCertificationFlag;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
            ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
