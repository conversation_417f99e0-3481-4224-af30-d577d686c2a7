package com.youxin.risk.verify.service;


import com.youxin.risk.verify.constants.VerifyChannelConstants;
import com.youxin.risk.verify.model.VerifyTransaction;
import com.youxin.risk.verify.vo.ResultAppNotifyVo;

import com.youxin.risk.verify.constants.VerifyChannelConstants;
import com.youxin.risk.verify.vo.VerifyUserLineManagementVo;

public interface VerifyNotifyService {


    boolean checkDefaultReject();

    void notifyVerifyResult(Boolean result, ResultAppNotifyVo notifyVo, Integer holdMinute, VerifyChannelConstants sourceSystem);

    void notifyTransAudit(VerifyTransaction verifyTransaction);


    void notifyLineMangResult(VerifyChannelConstants sourceSystem, VerifyUserLineManagementVo result);
}
