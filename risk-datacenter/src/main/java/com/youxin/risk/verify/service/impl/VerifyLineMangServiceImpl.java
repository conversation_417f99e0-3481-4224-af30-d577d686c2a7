package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyLineExpire;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.verify.constants.RedisKeyConstants;
import com.youxin.risk.verify.constants.VerifyConstants;
import com.youxin.risk.verify.redis.RedisService;
import com.youxin.risk.verify.service.VerifyAmountService;
import com.youxin.risk.verify.service.VerifyLineExpireService;
import com.youxin.risk.verify.service.VerifyLineMangService;
import com.youxin.risk.verify.service.VerifyResultService;
import com.youxin.risk.verify.vo.VerifyLineExpireVo;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class VerifyLineMangServiceImpl implements VerifyLineMangService {

    private final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private VerifyUserLineManagementService userLineManagementService;


    @Autowired
    private VerifyLineExpireService expireService;

    @Autowired
    private VerifyResultService verifyResultService;


    @Autowired(required = false)
    @Qualifier("cacheRedisService")
    private RedisService redisService;

    @Autowired
    private VerifyAmountService verifyAmountService;


    @Override
    public void dealTempLineData(JSONArray ext1List, VerifyUserLineManagement userLineManagement) {
        // ext1字段长度check
        if (userLineManagement.getExt1().length() > 1024) {
            userLineManagement.setExt1(userLineManagement.getExt1().substring(0, 1024));
        }
        if (CollectionUtils.isNotEmpty(ext1List)) {
            for (int i = 0; i < ext1List.size(); i++) {
                JSONObject tmpRec = ext1List.getJSONObject(i);
                String type = tmpRec.getString("type");
                String tmpLineStatus = tmpRec.getString("tmp_line_status");
                // 0无临额；1提临额；2临额中；3额度失效
                if ("0".equals(tmpLineStatus) || "3".equals(tmpLineStatus)) {
                    continue;
                }
                Date tmpLineEndTime = tmpRec.getDate("tmp_line_end_time");
                switch (type) {
                    // 1 好借；2 好买
                    case "1":
                        userLineManagement.setTmpLoanLineEndTime(tmpLineEndTime);
                        break;
                    case "2":
                        userLineManagement.setTmpShopLineEndTime(tmpLineEndTime);
                        break;
                    default:
                        break;
                }
            }
        }
    }


    @Override
    public Boolean checkUserLineVersion(String sourceSystem, String userKey, Integer oriVersion) {
        boolean result = true;
        try {
            if (!"on".equals(this.redisService.get(RedisKeyConstants.verify_userline_check_version_redis_key.name()))) {
                LOG.info("not need check userline verison");
                return result;
            }
            // 默认为初始版本
            oriVersion = oriVersion == null ? 0 : oriVersion;
            VerifyUserLineManagement userLineRec = userLineManagementService.getByUserKey(sourceSystem, userKey);
            Integer curVersion = userLineRec.getVersion() == null ? 0 : userLineRec.getVersion();
            LOG.info("curVersion={},oriVersion={}", curVersion, oriVersion);
            if (curVersion > oriVersion) {
                result = false;
            }
        } catch (Exception e) {
            LOG.error("check user line version error, userKey={},version={}", userKey, oriVersion, e);
        }
        return result;
    }



    @Override
    public String getLoanKeyFromVerifyResult(String userKey, Integer loanId, String sourceLoanKey) {
        String loanKey = sourceLoanKey;
        try {
            VerifyResult lastVerifyResult = verifyResultService.findLastResultBySysAndUserAndLoanId(
                    userKey, loanId);
            if (lastVerifyResult != null && StringUtil.isNotEmpty(lastVerifyResult.getLoanKey())) {
                loanKey = lastVerifyResult.getLoanKey();
            }
        } catch (Exception e) {
            LOG.error("get loankey from verify result failed,userKey={}", userKey, e);
        }
        return loanKey;
    }


    @Override
    public void verifySufexpire(VerifyLineExpireVo vo) {
        try {
            vo.setCurOrderId(""); //当前订单还款计划由策略来计算，置为空
            LOG.info("verifySufexpire params={}", JSON.toJSONString(vo));
            // 根据loanId获取loanKey
            String loanKey = getLoanKeyFromVerifyResult(vo.getUserKey(), vo.getLoanId(), vo.getLoanKey());
            vo.setLoanKey(loanKey);
            VerifyLineExpire expire = ObjectTransferUtils.transferObject(vo, VerifyLineExpire.class);
            expire.setCreateTime(new Date());
            expire.setUpdateTime(new Date());
            expireService.saveOrUpdateRec(expire);
             verifyAmountService.splitFlow(vo, VerifyConstants.EVENT_CODE_OVERDUE);

        } catch (Exception e) {
            LOG.error("verifySufexpire error", e);
        }
    }

}
