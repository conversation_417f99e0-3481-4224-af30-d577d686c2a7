/*
 * Copyright (C) 2020 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.verify.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-02-13
 */
public class StrategyVo {

    private Long id;
    //业务线
    private String sourceSystem;
    //类型
    private String type;
    //操作者
    private String developer;
    //策略版本
    private String strategyVersion;
    //代码ID
    private Long strategyCodeId;
    //版本号
    private Long version;
    // 备注
    private String remark;
    // 对应风险模型
    private String modelId;
    // 序号
    private Boolean riskApproval;
    // 风险审批
    private Boolean businessApproval;
    // 业务审批
    private Boolean managerApproval;
    // 领导审批
    private Integer sequenceNo;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;

    public StrategyVo() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDeveloper() {
        return developer;
    }

    public void setDeveloper(String developer) {
        this.developer = developer;
    }

    public String getStrategyVersion() {
        return strategyVersion;
    }

    public void setStrategyVersion(String strategyVersion) {
        this.strategyVersion = strategyVersion;
    }

    public Long getStrategyCodeId() {
        return strategyCodeId;
    }

    public void setStrategyCodeId(Long strategyCodeId) {
        this.strategyCodeId = strategyCodeId;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public Boolean getRiskApproval() {
        return riskApproval;
    }

    public void setRiskApproval(Boolean riskApproval) {
        this.riskApproval = riskApproval;
    }

    public Boolean getBusinessApproval() {
        return businessApproval;
    }

    public void setBusinessApproval(Boolean businessApproval) {
        this.businessApproval = businessApproval;
    }

    public Boolean getManagerApproval() {
        return managerApproval;
    }

    public void setManagerApproval(Boolean managerApproval) {
        this.managerApproval = managerApproval;
    }

    public Integer getSequenceNo() {
        return sequenceNo;
    }

    public void setSequenceNo(Integer sequenceNo) {
        this.sequenceNo = sequenceNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", id)
                .append("sourceSystem", sourceSystem)
                .append("type", type)
                .append("developer", developer)
                .append("strategyVersion", strategyVersion)
                .append("strategyCodeId", strategyCodeId)
                .append("version", version)
                .append("remark", remark)
                .append("modelId", modelId)
                .append("riskApproval", riskApproval)
                .append("businessApproval", businessApproval)
                .append("managerApproval", managerApproval)
                .append("sequenceNo", sequenceNo)
                .append("createTime", createTime)
                .append("updateTime", updateTime)
                .toString();
    }
}
