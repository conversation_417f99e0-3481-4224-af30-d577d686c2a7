package com.youxin.risk.verify.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.verify.annotion.XmlEntity;
import com.youxin.risk.verify.annotion.XmlNode;
import org.apache.commons.lang3.builder.EqualsBuilder;

import java.io.Serializable;
import java.util.Map;

@XmlEntity
public class UserLineMidverifyResultVo implements Serializable{
	
	/**
	 * @Fields serialVersionUID: 
	 */
	private static final long serialVersionUID = 4399883103842663422L;


    private String userKey;

    private String sourceSystem;

    @XmlNode(name="b_score")
    private Double bScore;

    @XmlNode(name="amount_score")
    private Double amountScore;

    @XmlNode(name="is_increased")
    private Boolean isIncreased;
    
    @XmlNode(name="inc_level")
    private String incLevel;

    @XmlNode(name="user_point")
    private Double userPoint;

    @XmlNode(name="user_level")
    private String userLevel;

    @XmlNode(name="strategy_id")
    private Integer strategyId;

    @XmlNode(name="strategy_type")
    private String strategyType;

    @XmlNode(name="is_closed")
    private Boolean isClosed;

    @XmlNode(name="is_active")
    private Boolean isActive;

    @XmlNode(name="loan_id")
    private Integer loanId;
    
    @XmlNode(name="loan_key")
    private String loanKey;
    
    @XmlNode(name="reason_code")
    private String reasonCode;
    
    @XmlNode(name="segment_code")
    private String segmentCode;
    
    @XmlNode(name="test_code")
    private String testCode;
    
    @XmlNode(name="num")
    private Integer num;

	public UserLineMidverifyResultVo() {
		this.bScore = 0.0;
		this.amountScore = 0.0;
		this.reasonCode = "{}";
		this.segmentCode = "{}";
		this.testCode = "{}";
	}

	public Double getbScore() {
		return bScore;
	}

	@JSONField(name = "b_score")
	public void setbScore(Double bScore) {
		this.bScore = bScore;
	}

	public Double getAmountScore() {
		return amountScore;
	}

	@JSONField(name = "amount_score")
	public void setAmountScore(Double amountScore) {
		this.amountScore = amountScore;
	}

	public Boolean getIsIncreased() {
		return isIncreased;
	}

	@JSONField(name = "is_increased")
	public void setIsIncreased(Boolean isIncreased) {
		this.isIncreased = isIncreased;
	}

	public String getIncLevel() {
		return incLevel;
	}

	@JSONField(name = "inc_level")
	public void setIncLevel(String incLevel) {
		this.incLevel = incLevel;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public Integer getStrategyId() {
		return strategyId;
	}

	public void setStrategyId(Integer strategyId) {
		this.strategyId = strategyId;
	}

	public String getStrategyType() {
		return strategyType;
	}

	public void setStrategyType(String strategyType) {
		this.strategyType = strategyType;
	}

	public Boolean getIsActive() {
		return isActive;
	}

	public void setIsActive(Boolean isActive) {
		this.isActive = isActive;
	}

	public Integer getLoanId() {
		return loanId;
	}

	public void setLoanId(Integer loanId) {
		this.loanId = loanId;
	}

	public String getLoanKey() {
		return loanKey;
	}

	public void setLoanKey(String loanKey) {
		this.loanKey = loanKey;
	}

	public String getReasonCode() {
		return reasonCode;
	}

	@JSONField(name = "reason_code")
	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	public String getSegmentCode() {
		return segmentCode;
	}

	@JSONField(name = "segment_code")
	public void setSegmentCode(String segmentCode) {
		this.segmentCode = segmentCode;
	}

	public String getTestCode() {
		return testCode;
	}

	@JSONField(name = "test_code")
	public void setTestCode(String testCode) {
		this.testCode = testCode;
	}

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }


    public Boolean getIsClosed() {
        return isClosed;
    }

	@JSONField(name = "is_closed")
    public void setIsClosed(Boolean isClosed) {
        this.isClosed = isClosed;
    }

    public Double getUserPoint() {
        return userPoint;
    }

	@JSONField(name = "user_point")
    public void setUserPoint(Double userPoint) {
        this.userPoint = userPoint;
    }

    public String getUserLevel() {
        return userLevel;
    }

	@JSONField(name = "user_level")
    public void setUserLevel(String userLevel) {
        this.userLevel = userLevel == null ? null : userLevel.trim();
    }

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		UserLineMidverifyResultVo that = (UserLineMidverifyResultVo) o;

		return new EqualsBuilder()
				.append(userKey, that.userKey)
				.append(sourceSystem, that.sourceSystem)
				.append(bScore, that.bScore)
				.append(amountScore, that.amountScore)
				.append(isIncreased, that.isIncreased)
				.append(incLevel, that.incLevel)
				.append(userPoint, that.userPoint)
				.append(userLevel, that.userLevel)
				.append(strategyId, that.strategyId)
				.append(strategyType, that.strategyType)
				.append(isClosed, that.isClosed)
				.append(isActive, that.isActive)
				.append(loanId, that.loanId)
				.append(loanKey, that.loanKey)
				.append(JsonUtils.toObject(reasonCode, Map.class), JsonUtils.toObject(that.reasonCode, Map.class))
				.append(JsonUtils.toObject(segmentCode, Map.class), JsonUtils.toObject(that.segmentCode, Map.class))
				.append(JsonUtils.toObject(testCode, Map.class), JsonUtils.toObject(that.testCode, Map.class))
				.append(num, that.num)
				.isEquals();
	}
}