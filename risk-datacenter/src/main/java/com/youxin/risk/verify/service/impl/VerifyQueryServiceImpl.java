package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.commons.model.datacenter.vo.SubmitContactVo;
import com.youxin.risk.verify.annotion.ReadOnlyDataSource;
import com.youxin.risk.verify.model.VerifySubmitRegister;
import com.youxin.risk.verify.service.*;
import com.youxin.risk.verify.utils.ApiCheck;
import com.youxin.risk.verify.vo.SubmitAddressVo;
import com.youxin.risk.verify.vo.SubmitJobVo;
import com.youxin.risk.verify.vo.VerifyBasicInformationVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class VerifyQueryServiceImpl implements VerifyQueryService {

    private static final Logger logger = LoggerFactory.getLogger(VerifyQueryServiceImpl.class);

    @Qualifier("verifySubmitJobService")
    @Autowired
    private SubmitJobService jobService;

    @Qualifier("verifySubmitRegisterService")
    @Autowired
    private SubmitRegisterService registerService;

    @Qualifier("verifySubmitAddressService")
    @Autowired
    private SubmitAddressService addressService;

    @Qualifier("verifySubmitContactInfoService")
    @Autowired
    private SubmitContactInfoService contactService;

    @Autowired
    private DcSubmitRegisterMapper registerMapper;

    @ReadOnlyDataSource
    @Override
    public VerifyBasicInformationVo getBasicInformationByUserKey(String userKey) {
        SubmitJobVo jobInfo = jobService.getJobInfoByUserKey(userKey);
        SubmitAddressVo addressInfo = addressService
                .getAddressInfoByUserKey(userKey);

        SubmitContactVo contactInfo = contactService
                .getContactByUserKey(userKey);
        VerifyBasicInformationVo information = new VerifyBasicInformationVo();
        String mobile = "";
        DcSubmitRegister dcRegister = registerMapper.getByUserKey(userKey);
        if (dcRegister == null) {
            logger.info("getBasicInformationByUserKey dc not exist, userKey: {}", userKey);
            VerifySubmitRegister verifyRegister = registerService.findSubmitRegisterByUserKey(userKey);
            mobile = verifyRegister.getMobile();
        } else {
            mobile = dcRegister.getMobile();
        }
        information.setMobile(mobile);
        information.setAddressInfo(addressInfo);
        information.setContactInfo(contactInfo);
        information.setJobInfo(jobInfo);
        return (VerifyBasicInformationVo) ApiCheck.removeEnum(information);
    }
}
