package com.youxin.risk.verify.vo;


import com.youxin.risk.verify.annotion.ParameterNullable;
import com.youxin.risk.verify.annotion.XmlEntity;
import com.youxin.risk.verify.annotion.XmlNode;

import java.util.Date;

/**
 * 退货实体类
 * 
 * <AUTHOR>
 * @version 2018年6月15日 下午4:04:55
 */
@XmlEntity(name= "feature")
public class VerifyMidrefundVo {

    @XmlNode(name = "last_line_management")
    private VerifyUserLineManagementVo lastLineManagement;

    @XmlNode(name = "cancel_amount")
    private Double cancelAmount;

    @XmlNode(name = "cancel_period")
    private String cancelPeriod;

    @XmlNode(name = "cancel_time")
    private Date cancelTime;

    @XmlNode(name = "cur_bill_repay_plan")
    private String curBillRepayPlan;

    @XmlNode(name = "his_bill_repay_plan")
    private String hisBillRepayPlan;
    
    @XmlNode(name = "user_key")
    private String whiteUserKey;

    @XmlNode(name = "mobile")
    private String registerPhone;
    
    @ParameterNullable
	public String getWhiteUserKey() {
		return whiteUserKey;
	}

	public void setWhiteUserKey(String whiteUserKey) {
		this.whiteUserKey = whiteUserKey;
	}

	@ParameterNullable
	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}

    public VerifyUserLineManagementVo getLastLineManagement() {
        return this.lastLineManagement;
    }

    public void setLastLineManagement(VerifyUserLineManagementVo lastLineManagement) {
        this.lastLineManagement = lastLineManagement;
    }

    public Double getCancelAmount() {
        return this.cancelAmount;
    }

    public void setCancelAmount(Double cancelAmount) {
        this.cancelAmount = cancelAmount;
    }

    public String getCancelPeriod() {
        return this.cancelPeriod;
    }

    public void setCancelPeriod(String cancelPeriod) {
        this.cancelPeriod = cancelPeriod;
    }

    public Date getCancelTime() {
        return this.cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getCurBillRepayPlan() {
        return this.curBillRepayPlan;
    }

    public void setCurBillRepayPlan(String curBillRepayPlan) {
        this.curBillRepayPlan = curBillRepayPlan;
    }

    public String getHisBillRepayPlan() {
        return this.hisBillRepayPlan;
    }

    public void setHisBillRepayPlan(String hisBillRepayPlan) {
        this.hisBillRepayPlan = hisBillRepayPlan;
    }
}
