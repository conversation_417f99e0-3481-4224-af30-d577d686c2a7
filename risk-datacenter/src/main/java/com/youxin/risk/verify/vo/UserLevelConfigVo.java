/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.verify.vo;


import com.youxin.risk.verify.annotion.XmlEntity;
import com.youxin.risk.verify.annotion.XmlNode;

/**
 * <AUTHOR>
 * @version 创建时间：2017年9月1日-下午2:52:32
 */
@XmlEntity(name = "user_level_config")
public class UserLevelConfigVo {

	@XmlNode(name = "user_level")
	private String userLevel;

	@XmlNode(name = "score_min")
	private Integer scoreMin;

	@XmlNode(name = "score_max")
	private Integer scoreMax;

	private Double amountMin;

	private Double amountMax;

	private Double rateMin;

	private Double rateMax;

	//@XmlNode(name = "installment_amount_max")
	private Double installmentAmountMax;


	public Double getRateMin() {
		return this.rateMin;
	}

	public void setRateMin(Double rateMin) {
		this.rateMin = rateMin;
	}

	public Double getRateMax() {
		return this.rateMax;
	}

	public void setRateMax(Double rateMax) {
		this.rateMax = rateMax;
	}

	public String getUserLevel() {
		return this.userLevel;
	}

	public void setUserLevel(String userLevel) {
		this.userLevel = userLevel;
	}

	public Integer getScoreMin() {
		return this.scoreMin;
	}

	public void setScoreMin(Integer scoreMin) {
		this.scoreMin = scoreMin;
	}

	public Integer getScoreMax() {
		return this.scoreMax;
	}

	public void setScoreMax(Integer scoreMax) {
		this.scoreMax = scoreMax;
	}

	public Double getAmountMin() {
		return this.amountMin;
	}

	public void setAmountMin(Double amountMin) {
		this.amountMin = amountMin;
	}

	public Double getAmountMax() {
		return this.amountMax;
	}

	public void setAmountMax(Double amountMax) {
		this.amountMax = amountMax;
	}

	public Double getInstallmentAmountMax() {
		return this.installmentAmountMax;
	}

	public void setInstallmentAmountMax(Double installmentAmountMax) {
		this.installmentAmountMax = installmentAmountMax;
	}


}
