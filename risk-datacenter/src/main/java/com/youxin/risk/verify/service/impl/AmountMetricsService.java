package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class AmountMetricsService {

    protected Logger logger = LoggerFactory.getLogger(AmountMetricsService.class);

    private ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(1,3,10, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(100000),new ThreadPoolExecutor.DiscardPolicy());

    protected void metricsAmount(VerifyUserLineManagement amount, String eventCode, String strategyType){
        poolExecutor.execute(() -> {
            //额度数值埋点
            metricsAmountData(amount,eventCode,strategyType);
            //费率埋点
            metricsAmountRate(amount,eventCode,strategyType);
        });
    }

    protected void metricsAmountRate(VerifyUserLineManagement amount,String eventCode,String strategyType) {
        String userLevel = amount.getUserLevel();

        String periodLineRate = amount.getPeriodLineRate();
        JSONArray array = JSONArray.parseArray(periodLineRate);
        for (int i = 0; i < array.size(); i++) {
            try {
                Map<String, String> tags = new HashMap<>();
                //用户等级
                tags.put("userLevel", userLevel);
                //策略类型
                tags.put("strategyType", strategyType);
                //事件
                tags.put("eventCode", eventCode);
                JSONObject jsonObject = array.getJSONObject(i);
                String type = jsonObject.getString("type");
                Integer period = jsonObject.getInteger("period");
                tags.put("rateType", type);
                tags.put("period", String.valueOf(period));

                Number rate = jsonObject.getDouble("rate");
                Map<String, Number> values = new HashMap<>();
                values.put("rate",rate);

                MetricsAPI.point(PointConstant.RISK_AMOUNT_RATE, tags, values, true, MetricsOpType.count);
            }catch (Exception e){
                logger.warn("amountRate metrics error");
            }
        }
    }


    protected void metricsAmountData(VerifyUserLineManagement amount,String eventCode,String strategyType) {
        try {
            Map<String, String> tags = new HashMap<>();
            //用户等级
            tags.put("userLevel", amount.getUserLevel());
            //策略类型
            tags.put("strategyType", strategyType);
            //事件
            tags.put("eventCode", eventCode);


            Map<String, Number> values = new HashMap<>();
            //总额度
            values.put("credit_line", amount.getCreditLine());
            //可用额度
            values.put("avail_line", amount.getAvailLine());
            //已用额度
            values.put("util_line", amount.getUtilLine());

            //（好借）总额度
            values.put("loan_line", amount.getLoanLine());
            //（好借）可用额度
            values.put("loan_avail_line", amount.getLoanAvailLine());
            //（好借）已用额度
            values.put("loan_util_line", amount.getLoanUtilLine());
            //（好借）实际额度
            values.put("loan_actual_line", amount.getLoanActualLine());

            //（好买）总额度
            values.put("shop_line", amount.getShopLine());
            //（好买）可用额度
            values.put("shop_avail_line", amount.getShopAvailLine());
            //（好买）已用额度
            values.put("shop_util_line", amount.getShopUtilLine());
            //（好买）实际额度
            values.put("shop_actual_line", amount.getShopActualLine());

            MetricsAPI.point(PointConstant.RISK_AMOUNT_DATA, tags, values, true, MetricsOpType.count);
        }catch (Exception e){
            logger.warn("amount metrics error");
        }
    }

}
