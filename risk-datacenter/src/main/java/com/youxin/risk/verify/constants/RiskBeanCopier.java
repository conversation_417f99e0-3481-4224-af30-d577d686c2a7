package com.youxin.risk.verify.constants;

import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.datacenter.pojo.UserLineManagementEffective;
import com.youxin.risk.datacenter.pojo.UserLineManagementHistory;
import org.springframework.cglib.beans.BeanCopier;

/**
 * <AUTHOR>
 */
public class RiskBeanCopier {

    public static final BeanCopier USER_LINE_MANAGEMENT_HISTORY_COPIER = BeanCopier.create(VerifyUserLineManagement.class, UserLineManagementHistory.class,false);

    public static final BeanCopier USER_LINE_MANAGEMENT_EFFECTIVE_COPIER = BeanCopier.create(VerifyUserLineManagement.class, UserLineManagementEffective.class,false);

    public static final BeanCopier HISTORY_TO_EFFECTIVE_COPIER = BeanCopier.create(UserLineManagementHistory.class, UserLineManagementEffective.class,false);

    public static final BeanCopier EFFECTIVE_TO_HISTORY_COPIER = BeanCopier.create(UserLineManagementEffective.class, UserLineManagementHistory.class,false);

}
