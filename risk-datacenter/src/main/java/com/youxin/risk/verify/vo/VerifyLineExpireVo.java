package com.youxin.risk.verify.vo;


import com.youxin.risk.verify.annotion.ParameterNullable;
import com.youxin.risk.verify.annotion.XmlEntity;
import com.youxin.risk.verify.annotion.XmlNode;

import java.util.Date;

@XmlEntity(name= "feature")
public class VerifyLineExpireVo extends VerifyLineCommonVo {

	@XmlNode(name = "overdue_amount")
    private Double overdueAmount;
    
	@XmlNode(name = "due_time")
    private Date dueTime;
	
	@XmlNode(name = "last_line_management")
    private VerifyUserLineManagementVo lastLineManagement;

	@XmlNode(name = "cur_bill_repay_plan")
    private String curBillRepayPlan;
    
	@XmlNode(name = "his_bill_repay_plan")
    private String hisBillRepayPlan;
	
	@XmlNode(name = "user_key")
    private String whiteUserKey;

    @XmlNode(name = "mobile")
    private String registerPhone;
    
    @ParameterNullable
	public String getWhiteUserKey() {
		return whiteUserKey;
	}

	public void setWhiteUserKey(String whiteUserKey) {
		this.whiteUserKey = whiteUserKey;
	}

	@ParameterNullable
	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}

	@ParameterNullable
	public String getCurBillRepayPlan() {
		return curBillRepayPlan;
	}

	public void setCurBillRepayPlan(String curBillRepayPlan) {
		this.curBillRepayPlan = curBillRepayPlan;
	}
	
	@ParameterNullable
	public String getHisBillRepayPlan() {
		return hisBillRepayPlan;
	}

	public void setHisBillRepayPlan(String hisBillRepayPlan) {
		this.hisBillRepayPlan = hisBillRepayPlan;
	}

	@ParameterNullable
	public VerifyUserLineManagementVo getLastLineManagement() {
		return lastLineManagement;
	}

	public void setLastLineManagement(VerifyUserLineManagementVo lastLineManagement) {
		this.lastLineManagement = lastLineManagement;
	}

	public Double getOverdueAmount() {
		return overdueAmount;
	}

	public void setOverdueAmount(Double overdueAmount) {
		this.overdueAmount = overdueAmount;
	}

	public Date getDueTime() {
		return dueTime;
	}

	public void setDueTime(Date dueTime) {
		this.dueTime = dueTime;
	}
    
}
