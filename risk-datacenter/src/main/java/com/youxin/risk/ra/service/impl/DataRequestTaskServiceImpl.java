package com.youxin.risk.ra.service.impl;

import com.youxin.risk.commons.constants.AnalysisSystemStatus;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.ra.enums.*;
import com.youxin.risk.ra.mapper.DataRequestTaskMapper;
import com.youxin.risk.ra.mapper.ReportRequestMapper;
import com.youxin.risk.ra.model.DataConfig;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.model.ReportRequest;
import com.youxin.risk.ra.mongo.vo.*;
import com.youxin.risk.ra.service.*;
import com.youxin.risk.ra.vo.DataFetchVo;
import com.youxin.risk.ra.vo.DataRequestTaskVo;
import com.youxin.risk.ra.vo.UnionpayVo;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
public class DataRequestTaskServiceImpl implements DataRequestTaskService {

	Logger logger = LoggerFactory.getLogger(DataRequestTaskServiceImpl.class);

	@Autowired
	private DataRequestTaskMapper dataRequestTaskMapper;

    @Autowired
    private ReportRequestMapper reportRequestMapper;

    @Autowired
    private DataConfigService dataConfigService;

    @Autowired
    private PhoneBookService phoneBookService;

    @Autowired
    private Rong360InfoService rong360InfoService;

    @Autowired
    private ShortMessageService shortMessageService;

    @Autowired
    private PhoneCallRecordService phoneCallRecordService;

    @Autowired
    private UnionpayService unionpayService;

    @Autowired
    private CreditCardBillService creditCardBillService;

    @Autowired
    private DfxkZhimaService dfxkZhimaService;

    @Autowired
    private CalendarInfoService calendarInfoService;


    @Autowired
    private DataService dataService;

    @Autowired
    @Qualifier("raCacheRedisService")
    private RedisService redisService;


    @Override
    public void fetch(Integer taskId) {
        DataRequestTask dataRequestTask = this.dataRequestTaskMapper.get(taskId);
        if (dataRequestTask == null) {
            this.logger.error("task [{}] not exist", taskId);
            throw new RuntimeException("task not exist");
        }
        this.checkAndSetStatus(dataRequestTask, DataRequestTaskStatus.FETCHED);
        this.dataRequestTaskMapper.update(dataRequestTask);
    }

    private void checkAndSetStatus(DataRequestTask dataRequestTask,
                                   DataRequestTaskStatus targetStatus) {
        DataRequestTaskStatus status = dataRequestTask.getTaskStatus();
        switch (targetStatus) {
            case FAILED:
                dataRequestTask.setTaskStatus(targetStatus);
                break;
            case FETCHED:
                /*
                 * if (status == DataRequestTaskStatus.FAILED) {
                 * logger.error("需要设置的状态[{}]不合法，当前状态[{}]", targetStatus, status);
                 * throw new RuntimeException("需要设置的状态不合法"); }
                 */
                dataRequestTask.setTaskStatus(targetStatus);
                break;
            case READY:
                if (status != DataRequestTaskStatus.INIT
                        && status != DataRequestTaskStatus.SUBMITED) {
                    this.logger.error("需要设置的状态[{}]不合法，当前状态[{}]", targetStatus, status);
                    throw new RuntimeException("需要设置的状态不合法");
                }
                dataRequestTask.setTaskStatus(targetStatus);
                break;
            case SUBMITED:
                if (status != DataRequestTaskStatus.INIT) {
                    this.logger.error("需要设置的状态[{}]不合法，当前状态[{}]", targetStatus, status);
                    throw new RuntimeException("需要设置的状态不合法");
                }
                dataRequestTask.setTaskStatus(targetStatus);
                break;
            default:
                break;

        }
    }

	@Override
    @Deprecated
	public DataRequestTask getDataRequestTaskByApplyIdAndType(Integer applyId, DataRequestTaskType type) {

		return this.dataRequestTaskMapper.findDataRequestTaskByApplyIdAndType(applyId, type);
	}

    @Override
    @Deprecated
    public DataRequestTask findDataRequestTaskByUserAndType(String sourceSystem,String userKey, DataRequestTaskType type){
        logger.info("Is it true that it is in use?-c-i");
        return this.dataRequestTaskMapper.findDataRequestTaskByUserAndTypeTaskStatus(sourceSystem, userKey, type,null);
    }

    @Override
    @Deprecated
	public DataRequestTask findDataRequestTaskByUserAndTypeTaskStatus(String sourceSystem, String userKey, DataRequestTaskType type, DataRequestTaskStatus taskStatus) {
		return this.dataRequestTaskMapper.findDataRequestTaskByUserAndTypeTaskStatus(sourceSystem, userKey, type, taskStatus);
	}

    @Override
    @Deprecated
    public List<DataRequestTask> findDataRequestTaskByUserKeyAndType(String sourceSystem, String userKey, DataRequestTaskType type) {
        return this.dataRequestTaskMapper.findDataRequestTaskByUserKeyAndType(sourceSystem, userKey, type);
    }

    @Override
    @Deprecated
    public DataRequestTask getDataRequestTaskByJobIdAndType(String jobId, String recordType) {
        return this.dataRequestTaskMapper.findDataRequestTaskByJobIdAndType(jobId, recordType);
    }

    /**
     * 检查异步任务状态
     *
     * @param sourceSystem
     * @param userKey
     */
    @Override
    public Boolean checkReportDataTask(String sourceSystem,String userKey){

        ReportRequest reportRequest = this.reportRequestMapper.findUserReportRequest(sourceSystem, userKey);
        if(reportRequest == null || reportRequest.getStatus() != AnalysisSystemStatus.DATA_TASK_STARTED){
            return false;
        }
        if(AnalysisSystemStatus.DATA_FETCHED == reportRequest.getStatus()) {
            return false;
        }
        this.logger.info("checkReportDataTask userKey={}",userKey );
        if(this.isAllTaskReady(sourceSystem,userKey,reportRequest.getCreateTime())){
            this.logger.info("checkReportDataTask all tasks fetched userKey={}",userKey);
            //更新状态
            try{
                reportRequest.setStatus(AnalysisSystemStatus.DATA_FETCHED);
                this.reportRequestMapper.update(reportRequest);
            }catch(Exception e){
                this.logger.error("userKey={},update report request status to Data_Fetched error",userKey,e);
            }
            return true;
        }

        return false;
    }

    @Override
    public Boolean isAllTaskReady(String sourceSystem, String userKey, Date reportTime){

        List<DataRequestTask> tasks = this.getTaskByUser(sourceSystem, userKey, reportTime);

        this.logger.info("checkAllTaskReady userKey={},tasks={}",userKey, JsonUtils.toJson(tasks));
        for (DataRequestTask task : tasks) {
            if (!task.getTaskStatus().isFinalStatus()){
                //check data config expire
                if (this.isDataExpired(task, reportTime)) {
                    continue;
                }
                this.logger.info("task not ready,userKey={},task={}",userKey,JsonUtils.toJson(task));
                return false;
            }
        }

        return true;
    }

    @Override
    @Deprecated
    public List<DataRequestTask> getTaskByUser(String sourceSystem, String userKey, Date before) {
        return this.dataRequestTaskMapper.findLastTaskByUser(sourceSystem, userKey, before);
    }

    private Boolean isDataExpired(DataRequestTask task, Date reportTime) {
        try {
            Map<String, DataConfig> dataConfigMap = this.dataConfigService.getDataConfigMap();
            if (dataConfigMap == null) {
                this.logger.warn("dataConfigMap is null");
                return false;
            }

            DataConfig dataConfig = dataConfigMap.get(task.getTaskType().name());
            if (dataConfig == null) {
                return false;
            }

            Date now = new Date();
            int submitTime = secondPassed(task.getCreateTime(), now);
            int applyTime = secondPassed(reportTime, now);

            if (submitTime > dataConfig.getSubmitWaitTime() && applyTime > dataConfig.getApplyWaitTime()) {
                this.logger.info("data task expired,userKey={},dataType={}", task.getUserKey(), task.getTaskType());
                return true;
            }
        } catch (Exception e) {
            this.logger.warn("check data expired error,userKey={},dataTask={}",task.getUserKey(),JsonUtils.toJson(task),e);
        }

        return false;
    }

    private int secondPassed(Date start, Date end) {
        if (start == null || end == null) {
            throw new RuntimeException("start or end time is null");
        }

        return (int)((end.getTime() - start.getTime())/1000);
    }

    @Override
    @Deprecated
    public DataRequestTask getDataRequestTaskByJobId(String jobId) {
        return this.dataRequestTaskMapper.findDataRequestTaskByJobId(jobId);
    }

    @Override
    public DataRequestTask processDataRequestTaskWithJobId(Integer taskId) {
        DataRequestTask dataRequestTask = this.dataRequestTaskMapper.get(taskId);
        if (dataRequestTask == null
                || !(DataRequestTaskStatus.SUBMITED.equals(dataRequestTask.getTaskStatus())
                || DataRequestTaskStatus.READY.equals(dataRequestTask.getTaskStatus()))
                || dataRequestTask.getSourceSystem() == null
                || dataRequestTask.getUserKey() == null
                || dataRequestTask.getApplyId() == null
                || dataRequestTask.getJobID() == null) {
            // logger.error("任务[{}]参数缺失或者jobid不存在，不继续处理", taskId);
            return dataRequestTask;
        }
        String sourceSystem = dataRequestTask.getSourceSystem();
        String userKey = dataRequestTask.getUserKey();
        String redisKey = DataRequestConstants.DATA_REQUEST_TASK + "_PROCESS_" + sourceSystem + "_" + userKey + "_" + taskId;
        if (!this.redisService.acquireLock(redisKey, DataRequestConstants.DATA_REQUEST_TASK_LOCK_TIME)) {
            this.logger.warn("请求redis锁失败, key :" + redisKey);
            return null;
        }
        try {
            this.fetchDataByTaskIdWithRetry(dataRequestTask);
            dataRequestTask = this.dataRequestTaskMapper.get(dataRequestTask.getId());
        } catch (Exception e) {
            this.logger.error("task[{}]从数据平台获取数据失败", taskId, e);
        } finally {
            this.redisService.releaseLock(redisKey);
        }
        return dataRequestTask;
    }

    @Override
    public boolean fetchDataByTaskIdWithRetry(DataRequestTask dataRequestTask) {
        switch (dataRequestTask.getTaskType()) {
            case PHONE_BOOK:
                if (this.phoneBookService.hasRecordByApplyId(dataRequestTask
                        .getApplyId())) {
                    logger.info("Is it true that it is in use?-a-v");
                    this.fetch(dataRequestTask.getId());
                    return true;
                }
            case SHUMEI_OVERDUE:
            case SHUMEI_MULTILOAN:
            case CALL_HISTORY:
            case RONG_RECORD:
            case RONG_REPORT:
            case SMS_REPORT:
            case PHONE_CALLRECORD:
            case UNIONPAYSMART_PERSONAL:
            case ALIPAY:
            case EMAIL_ACCOUNT:
            case DFXK_ZHIMA_RECORD:
            case CALENDAR_INFO:
                return this.fetchDataWithRetry(dataRequestTask);
            default:
                this.logger.error("暂时没有对接数据平台接口[{}],userKey={},loanKey={},jobid={}",
                        dataRequestTask.getTaskType().name(), dataRequestTask.getUserKey(),
                        dataRequestTask.getLoanKey(), dataRequestTask.getJobID());
                return false;
        }
    }

    private DataRequestTask getReadyDataRequestTaskBytaskId(Integer taskId) {
        logger.info("Is it true that it is in use?-j");
        DataRequestTask dataRequestTask = this.dataRequestTaskMapper.get(taskId);
        if (dataRequestTask == null) {
            this.logger.error("task [{}] not exist", taskId);
            return null;
        }
        if (dataRequestTask.getJobID() == null
                && dataRequestTask.getTaskStatus() != DataRequestTaskStatus.READY) {
            this.logger.error("task [{}] status is not READY and JobID is null",
                    taskId);
            return null;
        }
        return dataRequestTask;
    }

    private boolean fetchDataWithRetry(DataRequestTask dataRequestTask) {
        // 对通话详单，(1)pdl:只置dataRequestTask的状态，不真正取数据
        // (2)其他业务线反欺诈任务，获取数据，但是不保存
        if (!dataRequestTask.getTaskType().cacheData()) {
            if (RiskProduct.payDayLoanSeriesProducts.contains(dataRequestTask.getSourceSystem())) {
                logger.info("Is it true that it is in use?-a-vi-1");
                this.fetch(dataRequestTask.getId());
                return true;
            }
        }
        Object data;
        DataFetchVo vo = new DataFetchVo();
        vo.setJobid(dataRequestTask.getJobID());
        vo.setSystemid(dataRequestTask.getSourceSystem());
        try {
            data = this.getData(dataRequestTask.getTaskType(), vo);
        } catch (Exception e) {
            if (DataRequestTaskType.RONG_RECORD.equals(dataRequestTask.getTaskType())
                    || DataRequestTaskType.RONG_REPORT.equals(dataRequestTask.getTaskType())) {
                this.logger.warn("task[{}]调用数据平台接口[{}]失败", dataRequestTask.getId(),
                        dataRequestTask.getTaskType().name(), e);
            } else {
                this.logger.error("task[{}]调用数据平台接口[{}]失败", dataRequestTask.getId(),
                        dataRequestTask.getTaskType().name(), e);
            }
            return false;
        }
        if (data != null) {
            try {
                this.saveData(dataRequestTask,data, dataRequestTask.getTaskType(),
                        dataRequestTask.getSourceSystem(),
                        dataRequestTask.getUserKey(), dataRequestTask.getId(),
                        dataRequestTask.getApplyId());
                if(!DataRequestTaskType.RONG_RECORD.equals(dataRequestTask.getTaskType())){
                    logger.info("Is it true that it is in use?-a-vi-2");
                    this.fetch(dataRequestTask.getId());
                }
                return true;
            } catch (Exception e) {
                this.logger.error("task[{}]保存数据[{}]错误", dataRequestTask.getId(),
                        dataRequestTask.getTaskType().name(), e);
                return false;
            }
        } else {
            if(DataRequestTaskType.RONG_REPORT != dataRequestTask.getTaskType()) {
                this.logger.error("task[{}]从数据平台接口[{}]获取的数据为空", dataRequestTask.getId(),
                        dataRequestTask.getTaskType().name());
            }
            return false;
        }
    }


    private Object getData(DataRequestTaskType taskType, DataFetchVo vo) {
        switch (taskType) {
            case CALL_HISTORY:
                return this.dataService.getCallHistoryData(vo);
            case PHONE_BOOK:
                return this.dataService.getPhoneBookData(vo);
            case RONG_RECORD:
                return this.dataService.getRongRecordData(vo);
            case RONG_REPORT:
                return this.dataService.getRongReportData(vo);
            case SMS_REPORT:
                return this.dataService.getSMSData(vo);
            case PHONE_CALLRECORD:
                return this.dataService.getPhoneRecordData(vo);
            case EMAIL_ACCOUNT:
                return this.dataService.getCreditCardBillData(vo);
            case CALENDAR_INFO:
                return this.dataService.getCalendarData(vo);
            default:
                this.logger.warn("系统目前没有对接数据平台的接口:[{}]", taskType.name());
                break;
        }
        return null;
    }

    private void saveData(DataRequestTask dataTask,Object data, DataRequestTaskType taskType,
                          String sourceSystem, String userKey, Integer taskId, Integer applyId) {
        switch (taskType) {
            case CALL_HISTORY:
			/*callHistoryService.saveCallHistory((MobileCallHistoryVo) data,
					sourceSystem, userKey, taskId);*/
                break;
            case PHONE_BOOK:
                this.phoneBookService.savePhoneBook((PhoneBookRecordVo) data,
                        sourceSystem, userKey, applyId);
                break;
            case RONG_RECORD:
                this.rong360InfoService.saveRongRecord((RongRecordVo) data,
                        sourceSystem, userKey, applyId, taskId);
                break;
            case RONG_REPORT:
                this.rong360InfoService.saveRongReport((RongReportVo) data,
                        sourceSystem, userKey, applyId, taskId);
                break;
            case SMS_REPORT:
                this.shortMessageService.saveData((MessageBodyVo)data, taskId);
                break;
            case PHONE_CALLRECORD:
                this.phoneCallRecordService.saveData((PhoneCallRecordVo)data, taskId);
                break;
            case UNIONPAYSMART_PERSONAL:
                DataRequestTask dataRequestTask = new DataRequestTask();
                dataRequestTask.setSourceSystem(sourceSystem);
                dataRequestTask.setUserKey(userKey);
                dataRequestTask.setId(taskId);
                this.unionpayService.saveData((UnionpayVo)data, dataRequestTask);
                break;
            case EMAIL_ACCOUNT:
                this.creditCardBillService.saveData((CreditCardBillRecordVo)data, taskId);
                break;
            case DFXK_ZHIMA_RECORD:
                this.dfxkZhimaService.saveData((DfxkZhimaDataVo)data, dataTask);
                break;
            case CALENDAR_INFO:
                this.calendarInfoService.saveData((CalendarRecordVo)data, taskId);
            default:
                this.logger.warn("系统目前没有对接数据平台的接口:[{}]", taskType.name());
                break;
        }
    }

    @Override
    public DataRequestTask createDataRequestTaskWithJobId(Integer applyId, String sourceSystem, String userKey, DataRequestTaskType taskType, String jobId) {
        if (applyId == null || sourceSystem == null || userKey == null
                || taskType == null || jobId == null) {
            this.logger.error("创建数据任务失败,参数存在null sourceSystem={},userKey={},taskType={},jobId={}",sourceSystem,userKey,taskType,jobId);
            return null;
        }
        // 添加任务
        DataRequestTaskVo dataRequestTaskVo = new DataRequestTaskVo();
        dataRequestTaskVo.setApplyId(applyId);
        dataRequestTaskVo.setSourceSystem(sourceSystem);
        dataRequestTaskVo.setUserKey(userKey);
        dataRequestTaskVo.setTaskType(taskType);
        int expiry = DataPlatformConstants.getExpriry(taskType);
        Date expiredTime = new Date(0);
        if (expiry > 0) {
            expiredTime = DateUtils.addDays(new Date(), -1 * expiry);
        }
        dataRequestTaskVo.setExpiredTime(expiredTime);
        dataRequestTaskVo.setForce(false);
        dataRequestTaskVo.setTaskStatus(DataRequestTaskStatus.SUBMITED);
        dataRequestTaskVo.setTaskMode(TaskMode.ASYNC);
        dataRequestTaskVo.setJobID(jobId);
        // 创建dataRequestTask
        return this.createDataRequestTask(dataRequestTaskVo);
    }


    @Override
    public DataRequestTask createDataRequestTask(DataRequestTaskVo dataRequestTaskVo) {
        logger.info("Is it true that it is in use?-k-i");
        DataRequestTask dataRequestTask = null;
        try {
            dataRequestTask = ObjectTransferUtils.transferObject(dataRequestTaskVo, DataRequestTask.class);
            dataRequestTask.setVersion(0);
        } catch (Exception e) {
            // e.printStackTrace();
            logger.error("vo to model error,userKey={},loanKey={},sourceSystem={}", dataRequestTaskVo.getUserKey(), dataRequestTaskVo.getLoanKey(), dataRequestTaskVo.getSourceSystem(), e);
        }
        DataRequestTask existRecord = dataRequestTaskMapper.findOneBy(dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey(), dataRequestTask.getLoanKey(), dataRequestTask.getTaskType());
        if(!Objects.isNull(existRecord)) {
            dataRequestTask.setId(existRecord.getId());
        }
        this.dataRequestTaskMapper.insert(dataRequestTask);
        return dataRequestTask;
    }

}
