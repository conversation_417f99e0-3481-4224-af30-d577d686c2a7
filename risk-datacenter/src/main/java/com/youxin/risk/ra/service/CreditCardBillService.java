/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.service;

import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.mongo.dao.CreditCardBillDao;
import com.youxin.risk.ra.mongo.vo.CreditCardBillRecordVo;
import com.youxin.risk.ra.service.impl.AbstractBaseDataService;
import com.youxin.risk.ra.vo.DataFetchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 创建时间：2017年12月15日-下午3:11:01
 */
@Service
public class CreditCardBillService extends AbstractBaseDataService<CreditCardBillRecordVo> {


	@Autowired
	private CreditCardBillDao creditCardBillDao;

	@Override
	public void saveData(CreditCardBillRecordVo recordVo, Integer taskId) {
		recordVo.setCreatedTime(new Date());
		this.creditCardBillDao.saveRecord(recordVo, taskId);
	}

	@Override
	public CreditCardBillRecordVo getDataFromDB(DataRequestTask dataTask) {
		return null;
	}

	@Override
	public CreditCardBillRecordVo getDataFromPlatform(DataFetchVo vo) {
		return null;
	}
}
