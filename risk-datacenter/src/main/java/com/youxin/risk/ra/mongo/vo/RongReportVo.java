package com.youxin.risk.ra.mongo.vo;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection="RongReport")
public class RongReportVo extends AbstractRecordVo {
	private String orderNo;

	private HeadInfo headInfo;

	private InputInfo inputInfo;

	private BasicInfo basicInfo;

	private RiskAnalysis riskAnalysis;

	private UserPortrait userPortrait;

	private EmergencyAnalysis emergencyAnalysis;

	private AreaAnalysis areaAnalysis;

	private MonthlyConsumption monthlyConsumption;

	private TripAnalysis tripAnalysis;

	private CallLog callLog;

	private String sourceSystem;

	private Integer applyId;

	@Indexed(expireAfterSeconds=432000)//5 days
	private Date createdTime;

	public String getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public HeadInfo getHeadInfo() {
		return this.headInfo;
	}

	public void setHeadInfo(HeadInfo headInfo) {
		this.headInfo = headInfo;
	}

	public InputInfo getInputInfo() {
		return this.inputInfo;
	}

	public void setInputInfo(InputInfo inputInfo) {
		this.inputInfo = inputInfo;
	}

	public BasicInfo getBasicInfo() {
		return this.basicInfo;
	}

	public void setBasicInfo(BasicInfo basicInfo) {
		this.basicInfo = basicInfo;
	}

	public RiskAnalysis getRiskAnalysis() {
		return this.riskAnalysis;
	}

	public void setRiskAnalysis(RiskAnalysis riskAnalysis) {
		this.riskAnalysis = riskAnalysis;
	}

	public UserPortrait getUserPortrait() {
		return this.userPortrait;
	}

	public void setUserPortrait(UserPortrait userPortrait) {
		this.userPortrait = userPortrait;
	}

	public Date getCreatedTime() {
		return this.createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	public EmergencyAnalysis getEmergencyAnalysis() {
		return this.emergencyAnalysis;
	}

	public void setEmergencyAnalysis(EmergencyAnalysis emergencyAnalysis) {
		this.emergencyAnalysis = emergencyAnalysis;
	}

	public AreaAnalysis getAreaAnalysis() {
		return this.areaAnalysis;
	}

	public void setAreaAnalysis(AreaAnalysis areaAnalysis) {
		this.areaAnalysis = areaAnalysis;
	}

	public MonthlyConsumption getMonthlyConsumption() {
		return this.monthlyConsumption;
	}

	public void setMonthlyConsumption(MonthlyConsumption monthlyConsumption) {
		this.monthlyConsumption = monthlyConsumption;
	}

	public TripAnalysis getTripAnalysis() {
		return this.tripAnalysis;
	}

	public void setTripAnalysis(TripAnalysis tripAnalysis) {
		this.tripAnalysis = tripAnalysis;
	}

	public CallLog getCallLog() {
		return this.callLog;
	}

	public void setCallLog(CallLog callLog) {
		this.callLog = callLog;
	}

	public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public Integer getApplyId() {
		return this.applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}
}
