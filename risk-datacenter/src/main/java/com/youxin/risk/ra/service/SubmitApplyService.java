package com.youxin.risk.ra.service;


import com.youxin.risk.commons.vo.datavo.SubmitCommonVo;
import com.youxin.risk.ra.enums.ApplyType;
import com.youxin.risk.ra.model.SubmitApply;
import com.youxin.risk.ra.vo.SubmitApplyVo;

import java.util.List;

public interface SubmitApplyService {

    Integer saveApply(SubmitApplyVo applyVo) throws InstantiationException, IllegalAccessException, SecurityException;

    SubmitApplyVo getApplyVoFromInput(SubmitCommonVo commonVo, ApplyType applyType);

    List<SubmitApply> getLastApply(String sourceSystem, String userKey);

    SubmitApply getApplyById(Integer applyId);

    SubmitApply getLastApplySubmitByUserKeyAndSourceSystem(List<String> bindSourceSystem, String userKey, ApplyType apply);

    SubmitApply getLastApplyByUserKeyAndType(String sourceSystem, String userKey, ApplyType type);
    

}
