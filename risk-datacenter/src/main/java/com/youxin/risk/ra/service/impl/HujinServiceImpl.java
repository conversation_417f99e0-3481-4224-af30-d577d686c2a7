/**
 * Copyright(c) 2011-2018 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.enums.ThirdPartyDataSource;
import com.youxin.risk.ra.mapper.DataHujinMapper;
import com.youxin.risk.ra.model.DataHujin;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.model.ReportRequest;
import com.youxin.risk.ra.service.HujinService;
import com.youxin.risk.ra.vo.HujinRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 互金数据服务类
 *
 * <AUTHOR>
 * @version 创建时间：2018年3月13日-下午12:38:48
 */
@Service("hujinService")
public class HujinServiceImpl  implements HujinService {

	@Autowired
	private DataHujinMapper dataHujinMapper;

	@Override
	public void saveData(DataRequestTask dataTask, HujinRecordVo recordVo) {
		DataHujin record = new DataHujin();
		record.setSourceSystem(dataTask.getSourceSystem());
		record.setUserKey(dataTask.getUserKey());
		record.setTaskId(dataTask.getId());
		record.setLoanKey(dataTask.getLoanKey());
		record.setData(recordVo.getData().getData());
        record.setVersion(0);
		this.dataHujinMapper.insert(record);
	}

    @Override
    public void submitData(ReportRequest reportRequest, ThirdPartyDataSource dataSource) {

    }

    @Override
    public String getData(ReportRequest reportRequest, ThirdPartyDataSource dataSource) {
        return null;
    }
}
