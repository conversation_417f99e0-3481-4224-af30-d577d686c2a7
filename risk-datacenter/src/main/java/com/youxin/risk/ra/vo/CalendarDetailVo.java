package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.annotation.XmlEntity;
import com.youxin.risk.ra.annotation.XmlNode;

/**
 * 日历详细西信息
 *
 * <AUTHOR>
 * @version 创建时间：2018年5月7日 下午1:19:38
 */
@XmlEntity(name="calendar_info")
public class CalendarDetailVo {

    @XmlNode(name="title")
    private String title;

    @XmlNode(name="content")
    private String content;

    @XmlNode(name="start_time")
    private String startTime;

    @XmlNode(name="end_time")
    private String endTime;

    @XmlNode(name="last_modify_time")
    private String lastModifyTime;

    @XmlNode(name="timezone")
    private String timezone;

    @XmlNode(name="duration")
    private String duration;

    @XmlNode(name="organiser")
    private String organiser;

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getLastModifyTime() {
        return this.lastModifyTime;
    }

    public void setLastModifyTime(String lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getTimezone() {
        return this.timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getDuration() {
        return this.duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getOrganiser() {
        return this.organiser;
    }

    public void setOrganiser(String organiser) {
        this.organiser = organiser;
    }


}
