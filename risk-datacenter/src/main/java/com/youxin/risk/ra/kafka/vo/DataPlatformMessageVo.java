package com.youxin.risk.ra.kafka.vo;

import com.youxin.risk.commons.utils.JsonUtils;

public class DataPlatformMessageVo extends BaseKafkaMessage{
	private String type;
	private String message;
	private String jobID;
	private String code;
	private String recordType;
	private Long timestamp;
	private String data;
	private String subType;

	public String getData() {
		return this.data;
	}
	public void setData(String data) {
		this.data = data;
	}
	public String getType() {
		return this.type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getMessage() {
		return this.message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getJobID() {
		return this.jobID;
	}
	public void setJobID(String jobID) {
		this.jobID = jobID;
	}
	@Override
	public String getKey() {
		return this.jobID;
	}

	public String getCode() {
		return this.code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getRecordType() {
		return this.recordType;
	}
	public void setRecordType(String recordType) {
		this.recordType = recordType;
	}
	public Long getTimestamp() {
		return this.timestamp;
	}
	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public String getSubType() {
		return this.subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	@Override
	public void deserialize(String json) {
		DataPlatformMessageVo msg = JsonUtils.toObject(json, DataPlatformMessageVo.class);
		this.setJobID(msg.getJobID());
		this.setType(msg.getType());
		this.setMessage(msg.getMessage());
		this.setCode(msg.getCode());
		this.setRecordType(msg.getRecordType());
		this.setTimestamp(msg.getTimestamp());
		this.setData(msg.getData());
		this.setSubType(msg.getSubType());
	}

}
