package com.youxin.risk.ra.model;


import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

import java.io.Serializable;
import java.util.Date;

public class SubmitAddress implements CreateTimeSetter, UpdateTimeSetter, Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6299166554994022968L;

	private Integer id;
	private Integer applyId;
	private String sourceSystem;
	private String userKey;
	private String province;
	private String city;
	private String district;
	private String liveDuration;
	private String liveAddress;
	private String marriage;
	private String childNum;
	private Date updateTime;
	private Date createTime;
	private Integer version;

	@Override
	public void setUpdateTime() {
		this.updateTime = new Date();
	}

	@Override
	public void setCreateTime() {
		this.createTime = new Date();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApplyId() {
		return applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public String getSourceSystem() {
		return sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public String getUserKey() {
		return userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}

	public String getLiveDuration() {
		return liveDuration;
	}

	public void setLiveDuration(String liveDuration) {
		this.liveDuration = liveDuration;
	}

	public String getLiveAddress() {
		return liveAddress;
	}

	public void setLiveAddress(String liveAddress) {
		this.liveAddress = liveAddress;
	}

	public String getMarriage() {
		return marriage;
	}

	public void setMarriage(String marriage) {
		this.marriage = marriage;
	}

	public String getChildNum() {
		return childNum;
	}

	public void setChildNum(String childNum) {
		this.childNum = childNum;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}
}
