package com.youxin.risk.ra.service.postloan;

import com.youxin.risk.ra.mongo.vo.MobileCallRecordVo;
import com.youxin.risk.ra.vo.FrequentContactsVo;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PostLoanDelta extends PostLoanCalculator {
    @Override
    public Map<FrequentContactsVo, Double> getScoreMap(List<MobileCallRecordVo> mobileList, Integer days, Date dataTime) {

        Map<FrequentContactsVo, Double> result = new HashMap<>();
        if (mobileList == null || mobileList.isEmpty()) {
            return result;
        }
        Map<String, MobileProperty> mobileProperties = getMobileProperties(mobileList, days, dataTime);
        for (String toMobile : mobileProperties.keySet()) {
            MobileProperty property = mobileProperties.get(toMobile);
            Double score = 1 - Math.abs(property.inCnt - property.outCnt) * 1.0 / Math.max(property.inCnt, property.outCnt);
            FrequentContactsVo vo = new FrequentContactsVo();
            vo.setMobile(toMobile);
            vo.setCallNumber(property.inCnt + property.outCnt);
            vo.setLastCallTime(property.lastTime.getTime());
            result.put(vo, score);
        }
        return result;
    }
}