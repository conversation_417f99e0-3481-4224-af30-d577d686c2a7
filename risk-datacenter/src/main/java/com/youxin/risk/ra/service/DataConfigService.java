/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.ra.service;

import com.youxin.risk.ra.mapper.DataConfigMapper;
import com.youxin.risk.ra.model.DataConfig;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DataConfigService {

    private static final Logger LOG = LoggerFactory.getLogger(DataConfig.class);

    private Map<String, DataConfig> dataConfigMap = new HashMap();


    @Autowired
    private DataConfigMapper dataConfigMapper;


    public List<DataConfig> load() {
        return this.dataConfigMapper.loadAll(Boolean.TRUE);
    }

    @PostConstruct
    public void refresh() {
        try {
            List<DataConfig> list = this.load();
            if (CollectionUtils.isEmpty(list)) {
                LOG.info("data config empty");
                return;
            }
            Map<String, DataConfig> map = new HashMap();
            for (DataConfig config : list) {
                map.put(config.getDataType(), config);
            }
            this.dataConfigMap = map;
        } catch (Exception e) {
            LOG.error("refresh data config error", e);
        }

    }

    public Map<String, DataConfig> getDataConfigMap() {
        return this.dataConfigMap;
    }

}
