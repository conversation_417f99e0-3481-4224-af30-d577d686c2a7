package com.youxin.risk.ra.enums;

import com.youxin.risk.ra.utils.ClassLoaderUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class AccountSystemConstants {

	private static Properties property = ClassLoaderUtil.loadProperties(
			"/api/account_system_api.properties", AccountSystemConstants.class);

	public static Map<String, String> header = new HashMap<String, String>();
	public static Map<String, String> jsonHeader = new HashMap<String, String>();
	static {
		header.put("Content-Type", "application/x-www-form-urlencoded");
		jsonHeader.put("Content-Type", "application/json;charset=utf-8");
	}



	public static String baseUrl = property.getProperty("account.url.base");

	// public static String baseUrl = "http://172.16.2.122:9752";
	public static String loanByUserKeyUrl = property
			.getProperty("account.url.userKey");
	// public static String loanByUserKeyUrl =
	// "/account/loan/getLoanHistoryInfo";
	public static String loanByMobileUrl = property
			.getProperty("account.url.mobile");

	// public static String loanByMobileUrl =
	// "/account/loan/getLoanHistoryInfoOfAllPartner";

	public static String loanByIdUrl = property.getProperty("account.url.loan");

	// public static String loanByIdUrl = "/account/loan/getPayPlan";

	public static String serviceVersion = property
			.getProperty("account.service.version");
	// public static String serviceVersion = "1.0";
	public static String partner = property.getProperty("account.partner");
	// public static String partner = "PAY_DAY_LOAN";
	public static String salt = property.getProperty("account.salt");

	public static String saltHaohuan = property.getProperty("account.salt.haohuan");

	public static String saltRRD = property.getProperty("account.salt.RRD");
	// public static String salt = "123456";

	public static String loanByIdListUrl = property
			.getProperty("account.url.loan.list");

	public static String loanStatusByLoanListUrl = property.getProperty("account.url.loanStatus.list");

	public static final String bindCardURL = "/account/xw/register/HAOHUAN/HAOHUAN";

	public static final String  STATUS_UNREPAID = "UNREPAID";//待还款

	public static final String STATUS_PAYOFF = "PAYOFF";//结清

	public static final String STATUS_OVERDUE = "OVERDUE";//逾期

	public static final String TOTAL_LOANS = "TOTAL";//总借款数

	public static String timeCallMiddleSystem = "HAO_HUAN"; // 贷中额度查询默认系统编码

}
