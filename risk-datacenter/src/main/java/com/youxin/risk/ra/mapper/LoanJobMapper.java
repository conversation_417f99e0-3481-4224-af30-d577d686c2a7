package com.youxin.risk.ra.mapper;

import com.youxin.risk.ra.model.KafkaMessageLog;
import com.youxin.risk.ra.model.LoanJob;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LoanJobMapper extends BaseMapper<LoanJob> {

    LoanJob findByLoanKeyAndJobId(@Param("loanKey") String loanKey, @Param("jobType") String jobType, @Param("jobId") String jobId);

    List<LoanJob> findByTypes(@Param("loanKey") String loanKey, @Param("jobTypes") List<String> jobTypes);


}
