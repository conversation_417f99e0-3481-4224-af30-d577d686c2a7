package com.youxin.risk.ra.service.impl;

import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.ra.mongo.dao.RongRecordDao;
import com.youxin.risk.ra.mongo.dao.RongReportDao;
import com.youxin.risk.ra.mongo.vo.MobileCallHistoryVo;
import com.youxin.risk.ra.mongo.vo.MobileCallRecordVo;
import com.youxin.risk.ra.mongo.vo.RongRecordVo;
import com.youxin.risk.ra.mongo.vo.RongReportVo;
import com.youxin.risk.ra.service.DataRequestTaskService;
import com.youxin.risk.ra.service.Rong360InfoService;
import com.youxin.risk.ra.vo.RongRecordInfoVo;
import com.youxin.risk.ra.vo.TelDataVo;
import com.youxin.risk.ra.vo.TelInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

@Service
public class Rong360InfoServiceImpl implements Rong360InfoService {

    private static Logger logger = LoggerFactory.getLogger(Rong360InfoServiceImpl.class);

    @Autowired
    private RongRecordDao rongRecordDao;

    @Autowired
    private RongReportDao rongReportDao;

    @Autowired
    private DataRequestTaskService dataTaskService;

    @Override
    public void saveRongRecord(RongRecordVo rongRecordVo, String sourceSystem, String userKey, Integer applyId, Integer taskId) {
        RongRecordVo vo = this.rongRecordDao.getRecordByTaskId(taskId);
        if (vo != null) {
            this.dataTaskService.fetch(taskId);
            vo.setAdditinalOrderInfo(rongRecordVo.getAdditinalOrderInfo());
            try {
                this.rongRecordDao.updateById(vo);
            } catch (Exception e) {
                // maybe size out of limit
                logger.error("saveRongRecord occur an exception:{},userKey:{},applyId:{},taskId:{}",e.getMessage(),userKey,applyId,taskId);
            }
        } else {
            if (rongRecordVo.getAdditinalOrderInfo() != null) {
                this.dataTaskService.fetch(taskId);
            }
            rongRecordVo.setSourceSystem(sourceSystem);
            rongRecordVo.setUserKey(userKey);
            rongRecordVo.setApplyId(applyId);
            rongRecordVo.setTaskId(taskId);
            rongRecordVo.setCreatedTime(new Date());
            try {
                this.rongRecordDao.insert(rongRecordVo);
            } catch (Exception e) {
                logger.error("saveRongRecord occur an exception:{},userKey:{},applyId:{},taskId:{}",e.getMessage(),userKey,applyId,taskId);
            }
        }
    }

    @Override
    public void saveRongReport(RongReportVo rongReportVo, String sourceSystem, String userKey, Integer applyId, Integer taskId) {
        rongReportVo.setSourceSystem(sourceSystem);
        rongReportVo.setUserKey(userKey);
        rongReportVo.setApplyId(applyId);
        rongReportVo.setTaskId(taskId);
        rongReportVo.setCreatedTime(new Date());
        this.rongReportDao.insert(rongReportVo);
    }

    @Override
    public List<MobileCallHistoryVo> getCallRecords(String sourceSystem, String userKey) {
        RongRecordVo recordVo = this.rongRecordDao.getLastRecord(sourceSystem, userKey);
        if (recordVo == null) {
            return null;
        }
        try {
            RongRecordInfoVo recordInfo = JsonUtils.toObject(recordVo.getBaseOrderInfo().getData(), RongRecordInfoVo.class);
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<MobileCallHistoryVo> resultList = new ArrayList<>();
            for (TelInfoVo telInfo : recordInfo.getAddInfo().getMobile().getTel()) {
                List<MobileCallRecordVo> records = new ArrayList<>();
                for (TelDataVo telData : telInfo.getTeldata()) {
                    MobileCallRecordVo vo = new MobileCallRecordVo();
                    vo.setArea(telData.getTradeAddr());
                    vo.setMode(Integer.valueOf(telData.getTradeType()));
                    vo.setToMobile(telData.getReceivePhone());
                    vo.setDuration(Integer.valueOf(telData.getTradeTime()));
                    vo.setStartTime(df.parse(telData.getCallTime()));
                    records.add(vo);
                }
                MobileCallHistoryVo hisVo = new MobileCallHistoryVo();
                hisVo.setDetails(new HashSet<>(records));
                resultList.add(hisVo);
            }
            return resultList;
        } catch (Exception e) {
            logger.error("转换融360详单错误,userKey:{}",userKey, e);
        }
        return null;
    }
}
