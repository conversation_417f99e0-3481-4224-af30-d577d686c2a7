package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.service.RedisService;
import com.youxin.risk.ra.utils.SerializeUtil;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.List;
import java.util.Set;


public class RedisClusterServiceImpl extends JedisCluster implements RedisService {

    public RedisClusterServiceImpl(Set<HostAndPort> jedisClusterNode, int connectionTimeout, int soTimeout,
                                   int maxAttempts, String password, final GenericObjectPoolConfig poolConfig) {
        super(jedisClusterNode, connectionTimeout, soTimeout, maxAttempts, password, poolConfig);
    }

    public RedisClusterServiceImpl(Set<HostAndPort> nodes,
                                   GenericObjectPoolConfig poolConfig) {
        super(nodes, poolConfig);
    }

    @Override
    public String set(String key, String value, String nxxx, String expx, long time) {
        return super.set(key, value, nxxx, expx, time);
    }

    /**
     * 设置对象，有超时时间
     *
     * @param key
     * @param seconds
     * @param object
     */
    public void setObject(final String key, final int seconds,
                          final Object object) {
        setex(key.getBytes(), seconds, SerializeUtil.serialize(object));
    }

    /**
     * 获取对象
     *
     * @param key
     * @return
     */
    public Object getObject(final String key) {
        return SerializeUtil.unserialize(get(key.getBytes()));
    }

    @Override
    public void setObject(String key, Object object) {
        set(key.getBytes(), SerializeUtil.serialize(object));
    }

    @Override
    public void hdel(String key, String field) {
        super.hdel(key, field);
    }

    @Override
    public void hsetObject(String key, String field, Object value) {
        hset(key.getBytes(), field.getBytes(), SerializeUtil.serialize(value));
    }

    @Override
    public <T> T hgetObject(String key, String field) {
        return (T) SerializeUtil.unserialize(super.hget(key.getBytes(), field.getBytes()));
    }

    @Override
    public Set<String> keys(String keyPattern) {
        throw new RuntimeException("不支持的Redis操作");
    }

    @Override
    public void hdelObject(String key, String field) {
        super.hdel(key.getBytes(), field.getBytes());
    }

    @Override
    public Object runScript(String script, List<String> keys, List<String> args) {
        throw new RuntimeException("不支持的Redis操作");
    }

    @Override
    public Boolean acquireLock(String lock, long expired) {
        boolean success = false;
        for (int i = 0; i < 10; i++) {
            // expired 单位为秒
            long value = System.currentTimeMillis() + expired * 1000 + 1;
            long acquired = super.setnx(lock, String.valueOf(value));
            if (acquired == 1) {
                // 加上过期时间 防止 redis OOM
                super.expire(lock, (int) expired);
                // 加锁成功
                success = true;
                break;
            }
            String cacheValue = super.get(lock);
            if (null == cacheValue) {
                // 为空跳过本次重新加锁
                continue;
            }
            long oldValue = Long.valueOf(cacheValue);
            if (oldValue >= System.currentTimeMillis()) {
                // 未过期加锁失败
                success = false;
                break;
            }
            String getValue = super.getSet(lock, String.valueOf(value));
            if (null == getValue || Long.valueOf(getValue) == oldValue) {
                // 加上过期时间 防止 redis OOM
                super.expire(lock, (int) expired);
                // 加锁成功
                success = true;
            } else {
                success = false;
            }
            break;
        }
        return success;
    }

    @Override
    public void releaseLock(String lock) {
        long current = System.currentTimeMillis();
        Object value = super.get(lock);
        if (value != null && current < Long.valueOf(get(lock))) {
            super.del(lock);
        }
    }

    @Override
    public long rpushx(String key, String value, int seconds) {
        long length = super.rpushx(key, value);
        super.expire(key, seconds);
        return length;
    }

    @Override
    public long rpush(String key, int seconds, String... value) {
        long length = super.rpush(key, value);
        super.expire(key, seconds);
        return length;
    }

    @Override
    public void ltrim(String key, int start, int end) {
        super.ltrim(key, start, end);
    }

    @Override
    public List<String> lrange(String key, int start, int end) {
        return super.lrange(key, start, end);
    }

    @Override
    public String lindex(String key, Long index) {
        return super.lindex(key, index);
    }
}
