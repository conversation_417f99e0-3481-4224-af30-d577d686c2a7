package com.youxin.risk.ra.mongo.vo;

public class BasicInfo {
    private Object nameValid;
    private String phone;
    private String operator;
    private String operatorZh;
    private String phoneLocation;
    private Integer idCardCheck;
    private Integer nameCheck;
    private String idCard;
    private String realName;
    private Double aveMonthlyConsumption;
    private Double currentBalance;
    private Long regTime;
    private Integer ifCallEmergency1;
    private Integer ifCallEmergency2;
	public Object getNameValid() {
		return nameValid;
	}
	public void setNameValid(Object nameValid) {
		this.nameValid = nameValid;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getOperatorZh() {
		return operatorZh;
	}
	public void setOperatorZh(String operatorZh) {
		this.operatorZh = operatorZh;
	}
	public String getPhoneLocation() {
		return phoneLocation;
	}
	public void setPhoneLocation(String phoneLocation) {
		this.phoneLocation = phoneLocation;
	}
	public Integer getIdCardCheck() {
		return idCardCheck;
	}
	public void setIdCardCheck(Integer idCardCheck) {
		this.idCardCheck = idCardCheck;
	}
	public Integer getNameCheck() {
		return nameCheck;
	}
	public void setNameCheck(Integer nameCheck) {
		this.nameCheck = nameCheck;
	}
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Double getAveMonthlyConsumption() {
		return aveMonthlyConsumption;
	}
	public void setAveMonthlyConsumption(Double aveMonthlyConsumption) {
		this.aveMonthlyConsumption = aveMonthlyConsumption;
	}
	public Double getCurrentBalance() {
		return currentBalance;
	}
	public void setCurrentBalance(Double currentBalance) {
		this.currentBalance = currentBalance;
	}
	public Long getRegTime() {
		return regTime;
	}
	public void setRegTime(Long regTime) {
		this.regTime = regTime;
	}
	public Integer getIfCallEmergency1() {
		return ifCallEmergency1;
	}
	public void setIfCallEmergency1(Integer ifCallEmergency1) {
		this.ifCallEmergency1 = ifCallEmergency1;
	}
	public Integer getIfCallEmergency2() {
		return ifCallEmergency2;
	}
	public void setIfCallEmergency2(Integer ifCallEmergency2) {
		this.ifCallEmergency2 = ifCallEmergency2;
	}
}
