<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.DataConfigMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.DataConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="submit_wait_time" jdbcType="INTEGER" property="submitWaitTime"/>
        <result column="apply_wait_time" jdbcType="INTEGER" property="applyWaitTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>


    <sql id="table_name">
      ra_data_config
    </sql>


    <select id="loadAll" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where status = #{status}
    </select>


</mapper>