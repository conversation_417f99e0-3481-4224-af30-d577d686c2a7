<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.ra.mapper.ReportRequestMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.ra.model.ReportRequest">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="apply_id" jdbcType="INTEGER" property="applyId"/>
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="loan_id" jdbcType="INTEGER" property="loanId"/>
        <result column="loan_key" jdbcType="VARCHAR" property="loanKey"/>
        <result column="loan_duration" jdbcType="INTEGER" property="loanDuration"/>
        <result column="principal_amount" jdbcType="DOUBLE" property="principalAmount"/>
        <result column="limit_amount" jdbcType="DOUBLE" property="limitAmount"/>
        <result column="latitude" jdbcType="DOUBLE" property="latitude"/>
        <result column="longitude" jdbcType="DOUBLE" property="longitude"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="report_key" jdbcType="VARCHAR" property="reportKey"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="wifi_ssid" jdbcType="VARCHAR" property="wifiSSID"/>
        <result column="wifi_level" jdbcType="VARCHAR" property="wifiLevel"/>
        <result column="wifi_mac" jdbcType="VARCHAR" property="wifiMac"/>
        <result column="battery_level" jdbcType="INTEGER" property="batteryLevel"/>
        <result column="battery_plug_type" jdbcType="VARCHAR" property="batteryPlugType"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="low_battery_mode" jdbcType="VARCHAR" property="lowBatteryMode"/>
        <result column="shard" jdbcType="VARCHAR" property="shard"/>
        <result column="period_no" jdbcType="INTEGER" property="periodNo"/>
        <result column="step" jdbcType="VARCHAR" property="step"/>
        <result column="fund_channel" jdbcType="VARCHAR" property="fundChannel"/>
        <result column="auth_version" jdbcType="VARCHAR" property="authVersion"/>
    </resultMap>


    <sql id="table_name">
      ra_report_request
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.ra.model.ReportRequest">
        insert into
        <include refid="table_name"/>
        (apply_id,source_system,user_key,loan_id,loan_key,loan_duration,principal_amount,limit_amount,latitude,longitude,status,report_key,update_time,create_time,version,
        wifi_ssid,wifi_level,wifi_mac,battery_level,battery_plug_type,device_name,low_battery_mode,shard,period_no,step,fund_channel,auth_version)
        values
        (#{applyId},#{sourceSystem},#{userKey},#{loanId},#{loanKey},#{loanDuration},#{principalAmount},#{limitAmount},#{latitude},#{longitude},#{status},#{reportKey},now(),now(),
        #{version},#{wifiSSID},#{wifiLevel},#{wifiMac},#{batteryLevel},#{batteryPlugType},#{deviceName},#{lowBatteryMode},#{shard},#{periodNo},#{step},#{fundChannel},#{authVersion})
    </insert>

    <update id="update" parameterType="com.youxin.risk.ra.model.ReportRequest">
        update
        <include refid="table_name"/>
        <set>
            <if test="applyId != null and applyId !=''">
                apply_id = #{applyId},
            </if>
            <if test="sourceSystem != null and sourceSystem!=''">
                source_system = #{sourceSystem},
            </if>
            <if test="userKey != null and userKey!=''">
                user_key = #{userKey},
            </if>
            <if test="loanId != null and loanId !=''">
                loan_id = #{loanId},
            </if>
            <if test="loanKey != null and loanKey!=''">
                loan_key = #{loanKey},
            </if>
            <if test="loanDuration != null and loanDuration !=''">
                loan_duration = #{loanDuration},
            </if>
            <if test="principalAmount != null and principalAmount !=''">
                principal_amount = #{principalAmount},
            </if>
            <if test="limitAmount != null and limitAmount !=''">
                limit_amount = #{limitAmount},
            </if>
            <if test="latitude != null and latitude !=''">
                latitude = #{latitude},
            </if>
            <if test="longitude != null and longitude !=''">
                longitude = #{longitude},
            </if>
            <if test="status != null ">
                status = #{status},
            </if>
            <if test="reportKey != null and reportKey !=''">
                report_key = #{reportKey},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
            <if test="version != null and version !=''">
                version = #{version},
            </if>
            <if test="wifiSSID != null and wifiSSID !=''">
                wifi_ssid = #{wifiSSID},
            </if>
            <if test="wifiLevel != null and wifiLevel !=''">
                wifi_level = #{wifiLevel},
            </if>
            <if test="wifiMac != null and wifiMac !=''">
                wifi_mac = #{wifiMac},
            </if>
            <if test="batteryLevel != null and batteryLevel !=''">
                battery_level = #{batteryLevel},
            </if>
            <if test="batteryPlugType != null and batteryPlugType !=''">
                battery_plug_type = #{batteryPlugType},
            </if>
            <if test="deviceName != null and deviceName !=''">
                device_name = #{deviceName},
            </if>
            <if test="lowBatteryMode != null and lowBatteryMode !=''">
                low_battery_mode = #{lowBatteryMode},
            </if>
            <if test="shard != null and shard !=''">
                shard = #{shard},
            </if>
            <if test="periodNo != null and periodNo !=''">
                period_no = #{periodNo},
            </if>
            <if test="step != null and step !=''">
                step = #{step},
            </if>
            <if test="fundChannel != null and fundChannel !=''">
                fund_channel = #{fundChannel},
            </if>
            <if test="authVersion != null and authVersion !=''">
                auth_version = #{authVersion},
            </if>
        </set>
        where id = #{id}
    </update>


    <select id="get" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where id = #{applyId}
    </select>


    <select id="findReportRequestByUserKey" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} and source_system =#{sourceSystem} ORDER BY id DESC
        limit 1
    </select>

    <select id="findReportRequest" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} and source_system = #{sourceSystem} And loan_id = #{loanId} ORDER BY id DESC
        limit 1
    </select>

    <select id="findReportRequestByLoanKey" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} and source_system = #{sourceSystem} and loan_key=#{loanKey} ORDER BY id DESC
        limit 1
    </select>

    <select id="findUserReportRequest" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} and source_system = #{sourceSystem} ORDER BY id DESC
        limit 1
    </select>


    <select id="findReportRequestsExceptSelf" resultMap="BaseResultMap">
        select * from
        ra_report_request R where user_key = #{userKey} and source_system  IN
        <foreach collection="sourceList" item="sourceSystem" index="index" open="(" separator="," close=")">
            #{sourceSystem}
        </foreach>
        AND NOT EXISTS
        (SELECT 1 from ReportRequest WHERE loan_key = #{loanKey} AND id = R.id)  ORDER BY id DESC
    </select>

    <select id="findReportRequestByReportKey" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where report_key = #{reportKey}
        limit 1
    </select>

</mapper>