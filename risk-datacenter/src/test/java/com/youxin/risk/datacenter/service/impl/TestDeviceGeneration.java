package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.datacenter.mapper.DcDeviceGenerationMapper;
import com.youxin.risk.datacenter.mapper.DcDevicePriceMapper;
import com.youxin.risk.datacenter.model.DcDeviceGeneration;
import com.youxin.risk.datacenter.model.DcDevicePrice;
import com.youxin.risk.datacenter.service.DcDeviceGenerationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class TestDeviceGeneration {

    @Resource
    private DcDeviceGenerationMapper dcDeviceGenerationMapper;

    @Resource
    private DcDeviceGenerationService dcDeviceGenerationService;

    @Resource
    private DcDevicePriceMapper dcDevicePriceMapper;

    @Test
    public void testUpdate(){
        dcDeviceGenerationService.refreshDeviceGeneration();
    }

    @Test
    public void testGet(){
        DcDeviceGeneration byIdentifier = dcDeviceGenerationMapper.getByIdentifier("iPhone9,4");
        System.out.println(byIdentifier);
    }

    @Test
    public void test(){
        List<DcDevicePrice> list = dcDevicePriceMapper.getList(1L, 500);
        System.out.println(JSON.toJSONString(list));
    }

}
