<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.haofenqi.HaoHuanInstitutionFundRecordMapper">
    <!-- Result Map-->


    <!-- 查询用户的被拒记录 -->
    <select id="queryRejectRecordList" resultType="java.util.Map">
          SELECT created_at, institution, reject_reason  FROM hfq_institution_fund_record hifr ,user_account ua
          WHERE hifr.uid = ua.uid AND audit_result = 'FUNDER_REJECT'
         AND <![CDATA[ create_at > #{seconds} ]]> AND ua.system_unique_id = #{userKey}
	</select>

    <select id="getLineDataFromTemplateLogWithOutBaoGuoCard" resultType="hashmap">
        SELECT
            t.loan_id,
            t.borrow_time,
            t.period_nos apply_period,
            t.period_nos loan_period_num,
            t.emit_money apply_amount,
            t.emit_money real_amount,
            t.loan_rate apply_rate,
            t.loan_rate,
            t.`type`
        FROM
            template_log t
        WHERE t.loan_id = #{loanId}
          AND NOT EXISTS (
            SELECT
                1
            FROM
                save_money_card_member_order o
            WHERE
                o.uid = t.uid
              AND o.loan_id = t.loan_id
              AND o.card_type = 19
              AND o.current_status = 5
        )
            limit 1;
    </select>

    <select id="getLineDataFromTemplateLogWithOutBaoGuoCardBatch" resultType="java.util.Map">
        SELECT
            t.uid,
            t.loan_id,
            t.borrow_time,
            t.period_nos apply_period,
            t.period_nos loan_period_num,
            t.emit_money apply_amount,
            t.emit_money real_amount,
            t.loan_rate apply_rate,
            t.loan_rate,
            t.`type`
        FROM
            template_log t
        where t.loan_id in
        <foreach collection="loanIdList" item="loanId" open="(" separator="," close=")">
            #{loanId}
        </foreach>
    </select>

    <select id="getSaveMoneyCardMemberOrderBatch" resultType="java.util.Map">
        SELECT o.uid,o.loan_id,o.card_type,o.current_status from save_money_card_member_order o
        where o.loan_id in
        <foreach collection="loanIdList" item="loanId" open="(" separator="," close=")">
            #{loanId}
        </foreach>
        <if test="uidList != null and uidList.size() > 0">
            and o.uid in
            <foreach collection="uidList" item="uid" open="(" separator="," close=")">
                #{uid}
            </foreach>
        </if>
        and o.card_type = 19 and o.current_status = 5
    </select>
</mapper>
