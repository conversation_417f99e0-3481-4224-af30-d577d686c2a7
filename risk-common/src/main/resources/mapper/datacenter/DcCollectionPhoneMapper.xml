<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcCollectionPhoneMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcCollectionPhone" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="phone" property="phone" jdbcType="VARCHAR" />
        <result column="loan_app" property="loanApp" jdbcType="VARCHAR" />
        <result column="comments" property="comments" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="version" property="version" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, phone, loan_app, comments, create_time, update_time,version
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from dc_collection_phone
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from dc_collection_phone
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <select id="getAllCollectionPhone" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from dc_collection_phone
    </select>

</mapper>