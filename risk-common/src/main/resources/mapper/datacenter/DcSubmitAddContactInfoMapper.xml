<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcSubmitAddContactInfoMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="relation" property="relation" jdbcType="VARCHAR" />
        <result column="contact_name" property="contactName" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ContactMaskHandler"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ContactMaskHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, operation_log_id, user_key, relation, contact_name, mobile, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from risk_dc_submit_add_contact_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from risk_dc_submit_add_contact_info
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo" >
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into risk_dc_submit_add_contact_info (operation_log_id, user_key,
            relation, contact_name, mobile, 
            create_time, update_time)
        values (#{operationLogId,jdbcType=INTEGER}, #{userKey,jdbcType=VARCHAR}, #{relation,jdbcType=VARCHAR},
          #{contactName,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
          #{mobile,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo" >
        insert into risk_dc_submit_add_contact_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="operationLogId != null" >
                operation_log_id,
            </if>
            <if test="userKey != null" >
                user_key,
            </if>
            <if test="relation != null" >
                relation,
            </if>
            <if test="contactName != null" >
                contact_name,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="operationLogId != null" >
                #{operationLogId,jdbcType=INTEGER},
            </if>
            <if test="userKey != null" >
                #{userKey,jdbcType=VARCHAR},
            </if>
            <if test="relation != null" >
                #{relation,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null" >
                #{contactName,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo" >
        update risk_dc_submit_add_contact_info
        <set >
            <if test="operationLogId != null" >
                operation_log_id = #{operationLogId,jdbcType=INTEGER},
            </if>
            <if test="userKey != null" >
                user_key = #{userKey,jdbcType=VARCHAR},
            </if>
            <if test="relation != null" >
                relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null" >
                contact_name = #{contactName,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo" >
        update risk_dc_submit_add_contact_info
        set operation_log_id = #{operationLogId,jdbcType=INTEGER},
            user_key = #{userKey,jdbcType=VARCHAR},
            relation = #{relation,jdbcType=VARCHAR},
            contact_name = #{contactName,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            mobile = #{mobile,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--批量插入  useGeneratedKeys="true"-->
    <insert id="insertBatch"   parameterType="java.util.List">
        <!--<selectKey resultType="long" keyProperty="id">-->
        <!--SELECT LAST_INSERT_ID()-->
        <!--</selectKey>-->
        insert into risk_dc_submit_add_contact_info (operation_log_id, user_key,
        relation, contact_name, mobile
        )
        values
        <!--item就是List里每一项的对象名，要用","分割每一条数据，最后要";"结尾-->
        <foreach collection="list" item="item" index="index" separator="," close=";">
            ( #{item.operationLogId,jdbcType=VARCHAR}, #{item.userKey,jdbcType=VARCHAR}
            , #{item.relation,jdbcType=VARCHAR}
            , #{item.contactName,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler}
            , #{item.mobile,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler}
            )
        </foreach>
    </insert>

    <select id="getByOperationId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from risk_dc_submit_add_contact_info
        where operation_log_id = #{operationLogId,jdbcType=BIGINT} order  by create_time DESC limit 0,1
    </select>

    <select id="getLastByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from risk_dc_submit_add_contact_info
        WHERE user_key = #{userKey} order by id desc limit 1;
    </select>

    <select id="getLastsByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from risk_dc_submit_add_contact_info
        WHERE user_key = #{userKey}  AND operation_log_id = ( SELECT operation_log_id FROM risk_datacenter.risk_dc_submit_add_contact_info WHERE user_key = #{userKey} ORDER BY id DESC LIMIT 1 )
    </select>

    <select id="queryById" parameterType="map" resultType="java.util.HashMap">
        select id,mobile,contact_name from risk_dc_submit_add_contact_info where id between #{start} and #{end}
    </select>

    <update id="updateById" >
        update risk_dc_submit_add_contact_info set contact_name=#{contact_name}, mobile=#{mobile}
        WHERE id = #{id}
    </update>
</mapper>