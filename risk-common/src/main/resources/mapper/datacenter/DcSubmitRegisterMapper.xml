<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcSubmitRegister">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="idfa" property="idfa" jdbcType="VARCHAR"/>
        <result column="imei" property="imei" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.RegisterMaskHandler"/>
        <result column="old_mobile" property="oldMobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.RegisterMaskHandler"/>
        <result column="tongdun_fingerprint" property="tongdunFingerprint" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, operation_log_id, user_key, idfa, imei, mobile,old_mobile, tongdun_fingerprint, create_time,
        update_time
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_register
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from dc_submit_register
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitRegister">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into dc_submit_register (operation_log_id, user_key,
        idfa, imei, mobile, old_mobile,
        tongdun_fingerprint, create_time,
        update_time)
        values (#{operationLogId,jdbcType=INTEGER}, #{userKey,jdbcType=VARCHAR},
        #{idfa,jdbcType=VARCHAR}, #{imei,jdbcType=VARCHAR},
          #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
          #{oldMobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
        #{tongdunFingerprint,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitRegister">
        insert into dc_submit_register
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="operationLogId != null">
                operation_log_id,
            </if>
            <if test="userKey != null">
                user_key,
            </if>
            <if test="idfa != null">
                idfa,
            </if>
            <if test="imei != null">
                imei,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="oldMobile != null">
                old_mobile,
            </if>
            <if test="tongdunFingerprint != null">
                tongdun_fingerprint,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="operationLogId != null">
                #{operationLogId,jdbcType=INTEGER},
            </if>
            <if test="userKey != null">
                #{userKey,jdbcType=VARCHAR},
            </if>
            <if test="idfa != null">
                #{idfa,jdbcType=VARCHAR},
            </if>
            <if test="imei != null">
                #{imei,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
            </if>
            <if test="oldMobile != null">
                #{oldMobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
            </if>
            <if test="tongdunFingerprint != null">
                #{tongdunFingerprint,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitRegister">
        update dc_submit_register
        <set>
            <if test="operationLogId != null">
                operation_log_id = #{operationLogId,jdbcType=INTEGER},
            </if>
            <if test="userKey != null">
                user_key = #{userKey,jdbcType=VARCHAR},
            </if>
            <if test="idfa != null">
                idfa = #{idfa,jdbcType=VARCHAR},
            </if>
            <if test="imei != null">
                imei = #{imei,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
            </if>
            <if test="oldMobile != null">
                old_mobile = #{oldMobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
            </if>
            <if test="tongdunFingerprint != null">
                tongdun_fingerprint = #{tongdunFingerprint,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitRegister">
        update dc_submit_register
        set operation_log_id = #{operationLogId,jdbcType=INTEGER},
        user_key = #{userKey,jdbcType=VARCHAR},
        idfa = #{idfa,jdbcType=VARCHAR},
        imei = #{imei,jdbcType=VARCHAR},
        mobile = #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
        old_mobile = #{oldMobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.RegisterMaskHandler},
        tongdun_fingerprint = #{tongdunFingerprint,jdbcType=LONGVARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getByOperationId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_register
        where operation_log_id = #{operationLogId,jdbcType=BIGINT} order by create_time DESC limit 0,1
    </select>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_register
        where user_key = #{userKey,jdbcType=VARCHAR} order by create_time DESC limit 0,1
    </select>

    <select id="getListByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_register
        where user_key = #{userKey,jdbcType=VARCHAR}
        order by id desc
    </select>
    <select id="getUserCountByIdfa" resultType="java.lang.Long">
        select count(DISTINCT r.user_key) from dc_submit_register r JOIN dc_operation_log l
        ON (r.operation_log_id = l.id)
        where idfa = #{idfa,jdbcType=VARCHAR} AND l.source_system = #{sourceSystem,jdbcType=VARCHAR}
    </select>
    <select id="getUserCountByImei" resultType="java.lang.Long">
        select count(DISTINCT r.user_key) from dc_submit_register r JOIN dc_operation_log l
        ON (r.operation_log_id = l.id)
        where imei = #{imei,jdbcType=VARCHAR} AND l.source_system = #{sourceSystem,jdbcType=VARCHAR}
    </select>


    <select id="getUpdateMobileRecordsByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_submit_register
        where user_key = #{userKey,jdbcType=VARCHAR}
         and old_mobile is not null
        order by id desc
    </select>


    <update id="initOldMobile" parameterType="String">
        UPDATE dc_submit_register r2
        SET r2.old_mobile = (
        SELECT
        max(a.mobile)
        FROM
        (
        SELECT
        r1.user_key,
        r1.mobile
        FROM
        dc_submit_register r1
        WHERE
        r1.user_key = (
        SELECT
        r.user_key
        FROM
        dc_submit_register r
        WHERE
        r.operation_log_id = #{logId,jdbcType=VARCHAR}
        )
        AND r1.operation_log_id <![CDATA[<]]> #{logId,jdbcType=VARCHAR}
        ORDER BY
        id DESC
        LIMIT 1
        ) a
        )
        WHERE
        r2.operation_log_id = #{logId,jdbcType=VARCHAR}
    </update>

    <select id="getUserKeyByMobileList" resultType="java.lang.String">
        select user_key from
        dc_submit_register
        WHERE  mobile IN
        <foreach collection="mobileList" item="mobile" index="index" open="(" separator="," close=")">
            #{mobile}
        </foreach>
        order by id desc limit 1
    </select>

    <select id="queryRegisterById" parameterType="map" resultType="java.util.HashMap">
        select id,mobile from dc_submit_register where id between #{start} and #{end}
    </select>

    <update id="updateMobileById" >
        update dc_submit_register set mobile=#{mobile}
        WHERE id = #{id}
    </update>

    <select id="queryOldMobileRegisterById" parameterType="map" resultType="java.util.HashMap">
        select id,old_mobile from dc_submit_register where id between #{start} and #{end} and old_mobile is not null
    </select>

    <update id="updateOldMobileById" >
        update dc_submit_register set old_mobile=#{mobile}
        WHERE id = #{id}
    </update>

    <select id="findRegisterByUserKeyAndMobile" resultMap="BaseResultMap">
        select user_key,mobile from `dc_submit_register`
        <where>
            <if test = "mobile != null and mobile != ''">
                and `mobile`  = #{mobile}
            </if>
            <if test = "userKey != null and userKey != ''">
                and `user_key` = #{userKey}
            </if>
        </where>
        ORDER BY `update_time`desc limit 1
    </select>
</mapper>