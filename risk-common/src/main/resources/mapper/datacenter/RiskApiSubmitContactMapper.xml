<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.RiskApiSubmitContactMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitContact" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="api_source" property="apiSource" jdbcType="VARCHAR"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="relation" property="relation" jdbcType="VARCHAR" />
        <result column="ori_relation" property="oriRelation" jdbcType="VARCHAR" />
        <result column="contact_name" property="contactName" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ApiMaskHandler"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"
                typeHandler="com.youxin.risk.commons.mybatis.ApiMaskHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, api_source, source_system, batch_id, user_key, ori_relation, relation, contact_name, mobile, create_time, update_time
    </sql>

    <sql id="table_name">
        risk_api_submit_contact
    </sql>

    <insert id="insertBatch"   parameterType="java.util.List">
        insert into <include refid="table_name"/> (
            api_source, source_system, batch_id, user_key, ori_relation, relation, contact_name, mobile
        )
        values
        <!--item就是List里每一项的对象名，要用","分割每一条数据，最后要";"结尾-->
        <foreach collection="list" item="item" index="index" separator="," close=";">
            (
                #{item.apiSource,jdbcType=VARCHAR},#{item.sourceSystem,jdbcType=VARCHAR},#{item.batchId,jdbcType=VARCHAR},
                #{item.userKey,jdbcType=VARCHAR}, #{item.oriRelation,jdbcType=VARCHAR}, #{item.relation,jdbcType=VARCHAR},
                #{item.contactName,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler},
                #{item.mobile,jdbcType=VARCHAR,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
            )
        </foreach>
    </insert>

    <update id="updateById">
        update <include refid="table_name"/>
        set
        update_time = now()
        <if test="sourceSystem != null and sourceSystem != '' ">
            ,source_system = #{sourceSystem,jdbcType=VARCHAR}
        </if>
        <if test="batchId != null and batchId != '' ">
            ,batch_id = #{batchId,jdbcType=VARCHAR}
        </if>
        <if test="relation != null and relation != '' ">
            ,relation = #{relation,jdbcType=VARCHAR}
        </if>
        <if test="oriRelation != null and oriRelation != '' ">
            ,ori_relation = #{oriRelation,jdbcType=VARCHAR}
        </if>
        <if test="contactName != null and contactName != '' ">
            ,contact_name = #{contactName,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        </if>
        <if test="mobile != null and mobile != '' ">
            ,mobile = #{mobile,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        </if>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where user_key = #{userKey}
        <if test="apiSource != null and apiSource != ''">
            and api_source = #{apiSource}
        </if>
        and batch_id = ( SELECT batch_id FROM <include refid="table_name"/> WHERE user_key = #{userKey} ORDER BY id DESC LIMIT 1 )
    </select>

</mapper>