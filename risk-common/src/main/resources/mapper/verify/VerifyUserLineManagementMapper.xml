<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.verify.VerifyUserLineManagementMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="credit_line" property="creditLine"/>
        <result column="avail_line" property="availLine"/>
        <result column="util_line" property="utilLine"/>
        <result column="loan_line" property="loanLine"/>
        <result column="loan_avail_line" property="loanAvailLine"/>
        <result column="loan_actual_line" property="loanActualLine"/>
        <result column="loan_util_line" property="loanUtilLine"/>
        <result column="loan_rate" property="loanRate"/>
        <result column="loan_period" property="loanPeriod"/>
        <result column="bt_line" property="btLine"/>
        <result column="bt_avail_line" property="btAvailLine"/>
        <result column="bt_actual_line" property="btActualLine"/>
        <result column="bt_util_line" property="btUtilLine"/>
        <result column="bt_rate" property="btRate"/>
        <result column="bt_period" property="btPeriod"/>
        <result column="shop_line" property="shopLine"/>
        <result column="shop_avail_line" property="shopAvailLine"/>
        <result column="shop_actual_line" property="shopActualLine"/>
        <result column="shop_util_line" property="shopUtilLine"/>
        <result column="shop_rate" property="shopRate"/>
        <result column="shop_period" property="shopPeriod"/>
        <result column="period_line_rate" property="periodLineRate"/>
        <result column="account_status" property="accountStatus"/>
        <result column="is_closed" property="isClosed"/>
        <result column="user_point" property="userPoint"/>
        <result column="user_level" property="userLevel"/>
        <result column="line_assign_time" property="lineAssignTime"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_type" property="strategyType"/>
        <result column="is_active" property="isActive"/>
        <result column="last_id" property="lastId"/>
        <result column="loan_id" property="loanId"/>
        <result column="loan_key" property="loanKey"/>
        <result column="reason_code" property="reasonCode"/>
        <result column="segment_code" property="segmentCode"/>
        <result column="test_code" property="testCode"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="ext1" property="ext1"/>
        <result column="tmp_shop_line_end_time" property="tmpShopLineEndTime"/>
        <result column="tmp_loan_line_end_time" property="tmpLoanLineEndTime"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line, loan_actual_line,
        loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line, bt_actual_line, bt_util_line, bt_rate,
        bt_period, shop_line, shop_avail_line, shop_actual_line, shop_util_line, shop_rate, shop_period,
        period_line_rate, account_status, is_closed, user_point, user_level, line_assign_time, strategy_id,
        strategy_type, is_active, last_id, loan_id, loan_key, reason_code, segment_code, test_code, status,
        remark, create_time, update_time, version, ext1, tmp_shop_line_end_time, tmp_loan_line_end_time
    </sql>

    <select id="getByUserKey" resultMap="BaseResultMap" >
        select * from verify_user_line_management
        where user_key = #{userKey} AND  source_system = #{sourceSystem}  AND is_active = 1 order  by id DESC limit 0,1
    </select>

    <select id="getAmountAssignByUserKey" resultMap="BaseResultMap">
        select * from verify_user_line_management
        where user_key = #{userKey} AND  source_system = #{sourceSystem}  AND (strategy_type like 'AMOUNT_ASSIGN_%'
            or strategy_type in ('IRR36_VERIFY_AMOUNT', 'IRR24_VERIFY_AMOUNT', 'WHITE_LIST_VERIFY_AMOUNT'))
        order  by id DESC limit 0,1
    </select>

    <select id="getListByUserKey" resultMap="BaseResultMap">
        select
        id, user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line,
        loan_actual_line, loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line,
        bt_actual_line, bt_util_line, bt_rate, bt_period, shop_line, shop_avail_line, shop_actual_line,
        shop_util_line, shop_rate, shop_period, account_status, is_closed, user_point, user_level,
        line_assign_time, strategy_id, strategy_type, is_active, last_id, loan_id, loan_key,
        status, remark, create_time, update_time, version
        from verify_user_line_management
        where user_key = #{userKey} AND  source_system = #{sourceSystem} AND status = 1 order  by id DESC
    </select>

    <select id="getLatestAmountByType" resultMap="BaseResultMap">
        SELECT vulm.* from verify_user_line_management vulm , (SELECT max(id) as id from verify_user_line_management where strategy_type in(
        <foreach collection="strategyTypes" item="type" index="index" separator=",">#{type}
        </foreach>)
        AND user_key = #{userKey} GROUP BY strategy_type) as t where vulm.id = t.id
    </select>


    <update id="updateNoInvalid">
        update verify_user_line_management set is_active = 0
        where user_key = #{userKey}
        and id = #{lineId}
        and is_active = 1;
    </update>


    <update id="updateUserLineManagemert" parameterType="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        update verify_user_line_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="userKey != null">user_key = #{userKey},</if>
            <if test="sourceSystem != null">source_system = #{ sourceSystem},</if>
            <if test="creditLine != null">credit_line = #{ creditLine},</if>
            <if test="availLine != null">avail_line = #{ availLine},</if>
            <if test="utilLine != null">util_line = #{ utilLine},</if>
            <if test="loanLine != null">loan_line = #{ loanLine},</if>
            <if test="loanAvailLine != null">loan_avail_line = #{ loanAvailLine},</if>
            <if test="loanActualLine != null">loan_actual_line = #{ loanActualLine},</if>
            <if test="loanUtilLine != null">loan_util_line = #{loanUtilLine},</if>
            <if test="loanRate != null">loan_rate = #{ loanRate},</if>
            <if test="loanPeriod != null">loan_period = #{ loanPeriod},</if>
            <if test="btLine != null">bt_line = #{ btLine},</if>
            <if test="btAvailLine != null">bt_avail_line = #{ btAvailLine},</if>
            <if test="btActualLine != null">bt_actual_line = #{ btActualLine},</if>
            <if test="btUtilLine != null">bt_util_line = #{ btUtilLine},</if>
            <if test="btRate != null">bt_rate = #{ btRate},</if>
            <if test="btPeriod != null">bt_period = #{btPeriod},</if>
            <if test="shopLine != null">shop_line = #{ shopLine},</if>
            <if test="shopAvailLine != null">shop_avail_line = #{ shopAvailLine},</if>
            <if test="shopActualLine != null">shop_actual_line = #{ shopActualLine},</if>
            <if test="shopUtilLine != null">shop_util_line = #{shopUtilLine},</if>
            <if test="shopRate != null">shop_rate = #{ shopRate},</if>
            <if test="shopPeriod != null">shop_period = #{ shopPeriod},</if>
            <if test="periodLineRate != null">period_line_rate = #{periodLineRate},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="isClosed != null">is_closed = #{ isClosed},</if>
            <if test="userPoint != null">user_point = #{ userPoint},</if>
            <if test="userLevel != null">user_level = #{ userLevel},</if>
            <if test="lineAssignTime != null">line_assign_time = #{ lineAssignTime},</if>
            <if test="strategyId != null">strategy_id = #{ strategyId},</if>
            <if test="strategyType != null">strategy_type = #{strategyType},</if>
            <if test="isActive != null">is_active = #{ isActive},</if>
            <if test="lastId != null">last_id = #{ lastId},</if>
            <if test="loanId != null">loan_id = #{ loanId},</if>
            <if test="loanKey != null">loan_key = #{ loanKey},</if>
            <if test="reasonCode != null">reason_code = #{ reasonCode},</if>
            <if test="segmentCode != null">segment_code = #{ segmentCode},</if>
            <if test="testCode != null">test_code = #{ testCode},</if>
            <if test="status != null">status = #{ status},</if>
            <if test="remark != null">remark = #{ remark},</if>
            <if test="createTime != null">create_time = #{ createTime},</if>
            <if test="updateTime != null">update_time = #{ updateTime},</if>
            <if test="version != null">version = #{ version},</if>
            <if test="ext1 != null">ext1 = #{ ext1},</if>
            <if test="tmpShopLineEndTime != null">tmp_shop_line_end_time = #{ tmpShopLineEndTime},</if>
            <if test="tmpLoanLineEndTime != null">tmp_loan_line_end_time = #{ tmpLoanLineEndTime},</if>
        </trim>
        where id = #{id}
    </update>

    <insert id="saveUserLineManagemert" parameterType="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into verify_user_line_management
        (
        id,user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line, loan_actual_line,
        loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line, bt_actual_line, bt_util_line, bt_rate,
        bt_period, shop_line, shop_avail_line, shop_actual_line, shop_util_line, shop_rate, shop_period,
        period_line_rate, account_status, is_closed, user_point, user_level, line_assign_time, strategy_id,
        strategy_type, is_active, last_id, loan_id, loan_key, reason_code, segment_code, test_code, status,
        remark, create_time, update_time, version, ext1, tmp_shop_line_end_time, tmp_loan_line_end_time
        )
        values
        (
        #{id},#{userKey}, #{sourceSystem}, #{creditLine}, #{availLine}, #{utilLine}, #{loanLine}, #{loanAvailLine}, #{loanActualLine},
        #{loanUtilLine}, #{loanRate}, #{loanPeriod}, #{btLine}, #{btAvailLine}, #{btActualLine}, #{btUtilLine}, #{btRate},
        #{btPeriod}, #{shopLine}, #{shopAvailLine}, #{shopActualLine},#{shopUtilLine}, #{shopRate}, #{shopPeriod},
        #{periodLineRate},#{accountStatus}, #{isClosed}, #{userPoint}, #{userLevel}, #{lineAssignTime},#{strategyId},
        #{strategyType},#{isActive},#{lastId},#{loanId},#{loanKey},#{reasonCode},#{segmentCode},#{testCode},#{status},
        #{remark}, #{createTime}, #{updateTime},#{version},#{ext1},#{tmpShopLineEndTime},#{tmpLoanLineEndTime}
        )
    </insert>


    <select id="getFirstCreateTimeByUserKey"  resultType="java.util.Date" parameterType="java.lang.String">
        select create_time
        from verify_user_line_management
        where user_key = #{userKey}
        and strategy_type in ('AMOUNT_ASSIGN_ALL','AMOUNT_ASSIGN_APR_ALL','AMOUNT_ASSIGN_IRR_ALL')
        order by id limit 1
    </select>

    <select id="getVerifyUserLineManagementMapper" resultType="java.util.Map">
        select id, loan_actual_line, shop_actual_line, loan_period, strategy_type, create_time from verify_user_line_management
        where line_assign_time > (CURRENT_DATE() - INTERVAL 1 YEAR) and user_key = #{userKey};
    </select>

    <select id="selectAfterId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />  FROM verify_user_line_management
        where <![CDATA[ id > #{startId} ]]>
        limit #{limit}
    </select>
    <select id="getLastPreloanByUserKey" resultMap="BaseResultMap">
        select * from verify_user_line_management
        where user_key = #{userKey} AND  source_system = #{sourceSystem}  and strategy_type = 'PRE_RELOAN_AUDIT_ALL' order by id DESC limit 0,1
    </select>

    <select id="getLastAssignDate" resultType="java.util.Date">
        select create_time from verify_user_line_management
        where user_key = #{userKey} AND  source_system = #{sourceSystem} AND (strategy_type like 'AMOUNT_ASSIGN_%'
            or strategy_type in ('IRR36_VERIFY_AMOUNT', 'IRR24_VERIFY_AMOUNT', 'WHITE_LIST_VERIFY_AMOUNT'))
        order by id DESC limit 0,1
    </select>

    <select id="getCheckVerifyUserLineManagement" resultMap="BaseResultMap">
        select * from verify_user_line_management
        where  user_key = #{userKey} order by id ;
    </select>

</mapper>