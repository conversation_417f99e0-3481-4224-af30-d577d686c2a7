<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.OriginVariableMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.OriginVariable">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="variable_code" property="variableCode" jdbcType="VARCHAR"/>
        <result column="variable_name" property="variableName" jdbcType="VARCHAR"/>
        <result column="value_type" property="valueType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, variable_code, variable_name, value_type, create_time, update_time
  </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_origin_variable
        order by id
    </select>

    <select id="selectByUpdateTime" resultMap="BaseResultMap" parameterType="java.util.Date">
        select
        <include refid="Base_Column_List"/>
        from admin_origin_variable
        where update_time >= #{updateTime}
    </select>
</mapper>