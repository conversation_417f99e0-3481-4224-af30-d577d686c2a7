<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.AdminAgencyMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.AdminAgency">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="icode" property="icode" jdbcType="VARCHAR"/>
        <result column="agency_desc" property="agencyDesc" jdbcType="VARCHAR"/>
        <result column="callback_url" property="callbackUrl" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="agency_pub_key" property="agencyPubKey" jdbcType="VARCHAR"/>
        <result column="pub_key" property="pubKey" jdbcType="VARCHAR"/>
        <result column="pri_key" property="priKey" jdbcType="VARCHAR"/>
        <result column="sec_level" property="secLevel"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

   <sql id="Base_Column_List">
     id, icode, agency_desc, callback_url, status, agency_pub_key, pub_key, pri_key, sec_level, create_time, update_time
   </sql>

    <sql id="table_name">
      admin_agency
    </sql>

    <select id="selectAllInMaster" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name" /> where status=1
    </select>

    <select id="selectByUpdateTimeInMaster" resultMap="BaseResultMap" parameterType="java.util.Date">
        (select  <include refid="Base_Column_List" /> from <include refid="table_name" /> where status=1 and update_time >= #{updateTime})
    </select>
</mapper>