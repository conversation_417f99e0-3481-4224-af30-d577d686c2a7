<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.EventStrategyRelationMapper" >
  <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.EventStrategyRelation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="event_id" property="eventId" jdbcType="BIGINT" />
    <result column="event_code" property="eventCode" jdbcType="VARCHAR" />
    <result column="event_name" property="eventName" jdbcType="VARCHAR" />
    <result column="strategy_type" property="strategyType" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, event_id, event_code, event_name, strategy_type, create_time, update_time
  </sql>
  <delete id="deleteByEventCode">
    DELETE FROM event_strategy_relation WHERE event_code = #{eventCode}
  </delete>

  <insert id="insert">
    INSERT INTO event_strategy_relation (event_id, event_code, event_name, strategy_type)
    VALUES (#{eventId}, #{eventCode}, #{eventName}, #{strategyType})
  </insert>

  <select id="selectEventCodesByStrategyType" resultType="java.lang.String">
    SELECT event_code FROM event_strategy_relation WHERE strategy_type = #{strategyType}
  </select>
</mapper>