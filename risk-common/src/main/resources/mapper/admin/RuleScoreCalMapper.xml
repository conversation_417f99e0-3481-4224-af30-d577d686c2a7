<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.RuleScoreCalMapper">

    <resultMap id="baseResult" type="com.youxin.risk.commons.model.admin.RuleScoreStrategy">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="rule_key" property="ruleKey" jdbcType="VARCHAR"></result>
        <result column="rule_version" property="ruleVersion" jdbcType="INTEGER"></result>
        <result column="rule_level" property="ruleLevel" jdbcType="INTEGER"></result>
        <result column="strategy" property="strategy" jdbcType="VARCHAR"></result>
        <result column="node" property="node" jdbcType="VARCHAR"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
    </resultMap>

    <resultMap id="ruleScoreVarResult" type="com.youxin.risk.commons.model.admin.RuleScoreVar">
        <id column="id" property="id" jdbcType="BIGINT"></id>
        <result column="rule_key" property="ruleKey" jdbcType="VARCHAR"></result>
        <result column="rule_version" property="ruleVersion" jdbcType="INTEGER"></result>
        <result column="variable_code" property="variableCode" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_key,rule_version,`strategy`,node
    </sql>

    <select id="findRuleScoreVar" resultMap="ruleScoreVarResult">
        select id,rule_key,rule_version,variable_code
        from rule_score_variable
        where rule_key = #{ruleKey} and rule_version = #{ruleVersion}
    </select>

    <select id="findBindRuleScoreByStrategyNode" resultMap="baseResult">
        SELECT
            rss.rule_key,
            rss.online_version AS rule_version,
            CASE
                WHEN rsr.relation_rule_key IS NOT NULL THEN 2
                ELSE 1
                END AS rule_level
        FROM (
            select rule_key, max(rule_version) as online_version
            from rule_score_strategy
            where node = #{node}
            GROUP BY rule_key
        ) rss
        join rule_score rs on rss.rule_key = rs.rule_key and rss.online_version = rs.rule_version
        LEFT JOIN rule_score_relation_online rsr
        ON rss.rule_key = rsr.rule_key
    </select>
</mapper>