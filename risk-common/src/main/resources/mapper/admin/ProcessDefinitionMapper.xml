<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.ProcessDefinitionMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.ProcessDefinition">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR"/>
        <result column="process_name" property="processName" jdbcType="VARCHAR"/>
        <result column="process_content" property="processContent" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="serial_node" property="serialNode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="process_json" property="processJson" jdbcType="VARCHAR"/>
        <result column="sub_process" property="subProcess" jdbcType="TINYINT"/>
        <result column="strategy_type" property="strategyType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, process_def_id, process_name, process_content, status, create_time, update_time, serial_node,process_json,sub_process
        ,strategy_type
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        order by id
    </select>

    <select id="selectPuDaoVerify" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where process_def_id like 'pudaoverify%'
        order by id
    </select>

    <select id="selectAllByProcessDefId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where process_def_id in
        <foreach collection="processDefIds" item="processDefId" index="index" open="(" separator="," close=")">
            #{processDefId}
        </foreach>
        order by id
    </select>

    <select id="selectByUpdateTime" resultMap="BaseResultMap" parameterType="java.util.Date">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where update_time >= #{updateTime}
    </select>
    <select id="selectByProcessDefId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_process_definition
        where process_def_id = #{processDefId};
    </select>
</mapper>