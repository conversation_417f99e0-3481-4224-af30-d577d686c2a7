/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.commons.vo.datavo;

import java.util.Date;

public class DpJob {
	private String jobID;
	private Boolean finished;
	private Boolean requested;
	private Boolean successful;
	private Date startTime;
	private Date endTime;
	private Object dataFile;
	private String systemID;
	private Object query;

	public String getJobID() {
		return jobID;
	}

	public void setJobID(String jobID) {
		this.jobID = jobID;
	}

	public boolean isFinished() {
		return finished;
	}

	public void setFinished(Boolean finished) {
		this.finished = finished;
	}

	public boolean isRequested() {
		return requested;
	}

	public void setRequested(<PERSON><PERSON><PERSON> requested) {
		this.requested = requested;
	}

	public Boolean isSuccessful() {
		return successful;
	}

	public void setSuccessful(<PERSON><PERSON><PERSON> successful) {
		this.successful = successful;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Object getDataFile() {
		return dataFile;
	}

	public void setDataFile(Object dataFile) {
		this.dataFile = dataFile;
	}

	public String getSystemID() {
		return systemID;
	}

	public void setSystemID(String systemID) {
		this.systemID = systemID;
	}

	public Object getQuery() {
		return query;
	}

	public void setQuery(Object query) {
		this.query = query;
	}

}
