package com.youxin.risk.commons.model;

import lombok.Builder;
import org.jaxen.expr.Step;

/**
 * <AUTHOR>
 * @date 2022-10-18
 */
@Builder
public class ShowStatisticsResult{
    /**
     * 步骤
     */
    private String step;
    /**
     * 总案件
     */
    private Integer totalCount;
    /**
     * 线上通过数量
     */
    private Integer onlinePassedCount;
    /**
     * 线上通过率
     */
    private String onlinePassedPre;
    /**
     * 线上件均
     */
    private Double avgPassedOnline;
    /**
     * 镜像通过数量
     */
    private Integer expPassedCount;
    /**
     * 镜像通过率
     */
    private String expPassedPre;

    /**
     * 镜像件均
     */
    private Double avgPassedExp;

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getOnlinePassedCount() {
        return onlinePassedCount;
    }

    public void setOnlinePassedCount(Integer onlinePassedCount) {
        this.onlinePassedCount = onlinePassedCount;
    }

    public String getOnlinePassedPre() {
        return onlinePassedPre;
    }

    public void setOnlinePassedPre(String onlinePassedPre) {
        this.onlinePassedPre = onlinePassedPre;
    }

    public Double getAvgPassedOnline() {
        return avgPassedOnline;
    }

    public void setAvgPassedOnline(Double avgPassedOnline) {
        this.avgPassedOnline = avgPassedOnline;
    }

    public Integer getExpPassedCount() {
        return expPassedCount;
    }

    public void setExpPassedCount(Integer expPassedCount) {
        this.expPassedCount = expPassedCount;
    }

    public String getExpPassedPre() {
        return expPassedPre;
    }

    public void setExpPassedPre(String expPassedPre) {
        this.expPassedPre = expPassedPre;
    }

    public Double getAvgPassedExp() {
        return avgPassedExp;
    }

    public void setAvgPassedExp(Double avgPassedExp) {
        this.avgPassedExp = avgPassedExp;
    }
}
