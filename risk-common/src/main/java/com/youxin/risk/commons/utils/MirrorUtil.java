package com.youxin.risk.commons.utils;

import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/9/23 14:45
 * @desc
 */
public class MirrorUtil {

    private final static String mirrorSuffix = "_mirror";

    public static String getMirror(String str) {
        if (str.contains(mirrorSuffix)) {
            return str;
        }
        return str + mirrorSuffix;
    }

    public static String getOnline(String str) {
        if (str.contains(mirrorSuffix)) {
            return str.replace(mirrorSuffix, "");
        }
        return str;
    }

    public static String getOnlineStep(String strategyNodeId, String eventCode) {
        Map<String, String> stepConfig = ApolloClientAdapter.getMapConfig(ApolloNamespaceEnum.ENGINE_SPACE, "step" +
                ".newAndOld.config", String.class);
        String onlineStep = stepConfig.get(strategyNodeId);
        if(StringUtils.isEmpty(onlineStep)){
            String key = String.format("%s_%s", strategyNodeId, eventCode);
            onlineStep = stepConfig.get(key);
            if(StringUtils.isEmpty(onlineStep)){
                onlineStep = strategyNodeId;
            }
        }
        return onlineStep;
    }
}
