package com.youxin.risk.commons.model.verify;

import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;


public class VerifyLineMiddle implements Serializable{
    
	/**
	 * @Fields serialVersionUID: 
	 */
	private static final long serialVersionUID = 7129402135350941303L;

	@Id
	private Integer id;

    private String userKey;

    private String sourceSystem;

	/**
     * 0-开始, 1-等待，2-完成
     */
    private Integer isFinished;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private Integer version;
    
    private String transId;

	public String getTransId() {
		return transId;
	}

	public void setTransId(String transId) {
		this.transId = transId;
	}

	public Integer getIsFinished() {
		return isFinished;
	}

	public void setIsFinished(Integer isFinished) {
		this.isFinished = isFinished;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}