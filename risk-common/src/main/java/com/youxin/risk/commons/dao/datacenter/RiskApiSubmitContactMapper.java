package com.youxin.risk.commons.dao.datacenter;

import com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitContact;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskApiSubmitContactMapper {

    Integer insertBatch(List<RiskApiSubmitContact> list);

    Integer updateById(RiskApiSubmitContact record);

    List<RiskApiSubmitContact> getByUserKey(@Param("userKey") String userKey, @Param("apiSource") String apiSource);

}