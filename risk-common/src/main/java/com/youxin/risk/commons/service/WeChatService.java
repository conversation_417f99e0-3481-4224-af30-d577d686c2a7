package com.youxin.risk.commons.service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 企业微信服务类
 * @author: juxiang
 * @create: 2023-03-01 19:14
 **/
@Service
public class WeChatService {
    private final Logger LOGGER = LoggerFactory.getLogger(WeChatService.class);

    private final String wechatWarnUrl;

    @Autowired
    public WeChatService(@Value("${wechat.robot.url:}") String wechatWarnUrl) {
        this.wechatWarnUrl = wechatWarnUrl;
    }

//    todo 测试  测试完就可以引用该类

    /**
     * 发送文本消息
     * @param msgContent 消息内容  文本内容，最长不超过2048个字节，必须是utf8编码
     * @param robotKey 机器人key
     */
    public void sendTextMsg(String msgContent, String robotKey){
        sendTextMsg(msgContent, robotKey, null);
    }

    /**
     * 发送文本消息并@所有人
     * @param msgContent 消息内容  文本内容，最长不超过2048个字节，必须是utf8编码
     * @param robotKey 机器人key
     */
    public void sendTextMsgWithWarnAll(String msgContent, String robotKey){
        sendTextMsg(msgContent, robotKey, "@all");
    }

    /**
     * 发送文本消息
     * @param msgContent 消息内容  文本内容，最长不超过2048个字节，必须是utf8编码
     * @param robotKey 机器人key
     * @param mentioned_list 被@的人
     */
    public void sendTextMsg(String msgContent, String robotKey, Object mentioned_list) {
        sendMsg(msgContent, robotKey, MsgType.text, mentioned_list);
    }


    /**
     *  发送Markdown消息
     * @param msgContent 消息内容   markdown内容，最长不超过4096个字节，必须是utf8编码
     * @param robotKey 机器人key
     */
    public void sendMarkdownMsg(String msgContent, String robotKey) {
        if (isInvalidMarkdownMsg(msgContent)) {
            return;
        }
        sendMsg(msgContent, robotKey, MsgType.markdown, null);
    }

    private boolean isInvalidMarkdownMsg(String msgContent) {
        if (StringUtils.isBlank(msgContent) || msgContent.length() > 4000) {
            LoggerProxy.warn("发送企微markdown消息", LOGGER, "发送消息体为空或长度超过4000字节，不执行发送操作，消息内容为={}", msgContent);
            return true;
        }
        return false;
    }

    /**
     * 发送消息
     * @param msgContent 消息内容
     * @param robotKey 机器人key
     * @param msgType 消息类型
     * @param mentioned_list 被@的人
     */
    protected void sendMsg(String msgContent, String robotKey, MsgType msgType, Object mentioned_list) {
        LoggerProxy.info("发送企微消息", LOGGER, "发送企微消息={}", msgContent);
        JSONObject requestBody = msgType.buildRequestBody(msgContent, mentioned_list);

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        SyncHTTPRemoteAPI.postJson(wechatWarnUrl + robotKey, requestBody.toJSONString(), header, 30000, false);
    }


    public String warnMessage(String msg) {
        return String.format("<font color=\"warning\">%s</font>", msg);
    }



    enum MsgType {
        text {
            @Override
            /**
             * 构建文本消息
             * @param msgContent 文本内容，最长不超过2048个字节，必须是utf8编码
             * @param mentioned_list 	userid的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人
             * @return
             */
            public JSONObject buildRequestBody(String msgContent, Object mentioned_list) {
                JSONObject text = new JSONObject();
                text.put("content", msgContent);
                if (mentioned_list != null) {
                    text.put("mentioned_list", mentioned_list);
                }

                JSONObject requestBody = new JSONObject();
                requestBody.put("msgtype", this.name());
                requestBody.put("text", text);
                return requestBody;
            }
        },
        markdown {
            @Override
            /**
             * 构建Markdown消息
             * @param msgContent markdown内容，最长不超过4096个字节，必须是utf8编码
             * @param mentioned_list 这个参数无效，暂未开放
             * @return
             */
            public JSONObject buildRequestBody(String msgContent, Object mentioned_list) {
                JSONObject markdown = new JSONObject();
                markdown.put("content", msgContent);

                JSONObject requestBody = new JSONObject();
                requestBody.put("msgtype", this.name());
                requestBody.put("markdown", markdown);
                return requestBody;
            }
        },
        file {
            @Override
            /**
             * 构建文件消息
             * @param media_id 文件id，可以通过上传媒体文件接口获取
             * @param mentioned_list 这个参数无效，暂未开放
             * @return
             */
            public JSONObject buildRequestBody(String media_id, Object mentioned_list) {
                JSONObject file = new JSONObject();
                file.put("media_id", media_id);

                JSONObject requestBody = new JSONObject();
                requestBody.put("msgtype", this.name());
                requestBody.put("file", file);
                return requestBody;
            }
        };

        // 抽象方法，每种消息类型需要实现该方法
        public abstract JSONObject buildRequestBody(String msgContent, Object mentioned_list);
    }
}
