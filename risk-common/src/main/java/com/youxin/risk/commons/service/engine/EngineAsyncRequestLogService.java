package com.youxin.risk.commons.service.engine;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.EngineAsyncRequestLogStatusEnum;
import com.youxin.risk.commons.dao.engine.EngineAsyncRequestLogMapper;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.model.EngineAsyncRequestLogExtend;
import com.youxin.risk.commons.model.engine.CallDiSuccessRate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018/10/11 10:36
 */
@EnableRetry
public class EngineAsyncRequestLogService {
    private static final Logger LOGGER = LoggerFactory.getLogger(EngineAsyncRequestLogService.class);

    private static final int MAX_ATTEMPTS = 3;
    private static final long DELAY = 100L;
    private static final double MULTI_PLIER = 1;

    @Resource
    private EngineAsyncRequestLogMapper engineAsyncRequestLogMapper;

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int insert(EngineAsyncRequestLog engineAsyncRequestLog) {
        return engineAsyncRequestLogMapper.insert(engineAsyncRequestLog);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public void insert(List<EngineAsyncRequestLog> list) {
        engineAsyncRequestLogMapper.multiInsert(list);
    }

    /**
     * 更新status、jobid。如果状态已经是FETCHED或FAILED则不做任何修改
     * @param engineAsyncRequestLog
     * @return
     */
    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateStatus(EngineAsyncRequestLog engineAsyncRequestLog) {
        return engineAsyncRequestLogMapper.updateStatus(engineAsyncRequestLog);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateStatus(EngineAsyncRequestLog engineAsyncRequestLog, EngineAsyncRequestLogStatusEnum oldStatus) {
        return engineAsyncRequestLogMapper.updateStatusWithOld(engineAsyncRequestLog, oldStatus.name());
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public EngineAsyncRequestLog getByAsyncRequestId(String asyncRequestId) {
        return engineAsyncRequestLogMapper.getByAsyncRequestId(asyncRequestId);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<EngineAsyncRequestLog> getList(String sessionId, String nodeId) {
        return engineAsyncRequestLogMapper.getList(sessionId, nodeId);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<EngineAsyncRequestLog> getListBySessionId(String sessionId) {
        return engineAsyncRequestLogMapper.getListBySessionId(sessionId);
    }

    public List<EngineAsyncRequestLogExtend> selectRetryLogByEventCodes(Collection<String> includeEventCodes, Collection<String> excludeEventCodes,
                                                                        String currentTime, int limit) {
        return engineAsyncRequestLogMapper.selectRetryLogByEventCodes(includeEventCodes, excludeEventCodes, currentTime, limit);
    }


    // todo：跨库查询
    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<EngineAsyncRequestLogExtend> selectNotRequiredAndExpiredRequestLog() {
        return engineAsyncRequestLogMapper.selectNotRequiredAndExpiredRequestLog();
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<EngineAsyncRequestLogExtend> selectFetchedOldAndExpiredRequestLog() {
        return engineAsyncRequestLogMapper.selectFetchedOldAndExpiredRequestLog();
    }

    public int failNotRequired(String sessionId, String nodeId) {
        return engineAsyncRequestLogMapper.failNotRequired(sessionId, nodeId);
    }

    /**
     * 将list转换为map，key是sessionId
     */
    public Map<String, List<EngineAsyncRequestLogExtend>> flatSessionDataMap(List<EngineAsyncRequestLogExtend> engineAsyncRequestLogs) {
        Map<String, List<EngineAsyncRequestLogExtend>> sessionDataMap = Maps.newHashMap();
        for (EngineAsyncRequestLogExtend asyncRequestLog : engineAsyncRequestLogs) {
            List<EngineAsyncRequestLogExtend> sessionLogs = sessionDataMap.get(asyncRequestLog.getSessionId());
            if(Objects.isNull(sessionLogs)) {
                sessionLogs = Lists.newArrayList();
                sessionDataMap.put(asyncRequestLog.getSessionId(), sessionLogs);
            }
            sessionLogs.add(asyncRequestLog);
        }
        return sessionDataMap;
    }

    public void deleteById(Long id){
        engineAsyncRequestLogMapper.deleteById(id);
    }

    public List<EngineAsyncRequestLog> getListBySessionId(List<String> sessionIdList){
        return engineAsyncRequestLogMapper.getListBySessionIdList(sessionIdList);
    }

}
