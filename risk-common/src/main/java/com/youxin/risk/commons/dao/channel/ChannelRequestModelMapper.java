package com.youxin.risk.commons.dao.channel;

import com.youxin.risk.commons.model.ChannelRequestModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ChannelRequestModelMapper {

    int insertIgnore(ChannelRequestModel entry);

    int lockWaitSubmit(@Param("id") long id);

    int lockFailed(@Param("id") long id);

    int lockWaitCallback(@Param("id") long id);

    int lockPointAgencyWaitCallback(@Param("id") long id);

    int unlock(@Param("id") long id);

    int updateStatusToSubmit(@Param("requestId") String requestId);

    int updateStatusToFail(ChannelRequestModel entry);

    int updateStatusToFetched(ChannelRequestModel entry);

    int updateFundPlatCallbackMsg(ChannelRequestModel entry);

    int updateStatusToCallback(@Param("id") long id);

    int deleteByIds(@Param("items") List<ChannelRequestModel> items);

    List<ChannelRequestModel> selectWaitSubmit(@Param("size") int size);

    List<ChannelRequestModel> selectWaitCallback(@Param("size") int size);

    List<ChannelRequestModel> selectPointAgencyWaitCallback(@Param("size") int size, @Param("agencyCode") String agencyCode);

    List<ChannelRequestModel> selectFailed(@Param("size") int size);

    List<ChannelRequestModel> selectLockTimeout(@Param("size") int size);

    List<ChannelRequestModel> selectDelete();

    ChannelRequestModel selectByRequestId(@Param("requestId") String requestId);

	List<Map<String, Object>> selectNotCallback();

    int setFailedByRequestIds(@Param("requestIdList") List<String> requestIdList);

    List<ChannelRequestModel> selectByRequestIdsAndExpireTime(@Param("requestIdList") List<String> requestIdList);

    int updateExpireTimeByRequestIds(@Param("requestIdList") List<String> requestIdList);
}