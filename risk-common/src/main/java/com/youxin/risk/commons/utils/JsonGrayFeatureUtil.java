package com.youxin.risk.commons.utils;

import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * JSON灰度功能工具类
 * 处理JSON键名格式转换的灰度发布相关功能
 */
public class JsonGrayFeatureUtil {
    private final static Logger logger = LoggerFactory.getLogger(JsonGrayFeatureUtil.class);

    private static RetryableJedis retryableJedis;

    // 灰度功能相关的配置键
    private static final String UNDERSCORE_GRAY_ENABLED_KEY = "risk.json.underscore.gray.enabled";
    private static final String UNDERSCORE_GRAY_USERS_KEY = "risk.json.underscore.gray.users";
    private static final String UNDERSCORE_GRAY_RATIO_KEY = "risk.json.underscore.gray.ratio";
    private static final String UNDERSCORE_FULL_SWITCH_KEY = "risk.json.underscore.full.switch";
    private static final String REDIS_GRAY_PREFIX = "risk:json:underscore:gray:";
    private static final int REDIS_GRAY_EXPIRE = 86400; // 24小时过期

    /**
     * 检查下划线转换灰度功能是否开启
     *
     * @return 如果灰度功能开启返回true，否则返回false
     */
    public static boolean isUnderscoreGrayEnabled() {
        return Boolean.parseBoolean(NacosClient.getByNameSpace(ApolloNamespace.commonSpace, UNDERSCORE_GRAY_ENABLED_KEY, "false"));
    }

    public static boolean checkGrayFeature(String userKey) {
        // 灰度功能未开启，则不应用下划线转换
        if (!isUnderscoreGrayEnabled()) {
            return false;
        }

        return checkGrayFeatureByConfig(userKey);
    }

    public static boolean checkGrayFeatureByConfig(String userKey) {
        // 0. 检查是否开启全量切换，如果开启则直接返回true
        boolean fullSwitch = Boolean.parseBoolean(NacosClient.getByNameSpace(ApolloNamespace.commonSpace, UNDERSCORE_FULL_SWITCH_KEY, "false"));
        if (fullSwitch) {
            return true;
        }

        // 1. 检查nacos配置中是否包含该userKey
        String grayUsersStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, UNDERSCORE_GRAY_USERS_KEY, "");
        if (StringUtils.isNotBlank(grayUsersStr)) {
            Set<String> grayUsers = Arrays.stream(grayUsersStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toSet());
            if (grayUsers.contains(userKey)) {
                return true;
            }
        }

        // 2. 检查Redis中是否存在该userKey的灰度标识
        return extendGrayFeature(userKey);
    }

    /**
     * 获取Redis客户端实例
     *
     * @return Redis客户端实例
     */
    private static RetryableJedis getRiskRetryAbleJedis() {
        if (retryableJedis == null) {
            try {
                retryableJedis = (RetryableJedis) SpringContext.getBean("retryableJedis");
            } catch (Exception e) {
                // 获取Bean失败，返回null
            }
        }
        return retryableJedis;
    }

    /**
     * 检查Redis中是否存在该userKey的灰度标识，并延长其过期时间
     *
     * @param userKey 用户标识
     * @return 如果存在有效的灰度标识返回true，否则返回false
     */
    public static boolean extendGrayFeature(String userKey) {
        if(null == userKey){
            return true;
        }
        String redisKey = REDIS_GRAY_PREFIX + userKey;
        try {
            if (getRiskRetryAbleJedis() != null) {
                String redisValue = getRiskRetryAbleJedis().get(redisKey);
                if (redisValue != null) {
                    // 如果获取到灰度标记，延长Redis键的过期时间，确保案件处理过程中不会失效
                    try {
                        getRiskRetryAbleJedis().setex(redisKey, REDIS_GRAY_EXPIRE, redisValue);
                        if (JsonLogUtil.isLogEnabled()) {
                            logger.info("[extendGrayFeature] 延长Redis灰度标记过期时间: key={}, value={}, expire={}", 
                                    redisKey, redisValue, REDIS_GRAY_EXPIRE);
                        }
                    } catch (Exception ex) {
                        // 延长过期时间失败不影响正常流程，只记录日志
                        logger.warn("[extendGrayFeature] 延长Redis灰度标记过期时间失败: {}", ex.getMessage());
                    }
                    return "1".equals(redisValue);
                }
            }
        } catch (Exception e) {
            logger.warn("[extendGrayFeature] 从Redis获取灰度标记失败: {}", e.getMessage());
        }
        return false;
    }


    /**
     * 设置用户的灰度标识到Redis中
     *
     * @param userKey 用户标识
     * @return 如果设置成功返回true，否则返回false
     */
    public static boolean setGrayFeature(String userKey) {
        if(null == userKey){
            return false;
        }

        // 灰度功能未开启，则不应用下划线转换
        if (!isUnderscoreGrayEnabled()) {
            return false; // 灰度功能未开启,不需要设置
        }

        // 检查是否已经是灰度
        if(checkGrayFeatureByConfig(userKey)){
            return true; // 已经是灰度用户,不需要设置
        }

        // 按比例灰度
        double grayRatio = Double.parseDouble(NacosClient.getByNameSpace(ApolloNamespace.commonSpace, UNDERSCORE_GRAY_RATIO_KEY, "0"));
        if(RandomUtils.nextInt(0, 101) <= grayRatio){
            // 记录日志
            if (JsonLogUtil.isLogEnabled()) {
                logger.info("[setGrayFeature] 开始设置灰度标识: userKey={}, 调用栈: {}",
                        userKey, JsonLogUtil.getCallerStackTrace());
            }

            // 获取Redis客户端实例
            RetryableJedis jedis = getRiskRetryAbleJedis();
            if (jedis == null) {
                logger.warn("[setGrayFeature] 获取Redis客户端实例失败");
                return false;
            }

            // 构造Redis键
            String redisKey = REDIS_GRAY_PREFIX + userKey;

            try {
                // 设置灰度标识到Redis，使用统一的过期时间
                jedis.setex(redisKey, REDIS_GRAY_EXPIRE, "1");

                if (JsonLogUtil.isLogEnabled()) {
                    logger.info("[setGrayFeature] 成功设置灰度标识: key={}, expire={}, 调用栈: {}",
                            redisKey, REDIS_GRAY_EXPIRE, JsonLogUtil.getCallerStackTrace());
                }

                return true;
            } catch (Exception e) {
                logger.warn("[setGrayFeature] 设置灰度标识失败: {}", e.getMessage());
                return false;
            }
        }
        return false;
    }
}