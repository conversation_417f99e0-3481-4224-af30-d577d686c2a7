package com.youxin.risk.commons.utils;

import com.youxin.risk.commons.constants.EngineDataVoPathEnum;
import org.apache.commons.lang.StringUtils;

import java.util.AbstractMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 把多层Map拍平为一层
 */
public final class FlatMapUtil {

    private FlatMapUtil() {
        throw new AssertionError("No instances for you!");
    }

    public static Map<String, Object> flatten(Map<String, Object> map) {
        return map.entrySet().stream()
                .flatMap(FlatMapUtil::flatten)
                .collect(LinkedHashMap::new, (m, e) -> m.put("/" + e.getKey(), e.getValue()), LinkedHashMap::putAll);
    }

    private static Stream<Map.Entry<String, Object>> flatten(Map.Entry<String, Object> entry) {

        if (entry == null) {
            return Stream.empty();
        }

        if (entry.getValue() instanceof Map<?, ?>) {
            return ((Map<?, ?>) entry.getValue()).entrySet().stream()
                    .flatMap(e -> flatten(new AbstractMap.SimpleEntry<>(entry.getKey() + "/" + e.getKey(), e.getValue())));
        }

        if (entry.getValue() instanceof List<?>) {
            List<?> list = (List<?>) entry.getValue();
            return IntStream.range(0, list.size())
                    .mapToObj(i -> new AbstractMap.SimpleEntry<String, Object>(entry.getKey() + "/" + i, list.get(i)))
                    .flatMap(FlatMapUtil::flatten);
        }

        return Stream.of(entry);
    }

    public static String getFlatMapDataVoKey(String saveType, String thirdPartyType){
        EngineDataVoPathEnum saveDataType = StringUtils.isBlank(saveType) ? EngineDataVoPathEnum.THIRD_PARTY : EngineDataVoPathEnum.valueOf(saveType);
        String result;
        switch (saveDataType) {
            case ROOT:
                result ="/"+thirdPartyType;
                break;
            case THIRD_PARTY:
                result = "/thirdPartyData/"+thirdPartyType;
                break;
            default:
                result = "/thirdPartyData/"+thirdPartyType;
        }
        return result;
    }
}