package com.youxin.risk.commons.model;

import java.io.Serializable;

/**
 *  online feature depend
 */
public class RmFeatureDependProperty implements Serializable {

    private static final long serialVersionUID = -5235883771676452721L;

    private Integer id;

    private Long submitDetailId;

    private String type;

    private String dataName;

    private String dataCode;

    private String dataJoinPath;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getSubmitDetailId() {
        return submitDetailId;
    }

    public void setSubmitDetailId(Long submitDetailId) {
        this.submitDetailId = submitDetailId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getDataName() {
        return dataName;
    }

    public void setDataName(String dataName) {
        this.dataName = dataName == null ? null : dataName.trim();
    }

    public String getDataCode() {
        return dataCode;
    }

    public void setDataCode(String dataCode) {
        this.dataCode = dataCode == null ? null : dataCode.trim();
    }

    public String getDataJoinPath() {
        return dataJoinPath;
    }

    public void setDataJoinPath(String dataJoinPath) {
        this.dataJoinPath = dataJoinPath == null ? null : dataJoinPath.trim();
    }
}