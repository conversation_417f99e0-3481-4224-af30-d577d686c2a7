/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.commons.mongo;

import java.util.List;

import com.youxin.risk.commons.vo.OfflineBatchRecordStatus;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;

import com.youxin.risk.commons.vo.OfflineBatchRecordVo;

@EnableRetry
public class OfflineBatchRecordMongoDao {

    private static final int MAX_ATTEMPTS = 3;
    private static final long DELAY = 100L;
    private static final double MULTI_PLIER = 1;

    private MongoTemplate template;
    private String collectionName;
    public OfflineBatchRecordMongoDao() {
        collectionName = "offlineBatchRecord";
    }
    public List<OfflineBatchRecordVo> getRecords(Integer batchId, Integer processId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("batchId").is(batchId)
                .and("status").is(OfflineBatchRecordStatus.INIT.getCode())
                .and("processId").is(processId));
        return template.find(query, OfflineBatchRecordVo.class, collectionName);
    }

    public List<OfflineBatchRecordVo> getRecords(Integer batchId, Integer processId,Integer startId,Integer limit) {
        Query query = new Query();
        query.addCriteria(Criteria.where("batchId").is(batchId)
                .and("status").is(OfflineBatchRecordStatus.INIT.getCode())
                .and("processId").is(processId).and("recordId")
                .gte(startId)).limit(limit);
        return template.find(query, OfflineBatchRecordVo.class, collectionName);
    }


    public List<OfflineBatchRecordVo> getRecordsByStatus(Integer batchId, Integer status) {
        Query query = new Query();
        query.addCriteria(Criteria.where("batchId").is(batchId).and("status").is(status));
        return template.find(query, OfflineBatchRecordVo.class, collectionName);
    }
    public long countByStatus(Integer batchId, Integer status) {
        Query query = new Query();
        query.addCriteria(Criteria.where("batchId").is(batchId).and("status").is(status));
        return template.count(query, OfflineBatchRecordVo.class, collectionName);
    }

    public void updateStatus(OfflineBatchRecordVo recordVo) {
        Query query = new Query(Criteria.where("_id").is(recordVo.getId()));
        Update update = new Update();
        update.set("status", recordVo.getStatus());
        template.updateFirst(query, update, collectionName);
    }

    public void update2Success(OfflineBatchRecordVo recordVo) {
        recordVo.setStatus(OfflineBatchRecordStatus.SUCCEEDED.getCode());
        updateStatus(recordVo);
    }
    public void update2Failed(OfflineBatchRecordVo recordVo) {
        recordVo.setStatus(OfflineBatchRecordStatus.FAILED.getCode());
        updateStatus(recordVo);
    }

    public void updateManyStatusToStopped(int batchId) {
        Query query = new Query(Criteria.where("batchId").is(batchId)
                .and("status").is(OfflineBatchRecordStatus.INIT.getCode()));
        Update update = new Update();
        update.set("status", OfflineBatchRecordStatus.STOPPED.getCode());
        template.updateMulti(query, update, collectionName);
    }


    public MongoTemplate getTemplate() {
        return template;
    }

    public void setTemplate(MongoTemplate template) {
        this.template = template;
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<OfflineBatchRecordVo> getRecordsByPage(Integer batchId, Integer processId, Integer pageSize, Integer pageNum) {
        Pageable pageable = new PageRequest(pageNum-1, pageSize);
        Query query = new Query();
        query.addCriteria(Criteria.where("batchId").is(batchId)
                .and("status").is(OfflineBatchRecordStatus.INIT.getCode())
                .and("processId").is(processId));
        query.with(pageable);
        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createTime")));
        return template.find(query, OfflineBatchRecordVo.class, collectionName);
    }
}
