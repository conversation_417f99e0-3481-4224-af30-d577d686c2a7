package com.youxin.risk.commons.limiter;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 真正流量计算器
 *
 * <AUTHOR>
 */
public class RealLimitCalculator {
    private static final Logger LOGGER = LoggerFactory.getLogger(RealLimitCalculator.class);
    private final RateLimitParser parser;
    private volatile boolean parsedFlag = false;
    private Map<String, List<RateLimit>> rateLimitMap;

    public RealLimitCalculator(RateLimitParser parser) {
        this.parser = parser;
    }

    public int calculatorRealLimit(String eventCode, int configLimit) {
        // 解析操作 为了防止多次解析进行并发控制
        parseAndInit();
        try {
            if (rateLimitMap.containsKey(eventCode)) {
                List<RateLimit> rateLimitList = rateLimitMap.get(eventCode);
                for (RateLimit rateLimit : rateLimitList) {
                    if (rateLimit.getMatchedRateLimit() != null) {
                        int realLimit = rateLimit.getRealLimit(configLimit);
                        LoggerProxy.info("hitRateLimit", LOGGER, "realLimit={},eventCode={},matchedRateLimit={}", realLimit,eventCode,rateLimit);
                        return realLimit;
                    }
                }
            }
        } catch (Exception e) {
            LoggerProxy.error("realLimitCalculatorError", LOGGER, "eventCode={}", eventCode, e);
        }
        return configLimit;
    }

    private void parseAndInit() {
        if (!parsedFlag) {
            synchronized (this) {
                if (!parsedFlag) {
                    rateLimitMap = parser.parse();
                    parsedFlag = true;
                }
            }
        }
    }

}
