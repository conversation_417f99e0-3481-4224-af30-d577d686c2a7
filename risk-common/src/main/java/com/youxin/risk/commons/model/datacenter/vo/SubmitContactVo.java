package com.youxin.risk.commons.model.datacenter.vo;

import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

public class SubmitContactVo extends VerifyCommonData {

    private static final long serialVersionUID = 8398531910494933816L;
    private List<SubmitContactDetailsVo> contactList;

    private List<SubmitContactDetailsVo> addContactList;

    // 尝试将两个联系人填写为同一人  0无 1有
    private String isRepeat;

    public String getIsRepeat() {
        return isRepeat;
    }

    public void setIsRepeat(String isRepeat) {
        this.isRepeat = isRepeat;
    }

    public List<SubmitContactDetailsVo> getContactList() {
        return contactList;
    }

    public void setContactList(List<SubmitContactDetailsVo> contactList) {
        this.contactList = contactList;
    }

    public List<SubmitContactDetailsVo> getAddContactList() {
        return addContactList;
    }

    public void setAddContactList(List<SubmitContactDetailsVo> addContactList) {
        this.addContactList = addContactList;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
