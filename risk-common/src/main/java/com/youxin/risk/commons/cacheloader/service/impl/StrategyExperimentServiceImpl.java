package com.youxin.risk.commons.cacheloader.service.impl;

import com.youxin.risk.commons.cacheloader.service.StrategyExperimentService;
import com.youxin.risk.commons.dao.admin.StrategyExperimentMapper;
import com.youxin.risk.commons.model.StrategyExperiment;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/29 15:36
 */
public class StrategyExperimentServiceImpl implements StrategyExperimentService {
    @Resource
    private StrategyExperimentMapper strategyExperimentMapper;

    @Override
    public List<StrategyExperiment> selectAll() {
        return strategyExperimentMapper.selectAll();
    }

    @Override
    public List<StrategyExperiment> selectByUpdateTime(Date updateTime) {
        return strategyExperimentMapper.selectByUpdateTime(updateTime);
    }

    @Override
    public List<StrategyExperiment> selectEnable() {
        return strategyExperimentMapper.selectEnable();
    }
}
