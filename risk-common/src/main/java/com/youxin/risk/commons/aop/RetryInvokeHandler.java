package com.youxin.risk.commons.aop;

import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * retry
 * 
 * <AUTHOR>
 *
 */
public class RetryInvokeHandler implements MethodInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(RetryInvokeHandler.class);

    private int retryNum = 3;

    private long period = 1000L;

    @Override
    public Object invoke(MethodInvocation methodInvocation) throws Throwable {
        for (int i = 0; i < retryNum; i++) {
            try {
                Object ret = methodInvocation.proceed();
                return ret;
            } catch (Throwable e) {
                if (i < (retryNum - 1)) {
                    logger.warn("", e);
                    try {
                        Thread.sleep(period);
                    } catch (Exception e2) {
                        // nothing
                    }
                    continue;
                }
                throw e;
            }
        }
        return null;
    }

    public int getRetryNum() {
        return retryNum;
    }

    public void setRetryNum(int retryNum) {
        this.retryNum = retryNum;
    }

    public long getPeriod() {
        return period;
    }

    public void setPeriod(long period) {
        this.period = period;
    }

}
