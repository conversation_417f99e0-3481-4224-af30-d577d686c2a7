package com.youxin.risk.commons.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.*;

public class XmlUtils {

    private static final Logger LOG = LoggerFactory.getLogger(XmlUtils.class);

    /**
     * 把 newElement中的子元素 merge到 oldElement中
     *
     * @param oldElement
     * @param newElement
     */
    public static void mergeXml(Element oldElement, Element newElement) {

        Map<String, Object> oldElementMap = getElementMap(oldElement);
        Map<String, Object> newElementMap = getElementMap(newElement);

        // 遍历 newElementMap 中的 子元素
        for (Map.Entry<String, Object> entity : newElementMap.entrySet()) {
            // 在 oldElement 中没有同名子元素 ，直接覆盖
            if (!oldElementMap.containsKey(entity.getKey())) {
                replaceByNewElement(oldElement, entity);
            } else {

                // 该子元素 在 oldElement 中是 list
                if (oldElementMap.get(entity.getKey()) instanceof List) {

                    // 把 oldElement 中这个元素都删掉
                    List<Element> childElementList = (List<Element>) oldElementMap.get(entity.getKey());
                    for (Element child : childElementList) {
                        oldElement.remove(child);
                    }
                    // 用新的元素替换
                    replaceByNewElement(oldElement, entity);
                }
                // 该子元素 在 newElement中是 list
                else if (entity.getValue() instanceof List) {
                    // 把 oldElement 中同名的子元素都删掉
                    Element removeElement = (Element) oldElementMap.get(entity.getKey());
                    oldElement.remove(removeElement);

                    //添加entity中的 list 到 oldElement中
                    addNewElementList(oldElement, entity);
                }
                // 该子元素 在 oldElement 和 newElement中是 都不是 list
                else {
                    Element child1 = (Element) oldElementMap.get(entity.getKey());
                    Element child2 = (Element) entity.getValue();

                    // 如果其中子元素已经是叶子元素 直接覆盖
                    if (child1.isTextOnly() || child2.isTextOnly()) {

                        // 如果 新的xml 中的element是只读并且为 Invalid ，不覆盖原来的特征项
                        if (child2.isTextOnly() && "Invalid".equals(child2.getText())) {
                            continue;
                        }
                        // 能来到这的话，就用新xml中的element覆盖原来的特征项
                        child2.detach();
                        oldElement.remove(child1);
                        oldElement.add(child2);
                    }
                    // 递归调用
                    else {
                        mergeXml(child1, child2);
                    }
                }
            }
        }
    }

    public static Map<String, Object> flatXMLString2Map(final String strxml) throws JDOMException, IOException {
        if (null == strxml || "".equals(strxml)) {
            return null;
        }

        Map<String, Object> m = new HashMap<String, Object>();
        InputStream in = new ByteArrayInputStream(strxml.getBytes("UTF-8"));
        SAXBuilder builder = new SAXBuilder();
        org.jdom.Document doc = builder.build(in);
        org.jdom.Element root = doc.getRootElement();
        m.put(root.getName(), getFlatValueByRecursion(root));
        // 关闭流
        in.close();

        return m;
    }

    private static Object getFlatValueByRecursion(org.jdom.Element node){
        Map<String,Object> m = new HashMap<>();
        List list = node.getChildren();
        if(CollectionUtils.isEmpty(list)){
            return node.getText();
        }
        Iterator it = list.iterator();
        while(it.hasNext()){
            org.jdom.Element e = (org.jdom.Element) it.next();
            if(m.containsKey(e.getName())){
                Object value = m.get(e.getName());
                List l;
                if(value instanceof  List){
                    l = (List)value;
                }else{
                    l = new ArrayList();
                    l.add(value);
                }
                l.add(getFlatValueByRecursion(e));
                m.put(e.getName(),l);
            }else {
                m.put(e.getName(), getFlatValueByRecursion(e));
            }
        }
        return m;
    }

    /**
     * 获得 xml 子元素名称对应 的map
     * key是 element的 name
     * value 是 单一element 或 同名element构成的list
     *
     * @param root
     * @return
     */
    private static Map<String, Object> getElementMap(Element root) {

        Map<String, Object> elementMap = new HashMap<>();
        List<Element> childElements = root.elements();
        String preElementName = null;
        Element preElement;
        List<Element> preElementList;
        for (Element element : childElements) {

            // 如果之前已经有了同名子元素，说明该子元素是一个 list
            if (elementMap.containsKey(element.getName())) {
                // 如果 map中的 object 是 list 说明已经放入过了，直接把当前元素加入 list即可
                if (elementMap.get(preElementName) instanceof List) {
                    preElementList = (List<Element>) elementMap.get(preElementName);
                    preElementList.add(element);
                }
                // 否则这个元素是这个名字下的第二个元素，把该子元素转换成 list
                else {
                    preElement = (Element) elementMap.get(preElementName);
                    preElementList = new ArrayList<>();
                    preElementList.add(preElement);
                    preElementList.add(element);
                    elementMap.put(preElementName, preElementList);
                }
            }
            // 否则 直接加入
            else {
                elementMap.put(element.getName(), element);
            }
            preElementName = element.getName();
        }
        return elementMap;
    }

    /**
     * @param element 原来的Element
     * @param entity  entity中的value 用来替换element中原来的子元素
     */
    private static void replaceByNewElement(Element element, Map.Entry<String, Object> entity) {

        // 如果是 list 调动添加 list的方法
        if (entity.getValue() instanceof List) {
            addNewElementList(element, entity);
        }
        // 添加单一 element
        else {
            addNewElement(element, entity);
        }
    }

    /**
     * @param element 原来的Element
     * @param entity  包含新添加的 elementList
     */
    private static void addNewElementList(Element element, Map.Entry<String, Object> entity) {

        List<Element> childElementList = (List<Element>) entity.getValue();
        for (Element child : childElementList) {
            child.detach();
            element.add(child);
        }
    }

    /**
     * @param element 原来的Element
     * @param entity  包含新添加的 element
     */
    private static void addNewElement(Element element, Map.Entry<String, Object> entity) {

        Element addElement = (Element) entity.getValue();
        addElement.detach();
        element.add(addElement);
    }


    //将mainxml中被覆盖的节点备份到backup对应节点下
    private static void removeEleToBackupPath(Element mElement, String vElementPath, Element rootelement) {
        //实时特征备份路径/backup/processMerge/。
        try {
            String[] nodes = ("/backup/processMerge/" + vElementPath).split("/");
            Element parent = rootelement;
            for (int i = 1; i < nodes.length; i++) {
                if (StringUtils.isNotEmpty(nodes[i])) {
                    Element element = parent.element(nodes[i]);
                    if (element == null) {
                        element = parent.addElement(nodes[i]);
                    }
                    parent = element;
                }
            }
            parent.add(mElement);
        } catch (Exception e) {
            LOG.error("mergeXmlRemoveEleToBackupPathError,melement=" + mElement.getName() + "vElementPath=" + vElementPath, e);
        }
    }


    public static String mergeXml(String mainXml, String viceXml) {

        try {
            mainXml = stripNonValidXMLCharacters(mainXml);
            viceXml = stripNonValidXMLCharacters(viceXml);
            Document mainDocument = new SAXReader().read(new ByteArrayInputStream(mainXml.getBytes("UTF-8")));
            Element rootelement = mainDocument.getRootElement();
            Document viceDocument = new SAXReader().read(new ByteArrayInputStream(viceXml.getBytes("UTF-8")));
            Element viceRootelement = viceDocument.getRootElement();

            mergeXml(rootelement, viceRootelement);

            return mainDocument.asXML()
                    .replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "")
                    .replace("\r", "").replace("\n", "");
        } catch (Exception e) {
            LOG.error("mergeXml error,errMsg=", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取使用新的merge方法的标志
     *
     * @return
     */
    private static boolean getNewMergeMethodFlag() {

        String useNewMethod = "1";

        try {
            useNewMethod = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "xml.merge.new.method.flag", "1");
        } catch (Exception e) {
            LOG.error("getNewMergeMethodFlagError", e);
        }
        return "1".equals(useNewMethod);
    }


    public static void main(String[] args) {
        String xml1 = "<featurephone_test><standard><d>f</d></standard></featurephone_test>";
        String xml2 = "<featurephone_test><standard><td_service_sd><td_2794855_id_number>7.0</td_2794855_id_number><td_2794855_device_id_hit_rate>0.125</td_2794855_device_id_hit_rate><fraudResult_hitRules><decision>Accept</decision><score>2.0</score><id>2794309</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>2.0</score><id>2794339</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>6.0</score><id>2794397</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>10.0</score><id>2794437</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>3.0</score><id>2794439</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794441</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794443</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794445</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794447</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794449</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>6.0</score><id>2794451</id></fraudResult_hitRules><td_2794845_id_number_o2o>-999.0</td_2794845_id_number_o2o><td_2794869_p2p>-999.0</td_2794869_p2p><td_score_prob>0.************</td_score_prob><td_2794849_hlw_rate>-999.0</td_2794849_hlw_rate><td_2794849_hlw>-999.0</td_2794849_hlw><td_rule_result_2794855>8.0</td_rule_result_2794855><td_2794855_wsyh>1.0</td_2794855_wsyh><td_2794855_id_number_hit_rate>0.875</td_2794855_id_number_hit_rate><td_2794855_account_mobile_hit_rate>0.75</td_2794855_account_mobile_hit_rate><td_2794855_xyk>1.0</td_2794855_xyk><td_2794843_ybxf>1.0</td_2794843_ybxf><td_2794845_id_number_o2o_hit_rate>-999.0</td_2794845_id_number_o2o_hit_rate><td_2794855_device_id>1.0</td_2794855_device_id><td_2794851_yhxj>-999.0</td_2794851_yhxj><td_2794845_id_number>3.0</td_2794845_id_number><td_2794869_p2p_rate>-999.0</td_2794869_p2p_rate><td_2794849_yygr>-999.0</td_2794849_yygr><td_2794849_account_mobile>4.0</td_2794849_account_mobile><td_2794869_account_mobile>-999.0</td_2794869_account_mobile><td_2794855_dsf>-999.0</td_2794855_dsf><td_2794841_ybxf>1.0</td_2794841_ybxf><td_2794855_wsyh_rate>0.125</td_2794855_wsyh_rate><td_2794855_account_mobile>6.0</td_2794855_account_mobile><td_2794841_xedk>1.0</td_2794841_xedk><td_rule_result_2794845>3.0</td_rule_result_2794845><td_rule_result_2794869>-999.0</td_rule_result_2794869><td_2794847_xedk>2.0</td_2794847_xedk><td_2794855_dsf_rate>-999.0</td_2794855_dsf_rate><td_2794849_yygr_rate>-999.0</td_2794849_yygr_rate><td_rule_result_2794849>6.0</td_rule_result_2794849></td_service_sd></standard></featurephone_test>";
        String xml3 = "<featurephone_test><standard><td_service_sd><td_2794855_id_number>7.0</td_2794855_id_number><td_2794855_device_id_hit_rate>0.125</td_2794855_device_id_hit_rate><td_2794845_id_number_o2o>-999.0</td_2794845_id_number_o2o><td_2794869_p2p>-999.0</td_2794869_p2p><td_score_prob>0.************</td_score_prob><td_2794849_hlw_rate>-999.0</td_2794849_hlw_rate><td_2794849_hlw>-999.0</td_2794849_hlw><td_rule_result_2794855>8.0</td_rule_result_2794855><td_2794855_wsyh>1.0</td_2794855_wsyh><td_2794855_id_number_hit_rate>0.875</td_2794855_id_number_hit_rate><td_2794855_account_mobile_hit_rate>0.75</td_2794855_account_mobile_hit_rate><td_2794855_xyk>1.0</td_2794855_xyk><td_2794843_ybxf>1.0</td_2794843_ybxf><td_2794845_id_number_o2o_hit_rate>-999.0</td_2794845_id_number_o2o_hit_rate><td_2794855_device_id>1.0</td_2794855_device_id><td_2794851_yhxj>-999.0</td_2794851_yhxj><td_2794845_id_number>3.0</td_2794845_id_number><td_2794869_p2p_rate>-999.0</td_2794869_p2p_rate><td_2794849_yygr>-999.0</td_2794849_yygr><td_2794849_account_mobile>4.0</td_2794849_account_mobile><td_2794869_account_mobile>-999.0</td_2794869_account_mobile><td_2794855_dsf>-999.0</td_2794855_dsf><td_2794841_ybxf>1.0</td_2794841_ybxf><td_2794855_wsyh_rate>0.125</td_2794855_wsyh_rate><td_2794855_account_mobile>6.0</td_2794855_account_mobile><td_2794841_xedk>1.0</td_2794841_xedk><td_rule_result_2794845>3.0</td_rule_result_2794845><td_rule_result_2794869>-999.0</td_rule_result_2794869><td_2794847_xedk>2.0</td_2794847_xedk><td_2794855_dsf_rate>-999.0</td_2794855_dsf_rate><td_2794849_yygr_rate>-999.0</td_2794849_yygr_rate><td_rule_result_2794849>6.0</td_rule_result_2794849></td_service_sd></standard></featurephone_test>";
        String xml4 = "<featurephone_test><standard><td_service_sd>Invoid</td_service_sd></standard></featurephone_test>";

        String xmlPa = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml1.xml");
        String xmlA = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml2.xml");


//        xmlPa = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml1.xml");
//        xmlA = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml2.xml");
//    	System.out.println(mergeXml(xml1,xml2));
//    	System.out.println(mergeXml(xml2,xml1));
//    	System.out.println(mergeXml(xml2,xml3));
//    	System.out.println(mergeXml(xml3,xml2));
//    	System.out.println(mergeXml(xml2,xml4));
        System.out.println(mergeXml(xmlPa, xmlA));
    }


    /***
     * 读取文件文本信息
     * @param fileName 文件名
     * @return 文件名
     */
    public static String read(String fileName) {
        File file = new File(fileName);
        BufferedReader reader = null;
        StringBuilder sbf = new StringBuilder();
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                sbf.append(tempStr);
            }
            reader.close();
            return sbf.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return sbf.toString();
    }


    /**
     * @Description:根据w3c规定xml合法字符保留合法字符,链接https://www.w3.org/TR/2004/REC-xml-20040204/#charsets
     * @Param: [in]
     * @return: java.lang.String
     * @Author: LingYin·Fan
     * @Date: 2020/1/2
     */
    public static String stripNonValidXMLCharacters(String in) {

        StringBuffer out = new StringBuffer(); // Used to hold the output.
        char current; // Used to reference the current character.

        if (StringUtils.isEmpty(in)) {
            return "";
        }
        for (int i = 0; i < in.length(); i++) {
            current = in.charAt(i);
            if ((current == 0x9) ||
                    (current == 0xA) ||
                    (current == 0xD) ||
                    ((current >= 0x20) && (current <= 0xD7FF)) ||
                    ((current >= 0xE000) && (current <= 0xFFFD)) ||
                    ((current >= 0x10000) && (current <= 0x10FFFF))) {
                out.append(current);
            }
        }
        return out.toString();
    }

    /**
     * xml转map 不带属性
     *
     * @param e
     * @return
     */
    private static Map xml2map(Element e) {
        Map map = new JSONObject();
        List list = e.elements();
        if (list.size() > 0) {
            for (Object o : list) {
                Element iter = (Element) o;
                List mapList = new ArrayList();

                if (iter.elements().size() > 0) {
                    Map m = XmlUtils.xml2map(iter);
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof List)) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        if (obj instanceof List) {
                            mapList = (List) obj;
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else {
                        map.put(iter.getName(), m);
                    }
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof List)) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        if (obj instanceof List) {
                            mapList = (List) obj;
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else {
                        map.put(iter.getName(), iter.getText());
                    }
                }
            }
        } else {
            map.put(e.getName(), e.getText());
        }
        return map;
    }

    public static Map xml2map(String xmlStr, boolean needRootKey)
            throws DocumentException {
        Document doc = DocumentHelper.parseText(xmlStr);
        Element root = doc.getRootElement();
        Map<String, Object> map = XmlUtils.xml2map(root);
        if (root.elements().size() == 0 && root.attributes().size() == 0) {
            return map;
        }
        if (needRootKey) {
            //在返回的map里加根节点键（如果需要）
            Map<String, Object> rootMap = new HashMap<>();
            rootMap.put(root.getName(), map);
            return rootMap;
        }
        return map;
    }

    /**
     *
     * @param xml
     * @return
     */
    public static String strategyXmlToJson(String xml) throws DocumentException {
        Map xml2map = XmlUtils.xml2map(xml, true);
        return JSON.toJSONString(xml2map);
    }
}
