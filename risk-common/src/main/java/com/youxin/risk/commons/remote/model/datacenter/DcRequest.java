package com.youxin.risk.commons.remote.model.datacenter;

import com.youxin.risk.commons.model.BaseEntity;
import com.youxin.risk.commons.remote.model.di.DiRequestService;

import java.util.List;
import java.util.Map;

public class DcRequest extends BaseEntity {

    private static final long serialVersionUID = 259365383650259345L;

    private String requestId;
    private String userKey;
    private String loanKey;
    private String reason;
    private List<DcRequestService> services;
    private Map<String, Object> params;

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getLoanKey() {
        return loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<DcRequestService> getServices() {
        return services;
    }

    public void setServices(List<DcRequestService> services) {
        this.services = services;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
}
