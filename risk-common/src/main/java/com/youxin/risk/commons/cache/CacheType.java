package com.youxin.risk.commons.cache;

/**
 * 公共缓存类型
 *
 * <AUTHOR>
 */

public enum CacheType {

    // 字典表配置信息
    dict_list_by_dict_code,
    di_service_list_by_service_code,
    di_service_class_list_by_service_code,
    di_task_type,
    alert_user,
    alert_user_by_group,
    alert_user_group,
    alert_policy,
    alert_policy_collect,
    alert_collect_conf,
    alert_metadata,
    admin_agency_by_icode,
    only_di_service,
    origin_variable,
    result_variable,
    event_info,
    process_node,
    process_dynamic_datanode,
    policy_library_conf_by_policy_code,
    library_conf_by_library_code,
    strategy_experiment,
    strategy_exp,
    strategy_split_flow,
    feature_experiment,
    datasource_experiment,
    process_experiment,
    feature_split_flow,
    process_node_candidate,
    process_split_flow,
    process_data_config,
    feature_data_depend,
    process_serial_realtion,
    event_node_step_relation,
    rule_score_experiment
}