package com.youxin.risk.commons.mongo;

import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.StrategyInvokeVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

/**
 * <AUTHOR>
 * @date 2019/2/22 10:04
 */
public class StrategyInvokeMongoDao extends BaseMongoDao {

    private static final Logger logger = LoggerFactory.getLogger(StrategyInvokeMongoDao.class);

//    @Autowired
//    @Qualifier("shardingMongoTemplate")
//    private MongoTemplate shardingTemplate;

    public StrategyInvokeMongoDao() {
        collectionName = "StrategyInvoke";
    }

    public StrategyInvokeVo getByLoanKeyAndStep(String loanKey, String step) {
        LoggerProxy.info(logger,"countAllQueryCollection=StrategyInvoke");
        long start = System.currentTimeMillis();
        Query query = new Query();
        query.addCriteria(Criteria.where("loanKey").is(loanKey));
        query.addCriteria(Criteria.where("step").is(step));
        query.with(new Sort(Sort.Direction.DESC, "createTime"));
//        StrategyInvokeVo invokeVo = null;
//        try {
//            invokeVo =  shardingTemplate.findOne(query, StrategyInvokeVo.class, collectionName);
//            logger.info("getByLoanKeyAndStep query time used:" + (System.currentTimeMillis() - start)+" from sharding mongo");
//        }catch (Exception ex){
//            logger.error("getByLoanKeyAndStep query time used:" + (System.currentTimeMillis() - start)+" from sharding mongo",ex);
//        }
//        if(invokeVo==null){
//            logger.info("getByLoanKeyAndStep query time used:" + (System.currentTimeMillis() - start)+" from mongo");
//            invokeVo = template.findOne(query, StrategyInvokeVo.class, collectionName);
//        }
//        StrategyInvokeVo invokeVo =  shardingTemplate.findOne(query, StrategyInvokeVo.class, collectionName);
//        logCount(invokeVo);
        logger.info("getByLoanKeyAndStep query time used:" + (System.currentTimeMillis() - start)+" from sharding mongo");
        return null;
    }
}