package com.youxin.risk.commons.cacheloader.service.impl;

import com.youxin.risk.commons.cacheloader.service.ProcessDefinitionService;
import com.youxin.risk.commons.dao.admin.ProcessDefinitionMapper;
import com.youxin.risk.commons.model.ProcessDefinition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/13 10:55
 */
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {
    @Resource
    private ProcessDefinitionMapper processDefinitionMapper;

    @Override
    public List<ProcessDefinition> selectAll() {
        return processDefinitionMapper.selectAll();
    }

    @Override
    public List<ProcessDefinition> selectByUpdateTime(Date updateTime) {
        return processDefinitionMapper.selectByUpdateTime(updateTime);
    }

    @Override
    public ProcessDefinition selectByProcessDefId(String processDefId) {
        return processDefinitionMapper.selectByProcessDefId(processDefId);
    }

}
