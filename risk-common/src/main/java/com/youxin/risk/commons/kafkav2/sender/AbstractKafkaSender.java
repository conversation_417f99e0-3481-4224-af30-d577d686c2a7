package com.youxin.risk.commons.kafkav2.sender;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.ProducerListener;

import java.util.HashMap;
import java.util.Map;

public abstract class AbstractKafkaSender implements InitializingBean {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private String topic;
    private String bootstrapServers;
    private boolean autoFlush;

    private Map<String, Object> defaultProducerProperties = new HashMap<>();
    private Map<String, Object> producerProperties;

    protected KafkaTemplate kafkaTemplate;

    private ProducerListener producerListener;

    private static final String CONFIG_KEY_RETRIES = "retries";
    private static final String CONFIG_KEY_REQUEST_TIMEOUT_MS = "request.timeout.ms";
    private static final String CONFIG_KEY_BATCH_SIZE = "batch.size";
    private static final String CONFIG_KEY_BUFFER_MEMORY = "buffer.memory";
    private static final String CONFIG_KEY_COMPRESSION_TYPE = "compression.type";
    private static final String CONFIG_KEY_KEY_SERIALIZER = "key.serializer";
    private static final String CONFIG_KEY_VALUE_SERIALIZER = "value.serializer";
    private static final String CONFIG_KEY_VALUE_BOOTSTRAP_SERVERS = "bootstrap.servers";
    private static final String CONFIG_KEY_MAX_REQUEST_SIZE = " max.request.size";

    protected static final int CONFIG_DEFAULT_RETRIES = 10;
    protected static final int CONFIG_DEFAULT_REQUEST_TIMEOUT_MS = 3000;
    protected static final int CONFIG_DEFAULT_BATCH_SIZE = 262144;
    protected static final int CONFIG_DEFAULT_BUFFER_MEMORY = 50 * 1024 * 1024;
    protected static final String CONFIG_DEFAULT_COMPRESSION_TYPE = "lz4";
    protected static final String CONFIG_DEFAULE_SERIALIZER = "org.apache.kafka.common.serialization.StringSerializer";

    @Override
    public void afterPropertiesSet() throws Exception {
        if (StringUtils.isBlank(topic) || StringUtils.isBlank(bootstrapServers)) {
            throw new IllegalArgumentException("'topic' and 'bootstrapServers' can't be null");
        }

        defaultProducerProperties.put(CONFIG_KEY_RETRIES, CONFIG_DEFAULT_RETRIES);
        defaultProducerProperties.put(CONFIG_KEY_REQUEST_TIMEOUT_MS, CONFIG_DEFAULT_REQUEST_TIMEOUT_MS);
        defaultProducerProperties.put(CONFIG_KEY_BATCH_SIZE, CONFIG_DEFAULT_BATCH_SIZE);
        defaultProducerProperties.put(CONFIG_KEY_BUFFER_MEMORY, CONFIG_DEFAULT_BUFFER_MEMORY);
        defaultProducerProperties.put(CONFIG_KEY_COMPRESSION_TYPE, CONFIG_DEFAULT_COMPRESSION_TYPE);
        defaultProducerProperties.put(CONFIG_KEY_KEY_SERIALIZER, CONFIG_DEFAULE_SERIALIZER);
        defaultProducerProperties.put(CONFIG_KEY_VALUE_SERIALIZER, CONFIG_DEFAULE_SERIALIZER);
        defaultProducerProperties.put(CONFIG_KEY_MAX_REQUEST_SIZE, 50 * 1024 * 1024);

        if (MapUtils.isNotEmpty(producerProperties)) {
            defaultProducerProperties.putAll(producerProperties);
        }
        defaultProducerProperties.put(CONFIG_KEY_VALUE_BOOTSTRAP_SERVERS, bootstrapServers);

        DefaultKafkaProducerFactory factory = new DefaultKafkaProducerFactory(defaultProducerProperties);
        kafkaTemplate = new KafkaTemplate(factory, autoFlush);
        kafkaTemplate.setDefaultTopic(topic);
        if (null != producerListener) {
            kafkaTemplate.setProducerListener(producerListener);
        }
    }

    public abstract void send(String data);

    public abstract void sendWithPartition(String data, String partitionKey);

    public KafkaTemplate getKafkaTemplate() {
        return kafkaTemplate;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public Map<String, Object> getProducerProperties() {
        return producerProperties;
    }

    public void setProducerProperties(Map<String, Object> producerProperties) {
        this.producerProperties = producerProperties;
    }

    public ProducerListener getProducerListener() {
        return producerListener;
    }

    public void setProducerListener(ProducerListener producerListener) {
        this.producerListener = producerListener;
    }

    public boolean isAutoFlush() {
        return autoFlush;
    }

    public void setAutoFlush(boolean autoFlush) {
        this.autoFlush = autoFlush;
    }
}