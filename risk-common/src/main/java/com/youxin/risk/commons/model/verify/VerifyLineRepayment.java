package com.youxin.risk.commons.model.verify;

import java.io.Serializable;
import java.util.Date;

public class VerifyLineRepayment implements Serializable{
    
	/**
	 * @Fields serialVersionUID: 
	 */
	private static final long serialVersionUID = -9181378900731387566L;

	private Integer id;


    private String userKey;


    private String sourceSystem;
	

    private String loanKey;


    private Integer loanId;


    private String transId;


	private String curOrderId;
	

    private Double repayAmount;


    private Date repayTime;
	
	/**
     * 0-开始, 1-等待，2-完成
     */

    private Integer isFinished;


    private String remark;


    private Date createTime;


    private Date updateTime;


    private Integer version;

    public Integer getIsFinished() {
		return isFinished;
	}

	public void setIsFinished(Integer isFinished) {
		this.isFinished = isFinished;
	}

	public String getLoanKey() {
		return loanKey;
	}

	public void setLoanKey(String loanKey) {
		this.loanKey = loanKey;
	}

	public Integer getLoanId() {
		return loanId;
	}

	public void setLoanId(Integer loanId) {
		this.loanId = loanId;
	}

	public String getTransId() {
		return transId;
	}

	public void setTransId(String transId) {
		this.transId = transId;
	}

	public String getCurOrderId() {
		return curOrderId;
	}

	public void setCurOrderId(String curOrderId) {
		this.curOrderId = curOrderId;
	}

	public Double getRepayAmount() {
		return repayAmount;
	}

	public void setRepayAmount(Double repayAmount) {
		this.repayAmount = repayAmount;
	}

	public Date getRepayTime() {
		return repayTime;
	}

	public void setRepayTime(Date repayTime) {
		this.repayTime = repayTime;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}