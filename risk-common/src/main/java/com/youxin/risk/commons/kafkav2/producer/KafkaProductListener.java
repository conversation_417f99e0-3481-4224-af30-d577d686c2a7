package com.youxin.risk.commons.kafkav2.producer;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.support.ProducerListener;

/**
 * <AUTHOR>
 * @date 2018/9/25 15:50
 */
@SuppressWarnings("rawtypes")
public class KafkaProductListener implements ProducerListener<String, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaProductListener.class);

    @Override
    public void onSuccess(String topic, Integer partition, String key, String value, RecordMetadata recordMetadata) {
        LoggerProxy.info("producerOnSuccess", LOGGER, "topic={}, key={}, message={}", topic, key, value);
    }

    @Override
    public void onError(String topic, Integer partition, String key, String value, Exception exception) {
        LoggerProxy.error("sendMessageError", LOGGER, "topic={}, key={}, message={}", topic, key, value, exception);
    }

    @Override
    public boolean isInterestedInSuccess() {
        return true;
    }
}
