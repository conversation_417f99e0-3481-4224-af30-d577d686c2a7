package com.youxin.risk.commons.cacheloader;

import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.AlertCollectConfService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.model.AlertCollectCondition;
import com.youxin.risk.commons.model.AlertCollectConf;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.collections.MapUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 采集配置加载
 *
 * <AUTHOR>
 */
public class AlertCollectConfCacheLoader extends BaseCacheLoader {

    @Resource
    private AlertCollectConfService alertCollectConfService;

    @Override
    @Scheduled(fixedDelay = ALERT_ONE_MINS_FIXED_DELAY_MILLIS)
    public void load() {
        super.load(ConfigTableEnum.alert_admin_collect_conf.toString());
    }

    @Override
    protected void loadAll() {
        Map<String, Object> all = alertCollectConfService.selectAll();
        if (MapUtils.isEmpty(all)) {
            return;
        }
        Map<String, List<AlertCollectCondition>> allCondi = alertCollectConfService.selectAllCondis();
        loadCondition(all, allCondi);
        CacheManager.setCache(CacheType.alert_collect_conf, all);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected int loadPart() {

        Map<String, Object> updateMap = alertCollectConfService.selectByUpdateTime(getCacheTime());
        if (CollectionUtils.isEmpty(updateMap)) {
            return 0;
        }

        List<String> names = new ArrayList<>();
        for (Map.Entry<String, Object> entry : updateMap.entrySet()) {
            names.add(entry.getKey());
        }

        Map<String, List<AlertCollectCondition>> cs = alertCollectConfService.selectCollectCondisByCollectCodes(names);
        loadCondition(updateMap, cs);

        Map<String, Object> cacheMap = CacheManager.getCache(CacheType.alert_collect_conf);
        cacheMap.putAll(updateMap);

        CacheManager.setCache(CacheType.alert_collect_conf, cacheMap);

        return updateMap.size();
    }

    private void loadCondition(Map<String, Object> all, Map<String, List<AlertCollectCondition>> cs) {
        if (MapUtils.isEmpty(all) || MapUtils.isEmpty(cs)) {
            return;
        }
        for (Map.Entry<String, List<AlertCollectCondition>> e : cs.entrySet()) {
            String cacheKey = e.getKey();
            if (!all.containsKey(cacheKey)) {
                LoggerProxy.warn("notFoundAlertCollectConf", logger, "code=" + cacheKey);
                continue;
            }
            AlertCollectConf p = (AlertCollectConf) all.get(cacheKey);
            p.setConditions(e.getValue());
        }
    }
}