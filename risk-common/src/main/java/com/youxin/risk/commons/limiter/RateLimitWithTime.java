package com.youxin.risk.commons.limiter;

import com.youxin.risk.commons.utils.LoggerProxy;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;

/**
 * 根据指定时间范围限流
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class RateLimitWithTime implements RateLimit {
    private static final Logger LOGGER = LoggerFactory.getLogger(RateLimitWithTime.class);

    private Double rate;
    private LocalTime startTime;
    private LocalTime endTime;

    @Override
    public RateLimitWithTime getMatchedRateLimit() {
        if (startTime.isAfter(endTime)) {
            LoggerProxy.error("startTimeAfterEndTime", LOGGER, "startTime={},endTime={}",startTime,endTime);
        }
        LocalTime now = LocalTime.now();
        return startTime.isBefore(now) && endTime.isAfter(now) ? this : null;
    }

    @Override
    public boolean isExpired() {
        return endTime.isBefore(LocalTime.now());
    }
}
