package com.youxin.risk.commons.service.datacenter;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.model.DiService;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.SubscriptionDataRequestVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.youxin.risk.commons.constants.DiConstant.*;
import static com.youxin.risk.commons.utils.DateUtil.TIME_MILLIS;
import static com.youxin.risk.commons.utils.DateUtil.parsePlus;

/**
 * @description: 数据订阅服务类
 * @author: juxiang
 * @create: 2022-04-13 17:39
 **/
@Service
public class SubscriptionDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubscriptionDataService.class);

    @Value(value = "${riskDatacenter.subscriptionData.url:none}")
    private String dcQueryUrl;
    @Value(value = "${dataPlatform.subscriptionData.url:none}")
    private String dpQueryUrl;
    @Value(value = "${label.subscriptionData.url:http://antifraud-risk-label-api.weicai.com.cn/tag/api/subscribe}")
    private String labelQueryUrl;
    @Value(value = "${variable.subscriptionData.url:http://risk-variable-gateway.weicai.com.cn/api/variable/api/subscribe}")
    private String variableQueryUrl;

    private static final String DATA_CENTER = "dc";
    private static final String LAPEL = "label";
    private static final String DP = "dp";
    private static final String VAR = "var";
    private final String VARIABLE_SERVICE_CODE = "variableServiceCode";



    public void subscriptionData(ServiceRequest serviceRequest, DiService diService) {
        if (isSkipSubscribe(serviceRequest)) {
            LoggerProxy.info("subscriptionData", LOGGER, "skip subscribe!");
            return;
        }
        SubscriptionDataRequestVo subscriptionDataRequestVo = new SubscriptionDataRequestVo();
        subscriptionDataRequestVo.setDataType(diService.getDataType());
        subscriptionDataRequestVo.setUserKey(serviceRequest.getUserKey());
        subscriptionDataRequestVo.setRequestId(serviceRequest.getRequestId());
        subscriptionDataRequestVo.setWaitType((String) serviceRequest.getParams().get(WAIT_TYPE));
        if (Objects.nonNull(serviceRequest.getParams().get(UPLOAD_WAIT_TIME))) {
            subscriptionDataRequestVo.setWaitTime(Long.parseLong(serviceRequest.getParams().get(UPLOAD_WAIT_TIME).toString()));
        }
        try {
            subscriptionDataRequestVo.setUploadTime(parsePlus(String.valueOf(
                    serviceRequest.getParams().get(UPLOAD_TIME)), TIME_MILLIS));
            String dataSource = diService.getDataSource();
            String queryUrl = dcQueryUrl;
            if (StringUtils.equals(dataSource, DP)) {
                queryUrl = dpQueryUrl;
            } else if (StringUtils.equals(dataSource, LAPEL)) {
                queryUrl = labelQueryUrl;
            } else if (StringUtils.equals(dataSource, VAR)) {
                subscriptionDataRequestVo.setServiceCode(serviceRequest.getServiceCode());
                subscriptionDataRequestVo.setLoanKey(serviceRequest.getLoanKey());
                if (serviceRequest.getParams().get(VARIABLE_SERVICE_CODE) != null) {
                    subscriptionDataRequestVo.setServiceCode((String) serviceRequest.getParams().get(VARIABLE_SERVICE_CODE));
                }
                String systemEventCode = serviceRequest.getSystemEventCode();
                if (!StringUtils.isBlank(systemEventCode)){
                    LoggerProxy.info("subscriptionData_variableServiceCode", LOGGER, "systemEventCode = {}", systemEventCode);
                }
                queryUrl = variableQueryUrl;
            }
            String requestJson = JSONObject.toJSONString(subscriptionDataRequestVo);

//            LoggerProxy.info("beforeSubscriptionData", LOGGER, "requestId:{},dataSource:{},param:{},url:{}", serviceRequest.getRequestId(), dataSource, requestJson, queryUrl);
            String result = SyncHTTPRemoteAPI.postJson(queryUrl, requestJson, diService.getServiceTimeout().intValue());
            LoggerProxy.info("afterSubscriptionData", LOGGER, "requestId:{},dataSource:{},param:{},url:{} reponse:{}",
                    serviceRequest.getRequestId(),dataSource,requestJson,queryUrl, result);
        } catch (Exception e) {
            LoggerProxy.error("subscriptionDataError", LOGGER, String.format("param:%s,error:", JSONObject.toJSONString(subscriptionDataRequestVo)), e);
        }
    }

    public boolean isSkipSubscribe(ServiceRequest serviceRequest) {
        try {
            Object isSkipSubscribe = serviceRequest.getParams().get("isSkipSubscribe");
            if (Objects.isNull(isSkipSubscribe)) {
                return false;
            } else {
                return (boolean) isSkipSubscribe;
            }
        } catch (Exception e) {
            LoggerProxy.info("subscriptionDataError", LOGGER, "judge skip subscribe error:", e);
        } finally {
            serviceRequest.getParams().remove("isSkipSubscribe");
        }
        return false;
    }

    public void subscriptionDataBatch(Map<String, List<SubscriptionDataRequestVo>> subscriptionDataRequestMap) {
        try {
            for (Map.Entry<String, List<SubscriptionDataRequestVo>> entry : subscriptionDataRequestMap.entrySet()) {
                List<SubscriptionDataRequestVo> value = entry.getValue();
                String key = entry.getKey();
                if (CollectionUtils.isEmpty(value)) {
                    LoggerProxy.info("subscriptionDataBatch", LOGGER, "system:{} request empty", entry.getKey());
                    continue;
                }
                String requestJson = JSONObject.toJSONString(value);
                String queryUrl = (StringUtils.equals(key, DATA_CENTER) ? dcQueryUrl : dpQueryUrl) + "/batch";
                LoggerProxy.info("beforeSubscriptionDataBatch", LOGGER, "param:{},url:{}", requestJson, queryUrl);
                String result = SyncHTTPRemoteAPI.postJson(queryUrl, requestJson, 6000);
                LoggerProxy.info("afterSubscriptionData", LOGGER, "reponse:{}", result);
            }
        } catch (Exception e) {
            LoggerProxy.error("subscriptionDataError", LOGGER, "error:", e);
        }
    }
}
