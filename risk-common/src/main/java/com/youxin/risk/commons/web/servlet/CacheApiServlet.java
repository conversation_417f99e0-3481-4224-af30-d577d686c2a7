package com.youxin.risk.commons.web.servlet;

import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.utils.HttpResponseUtil;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.JsonFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;


public class CacheApiServlet extends HttpServlet {

    private Logger logger = LoggerFactory.getLogger(CacheApiServlet.class);


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            String cacheType = request.getParameter("cacheType");
            String key = request.getParameter("key");
            Map<String, Object> caches = CacheManager.getCache(CacheType.valueOf(cacheType));
            String json = "";
            if (StringUtils.isNotEmpty(key)) {
                json = JacksonUtil.toJson(caches.get(key));
            } else {
                json = JacksonUtil.toJson(caches);
            }

            String resstr = json;
            try {
                resstr = JsonFormatUtil.format(json);
            } catch (Exception e) {
                // nothing
            }
            HttpResponseUtil.renderText(response, resstr);
        } catch (Exception e) {
            LoggerProxy.warn("CacheApiServletException", logger, "", e);
            HttpResponseUtil.renderText(response, e.getMessage());
        }
    }

}