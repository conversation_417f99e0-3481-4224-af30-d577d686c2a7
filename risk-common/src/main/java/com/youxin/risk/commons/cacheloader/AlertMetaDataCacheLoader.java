package com.youxin.risk.commons.cacheloader;

import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.AlertMetaDataService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.model.AlertMetaData;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字典配置缓存加载
 *
 * <AUTHOR>
 */
public class AlertMetaDataCacheLoader extends BaseCacheLoader {

    @Resource
    private AlertMetaDataService service;

    @Scheduled(fixedDelay = ALERT_FIXED_DELAY_MILLIS)
    public void load() {
        super.load(ConfigTableEnum.alert_admin_metadata.toString());
    }

    /**
     * 字典表缓存结构：Map<meta_type,Map<meta_name,AlertMetaData>>
     */
    @Override
    protected void loadAll() {
        Map<String, Object> all = service.selectAll();
        CacheManager.setCache(CacheType.alert_metadata, all);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected int loadPart() {
        List<AlertMetaData> updateList = service.selectByUpdateTime(getCacheTime());
        Integer updateNum = 0;
        if (!CollectionUtils.isEmpty(updateList)) {
            updateNum = updateList.size();
            Map<String, Object> cacheMap = CacheManager.getCache(CacheType.alert_metadata);
            for (AlertMetaData entry : updateList) {
                if (cacheMap.containsKey(entry.getMeteType())) {
                    Map<String, AlertMetaData> valueMap = (Map<String, AlertMetaData>) cacheMap.get(entry.getMeteType());
                    valueMap.put(entry.getMetaName(), entry);
                } else {
                    Map<String, AlertMetaData> valueMap = new ConcurrentHashMap<>();
                    valueMap.put(entry.getMetaName(), entry);
                    cacheMap.put(entry.getMeteType(), valueMap);
                }
            }
        }
        return updateNum;
    }
}
