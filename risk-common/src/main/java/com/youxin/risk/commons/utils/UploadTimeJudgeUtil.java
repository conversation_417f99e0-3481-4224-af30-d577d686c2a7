package com.youxin.risk.commons.utils;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.youxin.risk.commons.constants.DiConstant.NEED_LATEST_UPLOAD_DATA;
import static com.youxin.risk.commons.constants.DiConstant.UPLOAD_TIME;
import static com.youxin.risk.commons.utils.DateUtil.TIME_MILLIS;
import static com.youxin.risk.commons.utils.DateUtil.parsePlus;

/**
 * @description: 采集时间判断公共工具类
 * @author: juxiang
 * @create: 2022-06-13 20:19
 **/
public class UploadTimeJudgeUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(UploadTimeJudgeUtil.class);
    public static final String LATEST_DATA_CREATE_TIME = "latestDataCreateTime";
    public static final String HFQ_UPLOAD_TIMESTAMP = "httpTimestamp";

    public static boolean SmsAndPhoneCallUploadTimeJudge(ServiceRequest serviceRequest, ServiceResponse response) {
        if (!isNeedLatestUploadData(serviceRequest)) {
            return true;
        }
        try {
            Date createTime = JSONObject.parseObject(response.getData()).getDate("createTime");
            Date uploadTime = parsePlus(String.valueOf(serviceRequest.getParams().get(UPLOAD_TIME)), TIME_MILLIS);
            return uploadTime.compareTo(createTime) != 1;
        } catch (Exception e) {
            LoggerProxy.info("SmsAndPhoneCallUploadTimeJudge#isInUploadTime", LOGGER, "error:",e);
        }
        return true;
    }

    public static boolean plistUploadTimeJudge(ServiceRequest serviceRequest, ServiceResponse response){
        try {
            if(!isNeedLatestUploadData(serviceRequest)){
                return true;
            }

            List<DcSubmitPlist> dcSubmitPlists = JSONObject.parseArray(response.getData(), DcSubmitPlist.class);
            if(CollectionUtils.isEmpty(dcSubmitPlists)){
                return false;
            }
            Date theLatestDate=dcSubmitPlists.get(0).getCreateTime();
            Date uploadTime=parsePlus(String.valueOf(serviceRequest.getParams().get(UPLOAD_TIME)),TIME_MILLIS);
            return uploadTime.compareTo(theLatestDate)!=1;
        }catch (Exception e){
            LoggerProxy.error("plistUploadTimeJudge#isInAcquisitionTime", LOGGER, "error {}", e);
        }
        return false;
    }

    public static boolean allPlistServiceUploadTimeJudge(ServiceRequest serviceRequest, ServiceResponse response){
        try {
            if(!isNeedLatestUploadData(serviceRequest)){
                return true;
            }
            Date uploadTime=parsePlus(String.valueOf(serviceRequest.getParams().get(UPLOAD_TIME)),TIME_MILLIS);
            Date latestUploadTime =(Date) response.getResult().get(LATEST_DATA_CREATE_TIME);
            if(Objects.isNull(latestUploadTime)){
                return false;
            }
            return uploadTime.compareTo(latestUploadTime)!=1;
        }catch (Exception e){
            LoggerProxy.error("plistUploadTimeJudge#isInAcquisitionTime", LOGGER, "error {}", e);
        }
        return false;
    }

    public static boolean isNeedLatestUploadData(ServiceRequest request) {
        try {
            return (boolean) Optional.ofNullable(request.getParams().get(NEED_LATEST_UPLOAD_DATA)).orElse(false);
        }catch (Exception e){
            LoggerProxy.error("isSaveSyncService",LOGGER,"get acquisitionTime error:",e);
        }
        return false;
    }

}
