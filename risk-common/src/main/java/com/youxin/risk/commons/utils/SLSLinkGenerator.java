package com.youxin.risk.commons.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.sls20201230.Client;
import com.aliyun.sls20201230.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SLS (阿里云日志服务) 免密链接生成工具类。
 * 该类用于根据指定的事件代码从Nacos配置生成可分享的免密链接。
 */
public class SLSLinkGenerator {
    private static final Logger logger = LoggerFactory.getLogger(SLSLinkGenerator.class);

    private static final String ENDPOINT = "cn-beijing.log.aliyuncs.com"; // SLS Endpoint (地域节点)
    private static final String TICKET_ENDPOINT = "cn-shanghai.log.aliyuncs.com"; // Ticket Endpoint (地域节点)
    private static final String PROJECT_NAME = "risk-service-logs"; // SLS 项目名称
    private static final String REGION = "cn-beijing"; // 项目所在地域
    private static final String CONSOLE_URL = "https://sls.console.aliyun.com/lognext/project/"; // 控制台 URL 前缀
    private static final String DASHBOARD_URL = "/dashboard/"; // 仪表盘 URL 路径
    private static final String BASE_SHARE_PARAMS = "?slsRegion=" + REGION;
    private static final String ALIBABA_CLOUD_ACCESS_KEY_ID = "LTAI5tRWttyLAksrQjbmgX4e";
    private static final String ALIBABA_CLOUD_ACCESS_KEY_SECRET = "******************************";

    private static final Map<String, String> DEFAULT_SHARE_PARAMS = new HashMap<String, String>() {{
        put("isShare", "true"); // 隐藏左侧导航栏和其他Tab页
        put("hideTopbar", "true"); // 隐藏顶部导航栏
        put("hideSidebar", "true"); // 隐藏左侧导航栏
        put("ignoreTabLocalStorage", "true"); // 关闭Tab访问的历史记录。
        put("queryTimeType", "4"); // 指定查询和分析的时间范围。4小时（相对）
        put("hiddenChangeProject", "true"); // 隐藏切换Project功能。
        put("disablePublicShare", "true"); // 在仪表盘中，隐藏创建免密分享按钮。
        put("hiddenOverview", "true"); // 隐藏Project概览入口。
        put("hiddenBack", "true"); // 隐藏当前Project控制台的返回按钮。
        put("readOnly", "true"); // 隐藏编辑、修改按钮，例如分享、查询分析属性，另存为快速查询、另存为告警等。
    }};


    /**
     * 根据事件代码获取免密分享链接。
     *
     * @param eventCode 事件代码
     * @return 免密分享链接，如果生成失败则返回 null
     */
    public static String getShareableLinkByEventCode(String eventCode) {
        // 直接从统一配置中获取监控配置
        Map<String, Object> monitorConfig = getEventCodeMonitorConfigFromUnified(eventCode);

        if (monitorConfig == null) {
            logger.warn("未找到事件代码 '{}' 的监控配置。", eventCode);
            return null;
        }

        String dashboardName = (String) monitorConfig.get("dashboardName");
        List<Map<String, String>> token = (List<Map<String, String>>) monitorConfig.get("token");
        List<Map<String, String>> extensions = (List<Map<String, String>>) monitorConfig.get("extensions");

        return getShareableLinkByConfig(dashboardName, token, extensions);
    }

    /**
     * 根据配置参数获取免密分享链接
     *
     * @param dashboardName 仪表盘名称
     * @param token Token 参数列表
     * @param extensions 扩展配置参数列表
     * @return 免密分享链接，如果生成失败则返回 null
     */
    public static String getShareableLinkByConfig(String dashboardName,
                                                 List<Map<String, String>> token,
                                                 List<Map<String, String>> extensions) {
        try {
            Client client = createClient(TICKET_ENDPOINT); // 创建 SLS 客户端
            if (client == null) {
                logger.error("SLS Client 创建失败, 无法生成分享链接.");
                return null;
            }

            if (dashboardName == null) {
                logger.warn("dashboardName为空");
                return null;
            }

            String ticket = createTicket(client); // 创建 Ticket
            if (ticket == null) {
                logger.warn("创建 Ticket 失败。");
                return null;
            }

            String shareableLink = generateShareableLink(dashboardName, ticket, token, extensions); // 生成可分享链接
            if (shareableLink == null) {
                logger.warn("生成分享链接失败.");
                return null;
            }
            logger.info("成功为dashboardName：{} 生成shareableLink: {}", dashboardName, shareableLink);
            return shareableLink;

        } catch (Exception e) {
            logger.error("发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据策略类型和监控ID获取免密分享链接
     *
     * @param strategyType 策略类型
     * @param monitorId 监控ID
     * @return 免密分享链接，如果生成失败则返回 null
     */
    public static String getShareableLinkByStrategyAndMonitor(String strategyType, String monitorId) {
        // 从统一配置中获取监控配置
        Map<String, Object> monitorConfig = getMonitorConfigByStrategyAndId(strategyType, monitorId);

        if (monitorConfig == null) {
            logger.warn("未找到策略类型 '{}' 下监控ID '{}' 的配置。", strategyType, monitorId);
            return null;
        }

        String dashboardName = (String) monitorConfig.get("dashboardName");
        List<Map<String, String>> token = (List<Map<String, String>>) monitorConfig.get("token");
        List<Map<String, String>> extensions = (List<Map<String, String>>) monitorConfig.get("extensions");

        return getShareableLinkByConfig(dashboardName, token, extensions);
    }



    /**
     * 在统一配置中判断事件代码是否存在
     *
     * @param eventCode 事件代码
     * @return 是否存在配置
     */
    public static boolean isEventCodeConfiguredInUnifiedConfig(String eventCode) {
        // 使用正确的统一配置文件名
        String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-unified-config", "");

        if (configString == null || configString.isEmpty()) {
            logger.warn("Nacos 统一配置 strategy-monitor-unified-config 为空。");
            return false;
        }

        try {
            logger.debug("检查事件代码是否在统一配置中存在, eventCode={}", eventCode);

            JSONObject config = JSON.parseObject(configString);

            // 遍历所有策略类型
            for (String strategyType : config.keySet()) {
                JSONObject strategyConfig = config.getJSONObject(strategyType);
                if (strategyConfig != null && strategyConfig.containsKey("monitors")) {
                    com.alibaba.fastjson.JSONArray monitors = strategyConfig.getJSONArray("monitors");
                    if (monitors != null) {
                        // 遍历策略下的所有监控项
                        for (int i = 0; i < monitors.size(); i++) {
                            JSONObject monitor = monitors.getJSONObject(i);
                            String currentEventCode = monitor.getString("eventCode");

                            if (eventCode.equals(currentEventCode)) {
                                logger.debug("在统一配置中找到事件代码, eventCode={}, strategyType={}, monitorId={}",
                                        eventCode, strategyType, monitor.getString("id"));
                                return true;
                            }
                        }
                    }
                }
            }

            logger.debug("在统一配置中未找到事件代码, eventCode={}", eventCode);
            return false;

        } catch (Exception e) {
            logger.error("解析统一配置 strategy-monitor-unified-config 失败, eventCode={}, error={}", eventCode, e.getMessage(), e);
            return false;
        }
    }


    /**
     * 从统一配置中根据策略类型和监控ID获取监控配置
     *
     * @param strategyType 策略类型
     * @param monitorId 监控ID
     * @return 监控配置 Map，包含 dashboardName, token 和 extensions
     */
    private static Map<String, Object> getMonitorConfigByStrategyAndId(String strategyType, String monitorId) {
        // 使用正确的统一配置文件名
        String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-unified-config", "");

        if (configString == null || configString.isEmpty()) {
            logger.warn("Nacos 统一配置 strategy-monitor-unified-config 为空。");
            return null;
        }

        try {
            logger.debug("开始解析统一配置, strategyType={}, monitorId={}", strategyType, monitorId);

            JSONObject config = JSON.parseObject(configString);
            JSONObject strategyConfig = config.getJSONObject(strategyType);

            if (strategyConfig == null) {
                logger.warn("策略类型 '{}' 在统一配置中没有对应的配置。", strategyType);
                return null;
            }

            com.alibaba.fastjson.JSONArray monitors = strategyConfig.getJSONArray("monitors");
            if (monitors == null || monitors.size() == 0) {
                logger.warn("策略类型 '{}' 在统一配置中没有监控项配置。", strategyType);
                return null;
            }

            // 遍历监控项查找匹配的监控ID
            for (int i = 0; i < monitors.size(); i++) {
                JSONObject monitor = monitors.getJSONObject(i);
                String currentMonitorId = monitor.getString("id");

                if (monitorId.equals(currentMonitorId)) {
                    logger.debug("找到匹配的监控项, strategyType={}, monitorId={}", strategyType, monitorId);

                    JSONObject slsConfig = monitor.getJSONObject("slsConfig");
                    if (slsConfig == null) {
                        logger.warn("监控项 '{}' 在策略 '{}' 中没有SLS配置。", monitorId, strategyType);
                        return null;
                    }

                    // 构建返回的监控配置
                    Map<String, Object> monitorConfig = new HashMap<>();

                    // 获取 dashboardName
                    String dashboardName = slsConfig.getString("dashboardName");
                    if (dashboardName == null || dashboardName.trim().isEmpty()) {
                        logger.error("监控项 '{}' 的 dashboardName 为空, strategyType={}", monitorId, strategyType);
                        return null;
                    }
                    monitorConfig.put("dashboardName", dashboardName);

                    // 解析 token 配置
                    List<Map<String, String>> tokenList = new ArrayList<>();
                    com.alibaba.fastjson.JSONArray tokenArray = slsConfig.getJSONArray("token");
                    if (tokenArray != null) {
                        for (int j = 0; j < tokenArray.size(); j++) {
                            JSONObject tokenObj = tokenArray.getJSONObject(j);
                            if (tokenObj != null) {
                                Map<String, String> tokenMap = new HashMap<>();
                                String key = tokenObj.getString("key");
                                String value = tokenObj.getString("value");
                                if (key != null && value != null) {
                                    tokenMap.put("key", key);
                                    tokenMap.put("value", value);
                                    tokenList.add(tokenMap);
                                } else {
                                    logger.warn("Token配置中存在空的key或value, strategyType={}, monitorId={}", strategyType, monitorId);
                                }
                            }
                        }
                    }
                    monitorConfig.put("token", tokenList);

                    // 解析 extensions 配置
                    List<Map<String, String>> extensionsList = new ArrayList<>();
                    com.alibaba.fastjson.JSONArray extensionsArray = slsConfig.getJSONArray("extensions");
                    if (extensionsArray != null) {
                        for (int j = 0; j < extensionsArray.size(); j++) {
                            JSONObject extensionObj = extensionsArray.getJSONObject(j);
                            if (extensionObj != null) {
                                Map<String, String> extensionMap = new HashMap<>();
                                for (String key : extensionObj.keySet()) {
                                    String value = extensionObj.getString(key);
                                    if (key != null && value != null) {
                                        extensionMap.put(key, value);
                                    }
                                }
                                if (!extensionMap.isEmpty()) {
                                    extensionsList.add(extensionMap);
                                }
                            }
                        }
                    }
                    monitorConfig.put("extensions", extensionsList);

                    logger.info("成功从统一配置获取监控配置, strategyType={}, monitorId={}, dashboardName={}, tokenCount={}, extensionCount={}",
                            strategyType, monitorId, dashboardName, tokenList.size(), extensionsList.size());

                    return monitorConfig;
                }
            }

            logger.warn("在策略类型 '{}' 的统一配置中未找到监控ID '{}'。", strategyType, monitorId);
            return null;

        } catch (Exception e) {
            logger.error("解析统一配置 strategy-monitor-unified-config 失败, strategyType={}, monitorId={}, error={}",
                    strategyType, monitorId, e.getMessage(), e);
            return null;
        }
    }



    /**
     * 从统一配置中根据事件代码获取监控配置
     *
     * @param eventCode 事件代码
     * @return 监控配置
     */
    private static Map<String, Object> getEventCodeMonitorConfigFromUnified(String eventCode) {
        // 使用正确的统一配置文件名
        String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "strategy-monitor-unified-config", "");

        if (configString == null || configString.isEmpty()) {
            logger.warn("Nacos 统一配置 strategy-monitor-unified-config 为空。");
            return null;
        }

        try {
            logger.debug("开始从统一配置中查找事件代码, eventCode={}", eventCode);

            JSONObject config = JSON.parseObject(configString);

            // 遍历所有策略类型
            for (String strategyType : config.keySet()) {
                JSONObject strategyConfig = config.getJSONObject(strategyType);
                if (strategyConfig != null && strategyConfig.containsKey("monitors")) {
                    com.alibaba.fastjson.JSONArray monitors = strategyConfig.getJSONArray("monitors");
                    if (monitors != null) {
                        // 遍历策略下的所有监控项
                        for (int i = 0; i < monitors.size(); i++) {
                            JSONObject monitor = monitors.getJSONObject(i);
                            String currentEventCode = monitor.getString("eventCode");

                            if (eventCode.equals(currentEventCode)) {
                                logger.debug("在统一配置中找到匹配的事件代码, eventCode={}, strategyType={}, monitorId={}",
                                        eventCode, strategyType, monitor.getString("id"));

                                JSONObject slsConfig = monitor.getJSONObject("slsConfig");
                                if (slsConfig != null) {
                                    // 使用策略类型和监控ID获取完整配置
                                    String monitorId = monitor.getString("id");
                                    Map<String, Object> monitorConfig = getMonitorConfigByStrategyAndId(strategyType, monitorId);

                                    if (monitorConfig != null) {
                                        logger.info("成功从统一配置获取事件代码对应的监控配置, eventCode={}, strategyType={}, monitorId={}",
                                                eventCode, strategyType, monitorId);
                                        return monitorConfig;
                                    } else {
                                        logger.warn("无法获取事件代码对应的监控配置, eventCode={}, strategyType={}, monitorId={}",
                                                eventCode, strategyType, monitorId);
                                    }
                                } else {
                                    logger.warn("事件代码 '{}' 对应的监控项没有SLS配置, strategyType={}, monitorId={}",
                                            eventCode, strategyType, monitor.getString("id"));
                                }
                            }
                        }
                    }
                }
            }

            logger.info("在统一配置中未找到事件代码 '{}' 对应的监控配置。", eventCode);
            return null;

        } catch (Exception e) {
            logger.error("从统一配置解析事件代码监控配置失败, eventCode={}, error={}", eventCode, e.getMessage(), e);
            return null;
        }
    }


    /**
     * 使用 AccessKey 初始化 SLS 客户端。 强烈建议在生产环境中使用 STS 或其他凭证提供方式，以提高安全性。
     *
     * @return SLS 客户端实例
     */
    private static Client createClient(String endpoint) {
        Config credentialConfig = new Config()
                .setType("access_key")
                .setAccessKeyId(ALIBABA_CLOUD_ACCESS_KEY_ID)
                .setAccessKeySecret(ALIBABA_CLOUD_ACCESS_KEY_SECRET)
                .setEndpoint(endpoint);
        try {
            return new Client(credentialConfig);
        } catch (Exception e) {
            logger.error("创建 SLS 客户端失败: {}", e.getMessage(), e);
            return null;
        }
    }


    /**
     * 根据仪表盘显示名称获取仪表盘名称 (Dashboard Name)。
     *
     * @param client      SLS 客户端实例
     * @param displayName 仪表盘显示名称
     * @return 仪表盘名称，如果找不到则返回 null
     */
    private static String getDashboardNameByDisplayName(Client client, String displayName) {
        ListDashboardRequest listDashboardRequest = new ListDashboardRequest(); // 创建 ListDashboard 请求
        RuntimeOptions runtime = new RuntimeOptions(); // 创建运行参数
        Map<String, String> headers = new HashMap<>(); // 创建请求头

        try {
            ListDashboardResponse response = client.listDashboardWithOptions(PROJECT_NAME, listDashboardRequest, headers, runtime); // 调用 API 获取仪表盘列表

            if (response != null && response.getBody() != null && response.getBody().dashboardItems != null) {
                List<ListDashboardResponseBody.ListDashboardResponseBodyDashboardItems> dashboardItems = response.getBody().dashboardItems; // 获取仪表盘条目列表
                for (ListDashboardResponseBody.ListDashboardResponseBodyDashboardItems item : dashboardItems) { // 遍历仪表盘条目
                    String dashboardName = item.dashboardName; // 获取仪表盘名称
                    String currentDisplayName = item.displayName; // 获取仪表盘显示名称
                    if (displayName.equals(currentDisplayName)) { // 如果显示名称匹配
                        return dashboardName; // 返回仪表盘名称
                    }
                }
            }
            logger.info("未找到显示名称为 '{}' 的仪表盘。", displayName); // 如果未找到，输出提示信息
            return null;

        } catch (TeaException error) { // 捕获 SLS API 异常
            logger.error("获取仪表盘信息失败: {}", error.getMessage(), error);
            return null;
        } catch (Exception error) { // 捕获其他异常
            logger.error("获取仪表盘信息失败: {}", error.getMessage(), error);
            return null;
        }
    }


    /**
     * 创建用于免密访问的 Ticket (授权票据)。
     *
     * @param client SLS 客户端实例
     * @return 生成的 Ticket，如果创建失败则返回 null
     */
    private static String createTicket(Client client) {
        CreateTicketRequest createTicketRequest = new CreateTicketRequest(); // 创建 CreateTicket 请求
        RuntimeOptions runtime = new RuntimeOptions(); // 创建运行参数
        Map<String, String> headers = new HashMap<>(); // 创建请求头
        try {
            CreateTicketResponse response = client.createTicketWithOptions(createTicketRequest, headers, runtime); // 调用 API 创建 Ticket
            if (response != null) {
                return response.getBody().ticket;
            }
            return null;
        } catch (TeaException error) { // 捕获 SLS API 异常
            logger.error("创建 Ticket 失败: {}", error.getMessage(), error);
            return null;
        } catch (Exception error) { // 捕获其他异常
            logger.error("创建 Ticket 失败: {}", error.getMessage(), error);
            return null;
        }
    }

    /**
     * 生成可分享的仪表盘链接，包含 Ticket 和可选的 Token 参数。
     *
     * @param dashboardName 仪表盘名称
     * @param ticket        Ticket (授权票据)
     * @param token         可选的 Token 参数，用于传递自定义参数 (List<Map<String, String>> 格式)
     * @param extensions    扩展配置参数 (List<Map<String, String>> 格式)
     * @return 完整的可分享链接
     */
    private static String generateShareableLink(String dashboardName, String ticket, List<Map<String, String>> token, List<Map<String, String>> extensions) {
        StringBuilder shareParamsBuilder = new StringBuilder(BASE_SHARE_PARAMS);
        // 添加扩展参数
        Map<String, String> params = new HashMap<>(DEFAULT_SHARE_PARAMS);

        if (extensions != null) {
            for (Map<String, String> extension : extensions) {
                for (Map.Entry<String, String> entry : extension.entrySet()) {
                    params.put(entry.getKey(), entry.getValue());
                }
            }
        }

        for (Map.Entry<String, String> entry : params.entrySet()) {
            shareParamsBuilder.append("&").append(entry.getKey()).append("=").append(entry.getValue());
        }

        if (ticket != null && !ticket.isEmpty()) {
            shareParamsBuilder.append("&sls_ticket=").append(ticket); // 添加 Ticket 参数
        }

        if (token != null && !token.isEmpty()) {
            try {
                // 构建正确格式的JSON字符串
                String tokenString = token.stream()
                        .map(m -> "{\"key\": \"" + m.get("key") + "\", \"value\": \"" + m.get("value") + "\"}")
                        .collect(Collectors.joining(", ", "[", "]"));

                // 使用自定义的encodeURIComponent方法
                String encodedToken = encodeURIComponent(tokenString);

                shareParamsBuilder.append("&token=").append(encodedToken);
            } catch (Exception e) {
                logger.error("URL 编码失败：{}", e.getMessage(), e);
                return null;
            }
        }

        return CONSOLE_URL + PROJECT_NAME + DASHBOARD_URL + dashboardName + shareParamsBuilder.toString(); // 构建完整的链接
    }

    public static String encodeURIComponent(String s) {
        try {
            return URLEncoder.encode(s, StandardCharsets.UTF_8.toString())
                    .replace("+", "%20")
                    .replace("%21", "!")
                    .replace("%27", "'")
                    .replace("%28", "(")
                    .replace("%29", ")")
                    .replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            return s;
        }
    }
}