package com.youxin.risk.commons.service.engine;

/**
 * <AUTHOR>
 * @date 2018/10/26 10:08
 */
@Deprecated
public class EventService {
//    private static final Logger LOGGER = LoggerFactory.getLogger(EventService.class);
//    private static final int MAX_ATTEMPTS = 3;
//    private static final long DELAY = 100L;
//    private static final double MULTI_PLIER = 1;

//    @Deprecated
//    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
//    public void insertEventToMongo(Event event) {
//        try {
//            EventVo eventVo = new EventVo();
//            eventVo.setId(UUID.randomUUID().toString());
//            eventVo.setSourceSystem(event.getSourceSystem());
//            eventVo.setUserKey(event.getUserKey());
//            eventVo.setLoanKey(event.getLoanKey());
//            eventVo.setSessionId(event.getSessionId());
//            eventVo.setEvent(event);
//            eventVo.setCreateTime(new Date());
//            eventDao.insert(eventVo);
//            EventCopyRecordMapper eventCopyRecordMapper = null;
//            try{
//                eventCopyRecordMapper =  (EventCopyRecordMapper) ContextUtil.getBean("eventCopyRecordMapper");
//            }catch (Exception e){
//                LoggerProxy.error("getBeanEventCopyRecordMapperError", LOGGER, "sessionId="+event.getSessionId());
//            }
//            try{
//                if(eventCopyRecordMapper != null){
//                    eventCopyRecordMapper.insert(new EventCopyRecord(event.getSessionId()));
//                    LoggerProxy.info("insertEventCopyRecord", LOGGER, "sessionId="+event.getSessionId());
//                }
//            } catch(DuplicateKeyException e){
//                throw e;
//            }catch (Exception e){
//                LoggerProxy.error("insertEventCopyRecordError", LOGGER, "sessionId="+event.getSessionId(),e);
//            }
//        } catch (DuplicateKeyException e) {
//            LoggerProxy.warn("duplicateKeyWarn", LOGGER, "sessionId={}", event.getSessionId());
//        }
//    }

//    @Deprecated
//    public void updateEventToMongo(Event event) {
////        updateEventToMongo(event, false);
//        LoggerProxy.info("skipUpdateEvent", LOGGER, "eventCode: {}", event.getEventCode());
//    }

//    @Deprecated
//    public void updateEventToMongo(Event event, boolean force) {
//        if (!force) {
//            LoggerProxy.info("skipUpdateEvent", LOGGER, "eventCode: {}", event.getEventCode());
//            return;
//        }
//
//        EventVo eventVo = new EventVo();
//        eventVo.setSessionId(event.getSessionId());
//        eventVo.setEvent(event);
//        try{
//            Map<String, Object> thirdPartyMap = (Map<String, Object>) event.getDataVo().get(DataVoUtils.getThirdPartyDataKey(event.getSourceSystem()));
//            StringBuilder stringBuilder = new StringBuilder();
//            if (null != thirdPartyMap) {
//                stringBuilder.append(",thirdPartyTotalSize:").append(thirdPartyMap.keySet().size()).append(",step:").append(event.get("step"));
//                for (String key : thirdPartyMap.keySet()) {
//                    stringBuilder.append(":").append(key);
//                }
//            }
//            LoggerProxy.info("updateEvent", LOGGER, "sessionId={},thirdParty={},params={}", event.getSessionId(), stringBuilder.toString(),
//                    JSON.toJSONString(event.getParams()));
//
//        }catch (Exception e){
//            LoggerProxy.info("logEventUpdate", LOGGER, "buildMessageError", e);
//        }
//        eventDao.updateBySessionId(eventVo);
//    }
//    @Deprecated
//    public Event getBySessionId(String sessionId) {
//        EventVo eventVo = eventDao.getBySessionId(sessionId);
//        if (eventVo == null) {
//            return null;
//        }
//        return eventVo.getEvent();
//    }

//    @Deprecated
//    public Event lastEvent(Event event) {
//        EventVo eventVo = eventDao.lastEvent(event);
//        if (eventVo == null) {
//            return null;
//        }
//        return eventVo.getEvent();
//    }

//    public EventVo getLastEventByUserKey(String userKey) {
//        return  eventDao.getLastEventByUserKey(userKey);
//    }

//    @Deprecated
//    public List<EventVo> getLastEventByUserKey(String userKey,String eventCode,String sourceSystem,List<String> loanKeyList,Integer limit) {
//        return  eventDao.getLastEventByUserKey(userKey,eventCode,sourceSystem,loanKeyList,limit);
//    }

    /**
     *
     * @param userKey
     * @param sourceSystem
     * @param eventCode
     * @param dataSource
     * @return
     */
//    @Deprecated
//    public List<EventVo> getEventsByUserKey(String userKey, String sourceSystem, String eventCode,String dataSource) {
//        List<EventVo> eventVos = eventDao.getListByUserKey(userKey, sourceSystem, eventCode,dataSource);
//        if (CollectionUtils.isEmpty(eventVos)) {
//            return Lists.newArrayList();
//        }
//
//        return eventVos;
//    }

//    @Deprecated
//    public void copyEvent(String sessionId){
//        String copyEventToBabelSwitch = CacheApi.getDictSysConfig("copyEventToShardingSwitch","0");
//        if("1".equals(copyEventToBabelSwitch)){
//            copyEventPool.execute(()->{
//                doCopyEvent(sessionId);
//            });
//        }
//    }

//    @Deprecated
//    private void doCopyEvent(String sessionId){
//        try {
//            EventVo evenVo = eventDao.getBySessionId(sessionId);
//            if(evenVo!=null){
//                eventDao.saveOrUpdateShardingMongo(evenVo);
//
//                eventDao.updateVerifyResultMongo(evenVo);
//
//                EventCopyRecordMapper eventCopyRecordMapper = null;
//                try{
//                    eventCopyRecordMapper =  (EventCopyRecordMapper) ContextUtil.getBean("eventCopyRecordMapper");
//                }catch (Exception e){
//                }
//                if(eventCopyRecordMapper != null){
//                    eventCopyRecordMapper.updateStatusBySessionId(new EventCopyRecord(sessionId,1));
//                    LoggerProxy.info("updateEventCopyRecord", LOGGER, "sessionId={}",sessionId);
//                }
//                LoggerProxy.info("doCopyEvent", LOGGER, "sessionId={}", sessionId);
//            }else{
//                LoggerProxy.error("doCopyEventError", LOGGER, "EventVo is null,sessionId={}", sessionId);
//            }
//        } catch (Exception e) {
//            LoggerProxy.error("doCopyEventError", LOGGER, e.getMessage(), e);
//            LoggerProxy.error("doCopyEventError", LOGGER, "sessionId={}", sessionId, e);
//        }
//    }
}
