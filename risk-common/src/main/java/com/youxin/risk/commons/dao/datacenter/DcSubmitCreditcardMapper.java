package com.youxin.risk.commons.dao.datacenter;

import org.apache.ibatis.annotations.Param;

import com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard;

public interface DcSubmitCreditcardMapper {
    int deleteByPrimaryKey(Integer id);

    Long insert(DcSubmitCreditcard record);

    int insertSelective(DcSubmitCreditcard record);

    DcSubmitCreditcard selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DcSubmitCreditcard record);

    int updateByPrimaryKey(DcSubmitCreditcard record);

    DcSubmitCreditcard getByOperationId(Long operationId);
    DcSubmitCreditcard getByUserKey(@Param("userKey")String userKey);
}