package com.youxin.risk.commons.model.datacenter.vo;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

/**
 * verify基础数据推送还款信息表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class SubmitLoansInfoVo extends VerifyCommonData {


    /**
     * 用户key
     */
    private String userKey;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 借款用途
     */
    private String loanUsage;

    /**
     * 其他网络借贷平台借款笔数
     */
    private Integer otherLoanNumber;

    /**
     * 还款来源
     */
    private String repaymentSource;

    /**
     * 房贷
     */
    private Integer hasHouseLoan;

    /**
     * 车贷
     */
    private Integer hasCarLoan;

    /**
     * 其他贷款
     */
    private String otherLoan;


    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getLoanUsage() {
        return loanUsage;
    }

    public void setLoanUsage(String loanUsage) {
        this.loanUsage = loanUsage == null ? null : loanUsage.trim();
    }

    public Integer getOtherLoanNumber() {
        return otherLoanNumber;
    }

    public void setOtherLoanNumber(Integer otherLoanNumber) {
        this.otherLoanNumber = otherLoanNumber;
    }

    public String getRepaymentSource() {
        return repaymentSource;
    }

    public void setRepaymentSource(String repaymentSource) {
        this.repaymentSource = repaymentSource == null ? null : repaymentSource.trim();
    }

    public Integer getHasHouseLoan() {
        return hasHouseLoan;
    }

    public void setHasHouseLoan(Integer hasHouseLoan) {
        this.hasHouseLoan = hasHouseLoan;
    }

    public Integer getHasCarLoan() {
        return hasCarLoan;
    }

    public void setHasCarLoan(Integer hasCarLoan) {
        this.hasCarLoan = hasCarLoan;
    }

    public String getOtherLoan() {
        return otherLoan;
    }

    public void setOtherLoan(String otherLoan) {
        this.otherLoan = otherLoan == null ? null : otherLoan.trim();
    }

}