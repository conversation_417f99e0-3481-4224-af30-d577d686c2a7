package com.youxin.risk.commons.constants;

public class PointConstant {
    /**
     * DI相关埋点
     */
    public static final String DI_HANDLER_POINT = AppName.risk_di.name() + "_handler";
    public static final String DI_HANDLER_SYNC_COST_TIME_POINT = AppName.risk_di.name() + "_handler_sync_costTime";
    public static final String DI_HANDLER_ASYNC_COST_TIME_POINT = AppName.risk_di.name() + "_handler_async_costTime";
    public static final String DI_CALL_SERVICE_POINT = AppName.risk_di.name() + "_callService";
    public static final String DI_CALL_SERVICE_SYNC_COST_TIME_POINT = AppName.risk_di.name() + "_callService_sync_costTime";
    public static final String DI_CALL_SERVICE_ASYNC_COST_TIME_POINT = AppName.risk_di.name() + "_callService_async_costTime";
    public static final String DI_USER_CALL_SERVICE_ASYNC_COST_TIME_POINT = AppName.risk_di.name() + "_user_callService_async_costTime";
    public static final String DI_IRR24_CALL_SERVICE_COST_TIME_POINT = AppName.risk_di.name() + "_irr24_call_service_costTime";
    public static final String DI_CALL_SERVICE_SIZE_POINT = AppName.risk_di.name() + "_call_service_size";
    public static final String DI_USERPAY_HBASE_COST_TIME_POINT = AppName.risk_di.name() + "_hbase_tableScan_costTime";
    /** DI相关埋点 */


    /**
     * gw
     */
    public static final String GW_POINT = AppName.risk_gateway.name();
    public static final String GW_POINT_HTTP_QPS = GW_POINT + "_http_qps";
    public static final String GW_POINT_ENGINE_MSG_QPS = GW_POINT + "_engine_msg_qps";
    public static final String GW_POINT_GATEWAY_NOTIFY = GW_POINT + "_gateway_notify";
    public static final String GW_POINT_CALL_ENGINE_COST_TIME = GW_POINT + "_call_engine_costTime";
    // gw 每个事件状态为已发送的数量
    public static final String GW_POINT_SENDED_COUNT = GW_POINT + "_sended_count";


    /**
     * process-engine相关埋点
     */
    public static final String PE_HANDLER_POINT = AppName.risk_process_engine.name() + "_handler_costTime";
    public static final String PE_CALL_DATAVO_POINT = AppName.risk_process_engine.name() + "_callDataVo_costTime";
    public static final String PE_CALL_FEATURE_POINT = AppName.risk_process_engine.name() + "_callFeature_costTime";
    public static final String PE_CALL_STRATEGY_POINT = AppName.risk_process_engine.name() + "_callStrategy_costTime";
    public static final String PE_CALL_DELAY_EVENT_SEND_POINT = AppName.risk_process_engine.name() + "_callDelayEventSend_costTime";
    public static final String PE_THIRD_DATA_POINT = AppName.risk_process_engine.name() + "_third_data_rate";
    public static final String PE_CALL_NODE_POINT = AppName.risk_process_engine.name()+"_callNode_costTime";
    public static final String PE_CALL_NODE_POINT_NEW = AppName.risk_process_engine.name()+"_callNodeNew_costTime";
    public static final String PE_CALL_RA_FEATURE_NODE_POINT = AppName.risk_process_engine.name()+"_raFeature_costTime";
    public static final String PE_HAO_HUAN_VERIFY_IRR24_POINT = AppName.risk_process_engine.name()+"_irr24";
    public static final String PE_THIRD_DATA_ITEM_TYPE_POINT = "third_data_item_type_7d";
    public static final String PE_ENGINE_EVENT_STATUS_POINT = AppName.risk_process_engine.name()+"_engineEventStatus";
    public static final String PE_ENGINE_VARIABLE_CALLBACK_POINT= AppName.risk_process_engine.name()+
            "_variableCallbackCostTime";
    /** process-engine相关埋点 */

    /**
     * fs相关
     */
    public static final String RM_POINT = "risk_rm";
    public static final String RM_POINT_HTTP = RM_POINT + "_http";
    public static final String RM_POINT_RUN_STRATEGY = RM_POINT + "_run_strateg";
    public static final String RM_POINT_CAL_FEATURE = RM_POINT + "_cal_feature";
    public static final String RM_POINT_CAL_FEATURE_EXP = RM_POINT + "_cal_feature_exp";
    public static final String RM_POINT_CAL_FEATURE_SPLITFOW = RM_POINT + "_cal_feature_splitflow";
    public static final String RM_POINT_RUN_STRATEGY_EXP = RM_POINT + "_run_strategy_exp";
    public static final String RM_POINT_RUN_STRATEGY_SPLITFLOW = RM_POINT + "_run_strategy_splitflow";
    public static final String RM_POINT_CAL_FC_FEATURE = RM_POINT + "_cal_feature_costTime";
    public static final String RM_POINT_CAL_FC_STEP_FEATURE = RM_POINT + "_step_feature_costTime";
    public static final String RM_POINT_CAL_FC_STRATEGY = RM_POINT + "_cal_strategy_costTime";
    public static final String RM_POINT_DR_LEVEL= RM_POINT + "_dr_level_costTime";
    public static final String RM_POINT_CAL_FC_FEATURE_REQ_SIZE = RM_POINT + "_cal_feature_req_size";
    /** fs相关 */


    /**
     * cd相关
     */
    public static final String CD_POINT = AppName.risk_credit_driver.name();
    public static final String CD_POINT_USER_KEY_QPS = CD_POINT + "_userKey_qps";
    /** cd相关 */

    /*进件审核流程*/
    public static final String verify_result_duration = "risk_hfq_verify_result_duration";
    public static final String verify_step_duration = "risk_hfq_verify_step_duration";
    public static final String verify_review_python = "risk_hfq_verify_review_python";
    public static final String verify_user_line_python = "risk_hfq_verify_user_line_python";
    public static final String ra_datavo_thirdparty = "ra_datavo_thirdparty";
    public static final String ra_datavo_feature = "ra_datavo_feature";


    /**
     * datacenter相关埋点
     */

    /**
     * 额度金额埋点
     */
    public static final String RISK_AMOUNT_DATA = AppName.risk_datacenter + "_amount_data";

    /**
     * 额度费率埋点
     */
    public static final String RISK_AMOUNT_RATE = AppName.risk_datacenter + "_amount_rate";

    /**
     * fc 相关
     * 特征和策略数据key是否被使用
     */
    public static final String FC_POINT = "python_fc";
    public static final String FC_POINT_CAL_FEATURE_ITEM_KEY_NUM = FC_POINT + "_cal_feature_input_key_num";
    public static final String FC_POINT_CAL_STRATEGY_ITEM_KEY_NUM = FC_POINT + "_cal_strategy_input_key_num";
}
