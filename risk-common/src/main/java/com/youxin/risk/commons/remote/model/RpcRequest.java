package com.youxin.risk.commons.remote.model;

import java.util.Map;
import java.util.TreeMap;

import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.BaseEntity;
import com.youxin.risk.commons.utils.SystemUtil;

public class RpcRequest<T> extends BaseEntity {

    private static final long serialVersionUID = -5930010048062597917L;

    // 版本号
    private String version = "1.0";
    // 客户端编码，参考appName
    private String appName;
    // 请求唯一主键
    private String requestId;
    // 是否重试（超时请求isRetry=true，reqId）
    private boolean isRetry;
    // 是否心跳请求
    private boolean isValidation;
    // 接口命令字
    private String command = "default-rpc";
    // 关键字段，用于日志监控，例如：productType, eventType
    private TreeMap<String, String> keys = Maps.newTreeMap();

    private String sourceIp = SystemUtil.getLocalIp();
    // 数据
    private T data;

    public RpcRequest() {

    }

    public RpcRequest(String reqId) {
        this.requestId = reqId;
    }

    public String getSourceIp() {
        return this.sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppName() {
        return this.appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getRequestId() {
        return this.requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isRetry() {
        return this.isRetry;
    }

    public void setRetry(boolean isRetry) {
        this.isRetry = isRetry;
    }

    public boolean isValidation() {
        return this.isValidation;
    }

    public void setValidation(boolean isValidation) {
        this.isValidation = isValidation;
    }

    public String getCommand() {
        return this.command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public TreeMap<String, String> getKeys() {
        return this.keys;
    }

    public void setKeys(TreeMap<String, String> keys) {
        this.keys = keys;
    }

    public void addKeys(Map<String, String> keys) {
        this.keys.putAll(keys);
    }

    public void addKey(String key, String value) {
        this.keys.put(key, value);
    }
}