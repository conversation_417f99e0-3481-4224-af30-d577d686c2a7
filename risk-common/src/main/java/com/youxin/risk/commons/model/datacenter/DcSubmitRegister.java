package com.youxin.risk.commons.model.datacenter;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

import java.util.Date;

/**
 * verify基础数据推送注册信息表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class DcSubmitRegister extends BaseModel {

    /**
     * operation_log表id
     */
    private Long operationLogId;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * idfa号
     */
    private String idfa;

    /**
     * imei号
     */
    private String imei;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 原电话
     */
    private String oldMobile;

    /**
     * 同盾指纹
     */
    private String tongdunFingerprint;


    public Long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa == null ? null : idfa.trim();
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei == null ? null : imei.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getOldMobile() {
        return oldMobile;
    }

    public void setOldMobile(String oldMobile) {
        this.oldMobile = oldMobile == null ? null : oldMobile.trim();
    }

    public String getTongdunFingerprint() {
        return tongdunFingerprint;
    }

    public void setTongdunFingerprint(String tongdunFingerprint) {
        this.tongdunFingerprint = tongdunFingerprint == null ? null : tongdunFingerprint.trim();
    }

}