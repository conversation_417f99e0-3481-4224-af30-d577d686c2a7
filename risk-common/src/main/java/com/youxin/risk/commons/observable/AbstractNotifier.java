package com.youxin.risk.commons.observable;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

public class AbstractNotifier {
    private static final Logger logger = LoggerFactory.getLogger(AbstractNotifier.class);

    protected Set<IListener> listenerSet;
    @Getter
    protected String type;

    public AbstractNotifier(String type) {
        this.listenerSet = new HashSet<>();
        this.type = type;
        logger.info("Notifier type: {}", type);
    }

    public synchronized void addListener(IListener listener){
        listenerSet.add(listener);
    }

    public synchronized void removeListener(IListener listener){
        listenerSet.remove(listener);
    }

    public synchronized void notifyListeners(Object arg){
        for (IListener listener: listenerSet){
            listener.consume(this, arg);
        }
    }
}
