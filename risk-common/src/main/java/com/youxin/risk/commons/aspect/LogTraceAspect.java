package com.youxin.risk.commons.aspect;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.annotation.LogTrace;
import com.youxin.risk.commons.model.EngineEvent;
import com.youxin.risk.commons.utils.AspectUtil;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @desc
 */
@Aspect
@Component
@Slf4j
public class LogTraceAspect {

    @Around(value = "@annotation(com.youxin.risk.commons.annotation.LogTrace)")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        //原始logid
        String originLogId = LogUtil.getLogIdFromContext();
        LogTrace logTrace = AspectUtil.getDeclaredAnnotation(joinPoint, LogTrace.class);
        try {
            //如果traceKey为空，则自动生成logid
            if (StringUtils.isBlank(logTrace.traceKey())) {
                LogUtil.buildAndBindLog();
            } else {
                Map<String, Object> paramsMap = AspectUtil.getParamsMap(joinPoint);
                String traceValue = getValue(logTrace.traceKey(), paramsMap, String.class);
                if (StringUtils.isNotBlank(traceValue)) {
                    LogUtil.bindLogId(traceValue);
                }
            }
        } catch (Exception e) {
            log.warn("绑定logid异常：路径={}，异常消息=", logTrace.traceKey(), e);
        }

        Object returnObj = null;
        try {
            returnObj = joinPoint.proceed();
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            //该方法结束后，绑定原logid
            LogUtil.bindLogId(originLogId);
        }

        return returnObj;
    }

    public static void main(String[] args) {
        EngineEvent engineEvent = new EngineEvent();
        engineEvent.setSessionId("123");
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("engineEvent", engineEvent);
        String value = getValue("engineEvent/sessionId", paramsMap, String.class);
        System.out.println(value);
    }

    private static   <T> T getValue(String path, Map<String, Object> paramsMap, Class<T> clazz) {

        String[] keys = path.split("/");
        String jsonString = JSON.toJSONString(paramsMap);
        JSONObject jsonObject = JSON.parseObject(jsonString);
        for (int i = 0; i < keys.length - 1; i++) {
            jsonObject = jsonObject.getJSONObject(keys[i]);
        }
        return jsonObject.getObject(keys[keys.length - 1], clazz);
    }


}
