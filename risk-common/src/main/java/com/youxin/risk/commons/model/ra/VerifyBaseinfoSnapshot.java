package com.youxin.risk.commons.model.ra;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * verify_baseinfo_snapshot
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VerifyBaseinfoSnapshot implements Serializable {
    private Long id;

    private String userKey;

    private String loanKey;

    private String eventCode;

    private String strategyType;

    private String industry;

    private String eduexperience;

    private String salary;

    private String companyPosition;

    private String marriage;

    private String province;

    private String city;

    private String platform;

    private Integer jailBroken;

    private String appVersion;

    private String device;

    private String deviceId;

    private String carTotalAmount;

    private String carLoanAmount;

    private String haveCar;

    private String houseTotalAmount;

    private String houseNature;

    private String houseBuyPeriod;

    private String mobile0;

    private String mobile1;

    private String ext;

    private Date createTime;

    private static final long serialVersionUID = 1L;
}