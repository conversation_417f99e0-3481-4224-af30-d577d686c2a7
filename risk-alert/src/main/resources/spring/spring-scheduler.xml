<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/task
     http://www.springframework.org/schema/task/spring-task.xsd">

    <!-- 配置任务线性池 -->
    <task:executor id="executor" pool-size="3"/>
    <task:scheduler id="scheduler" pool-size="3"/>

    <!-- 启用annotation方式 -->
    <task:annotation-driven scheduler="scheduler" executor="executor" proxy-target-class="true"/>

</beans>