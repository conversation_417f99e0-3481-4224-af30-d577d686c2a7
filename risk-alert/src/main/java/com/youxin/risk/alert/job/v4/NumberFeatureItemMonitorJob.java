package com.youxin.risk.alert.job.v4;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.alert.collecter.Collecter;
import com.youxin.risk.alert.collecter.CollecterFactory;
import com.youxin.risk.alert.constants.AlertSourceEnum;
import com.youxin.risk.alert.sender.impl.RobotAlertSender;
import com.youxin.risk.alert.vo.AlertEvent;
import com.youxin.risk.alert.vo.QueryResultModel;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.model.AlertPolicy;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.GW_SPACE;

/**
 * 数值型特征项监控
 *
 * <AUTHOR>
 */
@Component
public class NumberFeatureItemMonitorJob implements XxlJobBase {


    private Logger logger = LoggerFactory.getLogger(NumberFeatureItemMonitorJob.class);

    @Resource(name = "RobotAlertSender")
    private RobotAlertSender sender;

    @Autowired
    private RetryableJedis retryableJedis;

    private static final String REDIS_KEY = "feature_item_miss_monitor_redis_key";
    private static final String ENTER = "\n";

    @Override
    @XxlJob(value = "numberFeatureItemMonitorJob")
    public ReturnT<String> execJobHandler(String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        String robotId = jsonObject.getString("robotId");
        String robotIdForBatch = jsonObject.getString("robotIdForBatch");
        logger.info("numberFeatureItemMonitorJob start");
        process(robotId, null, null);
        processBatch(robotIdForBatch, null, null);
        return ReturnT.SUCCESS;
    }

    public void process(String robotId, String isHeart, String featureName) {
        try {
            AlertPolicy policy = new AlertPolicy();
            policy.setRobotKey(robotId);
            policy.setPolicyName("特征项均值策略");
            String msg = processAlert(isHeart, featureName);
            if (Strings.isNullOrEmpty(msg) && !StringUtils.isNotEmpty(isHeart)) {
                LoggerProxy.info("process", logger, "");
                return;
            }

            if(StringUtils.isNotEmpty(isHeart)){
                if(Strings.isNullOrEmpty(msg)){
                    msg = String.format("%s-心跳无数据", featureName);
                }
            }

            AlertEvent alertEvent = new AlertEvent(AlertSourceEnum.riskAlert, msg);
            alertEvent.setTitle(StringUtils.isEmpty(isHeart) ? "【特征项均值报警,2.0代表无数据】" : "心跳检测（可忽略）");
            alertEvent.setAlertPolicy(policy);
            sender.send(alertEvent);
        } catch (Exception e) {
            LoggerProxy.error("process",logger,"特征项均值报警异常", e);
        }
    }

    public void processBatch(String robotIdForBatch, String isHeart, String featureName) {
        try {
            AlertPolicy policy = new AlertPolicy();
            policy.setRobotKey(robotIdForBatch);
            policy.setPolicyName("特征项均值策略(跑批)");
            String msg = processAlertForBatch(isHeart, featureName);
            if (Strings.isNullOrEmpty(msg) && !StringUtils.isNotEmpty(isHeart)) {
                LoggerProxy.info("processBatch", logger, "");
                return;
            }

            if(StringUtils.isNotEmpty(isHeart)){
                if(Strings.isNullOrEmpty(msg)){
                    msg = String.format("%s-心跳无数据", featureName);
                }
            }

            AlertEvent alertEvent = new AlertEvent(AlertSourceEnum.riskAlert, msg);
            alertEvent.setTitle(StringUtils.isEmpty(isHeart) ? "【特征项均值报警（跑批）,2.0代表无数据】" : "心跳检测（跑批）（可忽略）");
            alertEvent.setAlertPolicy(policy);
            sender.send(alertEvent);
        } catch (Exception e) {
            LoggerProxy.error("processBatch",logger, "特征项均值报警异常(跑批)", e);
        }
    }



    private String processAlert(String isHeart, String featureName) {
        Collecter collecter = CollecterFactory.getCollecter(CollecterFactory.DatasourceType.influxdb);
        Map<String, Double> todayMap;
        Map<String, Double> yesMap;
        Map<String, Double> yesTotalMap;
        StringBuilder builder = new StringBuilder();
        try {
            int window = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.window", "10"));
            int baseWindow = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.base.window", "30"));
            int days = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.base.days", "1"));
            String missCountCommmand = getCommand("totalValue","count",window);
            String yesMissCountCommmand = getYesCommand("totalValue","count",baseWindow, days);
            String yesTotalCountCommmand = getYesTotalCommand(baseWindow, days);
            todayMap = buildResult(collecter.parseResult(collecter.collect(missCountCommmand)));
            yesMap = buildResult(collecter.parseResult(collecter.collect(yesMissCountCommmand)));
            yesTotalMap = buildResult(collecter.parseResult(collecter.collect(yesTotalCountCommmand)));

            Set<String> allKeys = new HashSet<>();
            allKeys.addAll(todayMap.keySet());
            allKeys.addAll(yesMap.keySet());
            LoggerProxy.info("processAlert", logger, "featureItemMissData todayMap:{} , yesMap:{} isHeart: {}", JSON.toJSONString(todayMap), JSON.toJSONString(yesMap), isHeart);
            Map<String, String> alertMessageMap = getAlertMessageMap(allKeys, todayMap, yesMap,yesTotalMap, isHeart, featureName, days);
            LoggerProxy.info("processAlert", logger, "featureItemMissAlertMessage,alertMessage:{} isHeart: {}", JSON.toJSONString(alertMessageMap), isHeart);
            getAlertMessage(alertMessageMap, builder);
            return builder.toString();
        } catch (Exception e) {
            LoggerProxy.error("processAlert", logger, "processAlertError,policy = featureItemMissMonitor, isHeart: {}", isHeart, e);
        }
        return null;
    }

    private String processAlertForBatch(String isHeart, String featureName) {
        Collecter collecter = CollecterFactory.getCollecter(CollecterFactory.DatasourceType.influxdb);
        Map<String, Double> totalCountMap;
        Map<String, Double> missRateMap;
        Map<String, Object> redisMap;
        StringBuilder builder = new StringBuilder();
        try {
            int window = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.window", "10"));
            int baseWindow = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.base.window", "30"));
            String totalCountCommmand = getTotalCommand(baseWindow);
            String missRateCommmand = getCommand("totalValue","count",window);

            totalCountMap = buildResult(collecter.parseResult(collecter.collect(totalCountCommmand)));
            missRateMap = buildResult(collecter.parseResult(collecter.collect(missRateCommmand)));

            String redisStr = retryableJedis.get(REDIS_KEY);
            if(StringUtils.isNotEmpty(redisStr)){
                redisMap = JSONObject.parseObject(redisStr, Map.class);
            }else{
                LoggerProxy.warn("processAlertForBatch", logger, "isHeart: {} redisStr is empty", isHeart);
                redisMap = new HashMap<>();
            }

            Set<String> allKeys = new HashSet<>();
            allKeys.addAll(missRateMap.keySet());
            allKeys.addAll(redisMap.keySet());
            LoggerProxy.info("processAlertForBatch", logger, "featureItemMissDataBatch missRateMap:{} , redisMap:{} isHeart: {}", JSON.toJSONString(missRateMap), JSON.toJSONString(redisMap), isHeart);
            Map<String, String> alertMessageMap = getAlertMessageMapForBatch(allKeys, missRateMap, redisMap,totalCountMap, isHeart, featureName);
            LoggerProxy.info("processAlertForBatch", logger, "featureItemMissAlertMessageBatch,alertMessage:{} isHeart: {}", JSON.toJSONString(alertMessageMap), isHeart);
            getAlertMessage(alertMessageMap, builder);

            retryableJedis.setex(REDIS_KEY,60*60*24*3, JSONObject.toJSONString(redisMap));

            return builder.toString();
        } catch (Exception e) {
            LoggerProxy.error("processAlertForBatch", logger, "processAlertError,policy = featureItemMissMonitor", e);
        }
        return null;
    }

    private void getAlertMessage(Map<String, String> alertMessageMap,StringBuilder builder) {
        if (alertMessageMap.isEmpty()) {
            return;
        }
        Set<String> keys = alertMessageMap.keySet();
        for (String key : keys) {
            //企业微信机器人发送信息有长度限制
            if (builder.length() > 1000) {
                LoggerProxy.warn("getAlertMessage", logger, "builder length too large, length: {}", builder.length());
                return;
            }
            builder.append(key).append(":").append(ENTER).append(alertMessageMap.get(key)).append(ENTER);
        }
    }

    private Map<String, String> getAlertMessageMap(Set<String> allKeys, Map<String, Double> todayMap,
                                                   Map<String, Double> yesMap, Map<String, Double> yesTotalMap,
                                                   String isHeart, String featureName, int days) {
        Map<String, String> messageMap = new HashMap<>();
        double threshold = Double.parseDouble(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.yoy", "0.3"));
        String specialConfigStr = NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.special.config", "{}");
        int alertCount = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.alert.count", "30"));

        JSONObject specialConfig = JSON.parseObject(specialConfigStr);
        double tmpThreshold = 0D;
        for (String key : allKeys) {
            //剔除跑批的
            Set<String> batchEventLimitSet = ApolloClientAdapter.getMapConfig(GW_SPACE, "batch.event.limit.map", Integer.class).keySet();
            if(batchEventLimitSet.contains(key.split("-")[0])){
                continue;
            }

            tmpThreshold = threshold;
            // 数据量太少，不报警
            Double c = yesTotalMap.get(key);
            if (c == null){
                LoggerProxy.info("skipAlertByNullBaseCount", logger, "key = {}", key);
                continue;
            }

            if (c <= alertCount){
                LoggerProxy.info("skipAlertBySmallPointCount", logger, "featureItem: {}, pointCount: {}, thres: {}", key, c, alertCount);
                continue;
            }

            Double today = todayMap.get(key);
            Double yes = yesMap.get(key);
            if (today == null || today <= 0D){
                continue;
            }
            today = rounded(today);
            yes = rounded(yes);
            if (specialConfig.containsKey(key)){
                tmpThreshold = specialConfig.getDoubleValue(key);
            }
            if ((today - yes) > tmpThreshold ||
                    (StringUtils.isNotEmpty(isHeart) &&
                            (StringUtils.isEmpty(featureName) || key.split("-")[1].equals(featureName)) )){
                messageMap.put(key,String.valueOf(today));
                messageMap.put(key, "当前/" + days +"天前：" + today + "/" + yes);
            }
        }
        return messageMap;
    }

    private Map<String, String> getAlertMessageMapForBatch(Set<String> allKeys, Map<String, Double> missRateMap,
                                                   Map<String, Object> redisMap, Map<String, Double> totalCountMap,
                                                           String isHeart, String featureName) {
        Map<String, String> messageMap = new HashMap<>();
        double threshold = Double.parseDouble(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.yoy", "0.3"));
        String specialConfigStr = NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.special.config", "{}");
        int alertCount = Integer.parseInt(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.item.monitor.alert.count", "30"));

        JSONObject specialConfig = JSON.parseObject(specialConfigStr);
        double tmpThreshold;
        for (String key : allKeys) {
            //剔除非跑批的
            Set<String> batchEventLimitSet = ApolloClientAdapter.getMapConfig(GW_SPACE, "batch.event.limit.map", Integer.class).keySet();
            if(!batchEventLimitSet.contains(key.split("-")[0])){
                continue;
            }
            tmpThreshold = threshold;
            // 数据量太少，不报警
            Double c = totalCountMap.get(key);
            if (c == null || c <= alertCount){
                LoggerProxy.info("skipAlertBySmallPointCount", logger, "featureItem: {}, pointCount: {}, thres: {}", key, c, alertCount);
                continue;
            }

            Double missRate = missRateMap.get(key);
            if (missRate == null || missRate <= 0D){
                continue;
            }
            Double redisValue = null;
            if(redisMap.containsKey(key)){
                redisValue = ((BigDecimal)redisMap.get(key)).doubleValue();// 反序列化之后直接转double会报错，反序列化成了BigDecimal类型
                if(redisValue <= 0D){
                    redisMap.put(key, missRate);
                    continue;
                }
            }

            missRate = rounded(missRate);
            redisValue = rounded(redisValue);
            if (specialConfig.containsKey(key)){
                tmpThreshold = specialConfig.getDoubleValue(key);
            }
            if ((missRate - redisValue) > tmpThreshold ||
                    (StringUtils.isNotEmpty(isHeart) &&
                            (StringUtils.isEmpty(featureName) || key.split("-")[1].equals(featureName)) )){
                messageMap.put(key,String.valueOf(missRate));
                messageMap.put(key, "当前/上次：" + missRate + "/" + redisValue);
            }

            if((missRate - redisValue) <= tmpThreshold){
                redisMap.put(key, missRate);
            }
        }
        return messageMap;
    }

    private static double rounded(Double origin){
        origin = origin == null ? 0D : origin;
        BigDecimal b = BigDecimal.valueOf(origin);
        return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    private String getTotalCommand(int window) {
        return "SELECT count(count) FROM feature_number_item_monitor " +
                "where time > now() - " + window + "m GROUP BY eventCode,featureName,featureItemName fill(2) &db=antifraud_risk_number_feature_monitor";
    }

    private String getYesTotalCommand(int window, int days) {
        return "SELECT count(count) FROM feature_number_item_monitor " +
                "where time > now() - " + (days * 24 * 60 + window) + "m and time < now() - " + days * 24 * 60 + "m GROUP BY eventCode,featureName,featureItemName fill(0) &db=antifraud_risk_number_feature_monitor";
    }


    private String getCommand(String numerator,String denominator,int window) {
        return "SELECT sum(" + numerator + ")/sum(" + denominator + ") FROM feature_number_item_monitor " +
                "where time > now() - " + window + "m GROUP BY eventCode,featureName,featureItemName fill(2) &db=antifraud_risk_number_feature_monitor";
    }

    private String getYesCommand(String numerator,String denominator,int window, int days) {
        return "SELECT sum(" + numerator + ")/sum(" + denominator + ") FROM feature_number_item_monitor " +
                "where time > now() - " + (days * 24 * 60 + window) + "m and time < now() - " + days * 24 * 60 + "m GROUP BY eventCode,featureName,featureItemName fill(2) &db=antifraud_risk_number_feature_monitor";
    }


    private Map<String, Double> buildResult(Map<String, Object> retNowMap) {
        String status = (String) retNowMap.get("status");
        if (!Collecter.ParseStatusEnum.no_data.toString().equals(status) && !Collecter.ParseStatusEnum.OK.toString().equals(status)) {
            LoggerProxy.warn("queryMabyeFailed", logger, "status = {}", status);
            return new HashMap<>();
        }

        JSONObject jsonObj = JSON.parseObject(NacosClient.getByNameSpace(ApolloNamespace.ALERT_NAMESPACE, "feature.monitor.channel.warn", "{}"));
        HashMap<String, Double> resultMap = new HashMap<>();
        for (String key : retNowMap.keySet()) {
            if ("status".equals(key)) {
                continue;
            }
            QueryResultModel nowModel = (QueryResultModel) retNowMap.get(key);
            if (null == nowModel) {
                LoggerProxy.error("queryMabyeFailed", logger, "");
                continue;
            }
            String tags = nowModel.getTags();
            JSONObject jsonTags = JSON.parseObject(tags);
            String eventCode = jsonTags.getString("eventCode");
            String featureName = jsonTags.getString("featureName");
            String featureItemName = jsonTags.getString("featureItemName");

            try{
                //读取apollo配置
                boolean flag = false;
                for(String jKey: jsonObj.keySet()){
                    List<String> split = Arrays.asList(jsonObj.getString(jKey).split(";"));
                    if(flag = (featureName.equals(jKey) &&
                            new Date().before(DateUtils.parseDate(split.get(0),"yyyy-MM-dd")))){
                        if(!split.contains(eventCode) && split.size()>1){
                            flag=false;
                        }
                        break;
                    }
                }
                if(flag){
                    LoggerProxy.info("disableAlertByConfig", logger, "featureName = " + featureName);
                    continue;
                }
            }catch (Exception e){
                LoggerProxy.error("buildResult", logger, e.getMessage());
            }

            resultMap.put(eventCode + "-" + featureName + "-" + featureItemName, nowModel.getResult());
        }
        return resultMap;
    }
}
