mode.name=prod
app.name=risk-credit-driver

home.base=/opt/app/tomcat

app.home=${home.base}/webapps/ROOT
app.log.home=${catalina.base}/logs

tomcat.home=${home.base}
tomcat.port=8101
tomcat.shutdown.port=8102
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx3550m -Xms3550m -Xmn1500m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=OFF

#admin ????
datasource.admin.maxActive=10
datasource.admin.initialSize=2
datasource.admin.minIdle=2

datasource.maxActive=10
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=****************************************?${datasource.url.params}
admin.datasource.username=${SEC_RISK_ADMIN_DB_USERNAME}
admin.datasource.pwd=${SEC_RISK_ADMIN_DB_PASSWORD}

cd.datasource.url=************************************************?${datasource.url.params}
cd.datasource.username=${SEC_RISK_CREDIT_DRIVER_DB_USERNAME}
cd.datasource.pwd=${SEC_RISK_CREDIT_DRIVER_DB_PASSWORD}

stat.datasource.url=*****************************************?${datasource.url.params}
stat.datasource.username=${SEC_RISK_ENGINE_DB_USERNAME}
stat.datasource.pwd=${SEC_RISK_ENGINE_DB_PASSWORD}

haohuan.datasource.url=****************************************?${datasource.url.params}
haohuan.datasource.username=${SEC_RISK_HAOHUAN_DB_DB_USERNAME}
haohuan.datasource.pwd=${SEC_RISK_HAOHUAN_DB_DB_PASSWORD}

redis.maxTotal=300
redis.maxIdle=30
redis.minIdle=10
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=1c1zOc9cHCGE9xmOZ51jzM
redis.cluster.nodes=***********:6398,***********:6395,***********:6396,************:6393,************:6394,***********:6399

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

di.url=http://antifraud-risk-di.rrdbg.com/handler

rrd.getUserBaseInfo.url=http://credit.rrdbg.com/thirdparty/getUserBaseInfo

gateway.crediting.url=http://antifraud-risk-gateway.rrdbg.com/risk/api/analyse


kafka.dp.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
kafka.mirror.dp.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
kafka.admin.cd.topic=risk.admin.cd.userKey.topic
kafka.admin.cd.topic.group.id=risk_admin_cd_group

kafka.engine.delay.event.message.topic=kafka.engine.delay.event.message.topic
kafka.engine.delay.event.message.topic.group.id=kafka.engine.delay.event.message.group


dc.batch.query.url=http://antifraud-risk-datacenter.rrdbg.com/dc/handler/queryUserBasicInfo
dc.inside.batch.query.url=http://antifraud-risk-datacenter-inside.weicai.com.cn/dc/handler/queryUserBasicInfo

#risk-alert
send.alert.url=http://antifraud-risk-alert.rrdbg.com/alert/api/event/riskAlert/v1

mongo.risk.host=rs1.wsmongo.app.rrd:27018,rs2.wsmongo.app.rrd:27018,arbiter1.wsmongo.app.rrd:27018
mongo.risk.username=${SEC_RISK_CREDIT_DRIVER_15_41_RISK_MONGODB_USERNAME}
mongo.risk.password=${SEC_RISK_CREDIT_DRIVER_15_41_RISK_MONGODB_PASSWORD}
mongo.risk.database=risk
mongo.risk.credentials=${mongo.risk.username}:${mongo.risk.password}@${mongo.risk.database}

mongo.sharding.host=************:27017,************:27017,************:27017
mongo.sharding.username=${SEC_RISK_CREDIT_DRIVER_12_150_N_RISK_MONGODB_USERNAME}
mongo.sharding.password=${SEC_RISK_CREDIT_DRIVER_12_150_N_RISK_MONGODB_PASSWORD}
mongo.sharding.database=risk
mongo.sharding.credentials=${mongo.sharding.username}:${mongo.sharding.password}@${mongo.sharding.database}

haofenqi.userCancel.url=http://haofenqi-user-server.haohuan.com/api/v1/user/cancel


metrics.point.kafka.hosts=kafka1.cdh.app.rrd:9092,kafka2.cdh.app.rrd:9092,kafka3.cdh.app.rrd:9092
#metrics.point.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
metrics.point.kafka.topic=metrics.point.kafka.topic
metrics.point.kafka.group.id=metrics.point.kafka.group
metrics.point.mirror.kafka.hosts=kafka5.cdh:9092,kafka6.cdh:9092,kafka7.cdh:9092
metrics.point.kafka.topic.list=metrics.point.kafka.topic,metrics.point.kafka.topic.gateway

youxin.env=PROD

# xxljob config
xxl.job.admin.addresses=http://risk-xxl-job-manager.weicai.com.cn
xxl.job.accessToken=
xxl.job.executor.appname=risk-credit-driver
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1