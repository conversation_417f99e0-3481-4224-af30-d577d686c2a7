package com.youxin.risk.driver.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.youxin.risk.commons.model.creditDriver.CdJobRecord;
import com.youxin.risk.commons.mongo.OfflineBatchMongoDao;
import com.youxin.risk.driver.common.CdConstant;
import com.youxin.risk.driver.interfaces.DataLoader;
import com.youxin.risk.driver.model.JobContext;
import com.youxin.risk.driver.utils.DateUtils;

/**
 *  mongod获取T-1制备用户数据
 */
@Service("hfqLineMiddleDataLoader")
public class HfqLineMiddleDataLoader implements DataLoader {

    @Autowired
    private OfflineBatchMongoDao offlineBatchMongoDao;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public JobContext loadData(Object param) {
        JobContext jobContext = new JobContext();
        CdJobRecord cdJobRecord = (CdJobRecord) param;
        jobContext.setCdJobRecordParam(cdJobRecord);
        // query贷中额度今日待执行用户
        String queryDate = DateUtils.getCurDayDate();
        jobContext.setLoadedData(this.offlineBatchMongoDao.getUndealBatch(CdConstant.OFF_LINE_PROCESSID_MIDDLE, queryDate));

        return jobContext;
    }

}
