package com.youxin.risk.driver.task;

import com.youxin.risk.driver.model.CdConfigKeyEnum;
import com.youxin.risk.driver.model.JobNamespaceEnum;

/**
 * 好分期贷中已结清job
 */
//@Service
public class HfqLineMiddlePayOffJob extends BaseCreditJob {


    @Override
    public String controlJobKey() {
        return CdConfigKeyEnum.HfqLineMiddlePayOffJob_SwitchKey.name();
    }

    @Override
    public String getLockName() {
        return getJobNamespace() + controlJobKey();
    }

    @Override
    protected String getJobNamespace() {
        return JobNamespaceEnum.HFQ_LINE_MIDDLE_PAY_OFF_LIST.name();
    }
}
