package com.youxin.risk.driver.service.batch;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.mongo.OfflineBatchMongoDao;
import com.youxin.risk.commons.mongo.OfflineBatchRecordMongoDao;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.*;
import com.youxin.risk.commons.vo.OfflineBatchRecordVo;
import com.youxin.risk.commons.vo.OfflineBatchVo;
import com.youxin.risk.driver.common.CdConstant;
import com.youxin.risk.driver.interfaces.DataProcesser;
import com.youxin.risk.driver.utils.DateUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 从研究平台mongo拉取待跑批用户数据基类
 */
abstract class BaseMongoRunBatchDataProcesser implements DataProcesser {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${gateway.crediting.url}")
    private String gatewayCreditingUrl;

    @Autowired
    private OfflineBatchMongoDao offlineBatchMongoDao;

    @Autowired
    private OfflineBatchRecordMongoDao offlineBatchRecordMongoDao;
    @Getter
    @Setter
    protected static int processId = -1;


    @Override
    public void processData(Object data) {
        if (data == null) {
            return;
        }
        List<OfflineBatchVo> list = ( List<OfflineBatchVo>)data;
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (OfflineBatchVo batchVo : list) {
            try {
                handleBatch(batchVo);
            } catch (Exception e) {
                LoggerProxy.error("handleBatchError", logger, "batch="+JsonUtils.toJson(batchVo),e);
            }
        }
    }

    private void handleBatch(OfflineBatchVo batchVo) {
        LoggerProxy.info("handleBatchRecord",logger,"start handle, batch={}",JsonUtils.toJson(batchVo));
        List<OfflineBatchRecordVo> records = this.offlineBatchRecordMongoDao.getRecords(batchVo.getBatchId(), processId);
        if (CollectionUtils.isEmpty(records)) {
            LoggerProxy.warn("batchRecordEmpty",logger,"batch={}", JsonUtils.toJson(batchVo));
            this.offlineBatchMongoDao.update2Finished(batchVo);
            return;
        }
        Boolean isBatchFinished = true;
        for (OfflineBatchRecordVo recordVo : records) {
            if (!this.handleRecord(recordVo)) {
                isBatchFinished = false;
            }
            SystemUtil.threadSleep(getDelay()); //单次请求完成sleep 50ms,限流,降低gateway压力
        }

        if (isBatchFinished) {
            this.offlineBatchMongoDao.update2Finished(batchVo);
        }

    }

    private int getDelay() {
        Integer delay = 50;
        try {
            String lineMiddleDelayTime = CacheApi.getDictSysConfig(CdConstant.LINE_MIDDLE_DELAY_TIME);
            if (StringUtils.isNotEmpty(lineMiddleDelayTime)) {
                delay = Integer.valueOf(lineMiddleDelayTime);
            }
        } catch (Exception e) {
            LoggerProxy.warn("getDelayTimeError",logger,"",e);
        }
        return delay;
    }

    private Boolean handleRecord(OfflineBatchRecordVo recordVo) {
        try {
            Integer recordId = recordVo.getRecordId();
            if (recordVo.getData() == null) {
                LoggerProxy.error("bathHandRecordDataNull",logger,"recordId={}",recordId);
                return false;
            }
            String userKey = (String) recordVo.getData().get("userkey");
            if (StringUtils.isBlank(userKey)) {
                LoggerProxy.error("findUserKeyFailed",logger,"recordId={}",recordId);
                return false;
            }

            if (!this.callGateway(userKey, recordVo)) {
                return false;
            }

            this.offlineBatchRecordMongoDao.update2Success(recordVo);

            return true;
        } catch (Exception e) {
            LoggerProxy.error("recordHandleError",logger,"record="+JsonUtils.toJson(recordVo),e);
        }

        return false;
    }

    private Boolean callGateway(String userKey,OfflineBatchRecordVo recordVo) {
        try {
            HashMap<String, String> request = this.buildGwRequest(userKey, recordVo);
            String result = SyncHTTPRemoteAPI.post(gatewayCreditingUrl, request, 10000);
            LoggerProxy.info("postRecordDataToGatewaySuccess", logger, "request={},resp={}",
                    JSONObject.toJSONString(request), result);
        } catch (Exception ex) {
            LoggerProxy.error("postRecordDataToGatewayError", logger, "partnerUserId=" + userKey + ",recordId"+recordVo.getRecordId(), ex);
            return false;
        }
        return true;
    }

    abstract HashMap<String, String> buildGwRequest(String userKey,OfflineBatchRecordVo recordVo);

    protected String getRequestId(String userKey,OfflineBatchRecordVo recordVo) {
        return GlobalUtil.getSessionId();
    }
}
