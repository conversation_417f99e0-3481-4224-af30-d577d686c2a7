<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-info.eslog</File> -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder> 
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/${app.name}-info.eslog.%d{yyyy-MM-dd}</FileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>
    
    <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-warn.eslog</File> -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/${app.name}-warn.eslog.%d{yyyy-MM-dd}</FileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>
    
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <File>${app.log.home}/${app.name}-error.eslog</File> -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${app.log.home}/${app.name}-error.eslog.%d{yyyy-MM-dd}</FileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>
    
     <appender name="console" class="ch.qos.logback.core.ConsoleAppender">  
         <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${console.log.level}</level>
        </filter>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.youxin.risk.commons.logback.RiskLogbackLayout">
                <Pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"}^|%p^|%t^|%tid^|%X{logid}^|%logger{0}^|%risk_method:%line^|%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>  
    
    <logger name="org.apache.axis.ConfigurationException" level="INFO" />
    
    <logger name ="com" level="info">
        <appender-ref ref="info" />
        <appender-ref ref="warn" />
        <appender-ref ref="error" />
        <appender-ref ref="console" />
    </logger>
    
    <logger name ="org" level="info">
        <appender-ref ref="info" />
        <appender-ref ref="warn" />
        <appender-ref ref="error" />
        <appender-ref ref="console" />
    </logger>
</configuration>
