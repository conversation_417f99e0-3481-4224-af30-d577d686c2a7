spring.profiles.active=@activeEnv@
di.url=${di.url}

rrd.getUserBaseInfo.url=${rrd.getUserBaseInfo.url}

gateway.crediting.url=${gateway.crediting.url}

dc.batch.query.url=${dc.batch.query.url}

dc.inside.batch.query.url=${dc.inside.batch.query.url}

send.alert.url=${send.alert.url}

haofenqi.userCancel.url=${haofenqi.userCancel.url}

xxl.job.admin.addresses=${xxl.job.admin.addresses}
xxl.job.accessToken=${xxl.job.accessToken}
xxl.job.executor.appname=${xxl.job.executor.appname}
xxl.job.executor.address=${xxl.job.executor.address}
xxl.job.executor.ip=${xxl.job.executor.ip}
xxl.job.executor.port=${xxl.job.executor.port}
xxl.job.executor.logpath=${xxl.job.executor.logpath}
xxl.job.executor.logretentiondays=${xxl.job.executor.logretentiondays}