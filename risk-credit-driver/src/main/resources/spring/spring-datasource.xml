<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/aop
     http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd 
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
     http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-4.0.xsd">

	<bean id="baseDruidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="maxActive" value="${datasource.maxActive}" />
        <property name="initialSize" value="${datasource.initialSize}" />
        <property name="minIdle" value="${datasource.minIdle}" />
        <property name="maxWait" value="${datasource.maxWait}" />
        <property name="testOnBorrow" value="${datasource.testOnBorrow}" />
        <property name="defaultTransactionIsolation" value="${datasource.defaultTransactionIsolation}" />
        <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${datasource.minEvictableIdleTimeMillis}" />
        <property name="timeBetweenLogStatsMillis" value="${datasource.timeBetweenLogStatsMillis}" />
        <property name="removeAbandoned" value="${datasource.druid.remove.abandoned}" />
        <property name="removeAbandonedTimeout" value="${datasource.druid.remove.abandoned.timeout}" />
        <property name="logAbandoned" value="${datasource.druid.log.abandoned}" />
        <property name="filters" value="${datasource.filters}"/>
        <property name="connectProperties">
		    <props>
		        <prop key="connectTimeout">${datasource.connectProperties.connectTimeout}</prop>
		        <prop key="socketTimeout">${datasource.connectProperties.socketTimeout}</prop>
		    </props>
		</property>
    </bean>

    <bean id="baseAdminDruidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="maxActive" value="${datasource.admin.maxActive}" />
        <property name="initialSize" value="${datasource.admin.initialSize}" />
        <property name="minIdle" value="${datasource.admin.minIdle}" />
        <property name="maxWait" value="${datasource.maxWait}" />
        <property name="testOnBorrow" value="${datasource.testOnBorrow}" />
        <property name="defaultTransactionIsolation" value="${datasource.defaultTransactionIsolation}" />
        <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${datasource.minEvictableIdleTimeMillis}" />
        <property name="timeBetweenLogStatsMillis" value="${datasource.timeBetweenLogStatsMillis}" />
        <property name="removeAbandoned" value="${datasource.druid.remove.abandoned}" />
        <property name="removeAbandonedTimeout" value="${datasource.druid.remove.abandoned.timeout}" />
        <property name="logAbandoned" value="${datasource.druid.log.abandoned}" />
        <property name="filters" value="${datasource.filters}"/>
        <property name="connectProperties">
            <props>
                <prop key="connectTimeout">${datasource.connectProperties.connectTimeout}</prop>
                <prop key="socketTimeout">${datasource.connectProperties.socketTimeout}</prop>
            </props>
        </property>
    </bean>

    <!-- admin datasource -->
    <bean id="adminDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseAdminDruidDataSource">
        <property name="name" value="adminDataSource" />
        <property name="username" value="${admin.datasource.username}" />
        <property name="password" value="${admin.datasource.pwd}" />
        <property name="url" value="${admin.datasource.url}" />
    </bean>
    <bean id="adminSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adminDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/admin/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.admin"/>
        <property name="sqlSessionFactoryBeanName" value="adminSqlSessionFactory"/>
    </bean>

    <!-- cd datasource -->
    <bean id="cdDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="cdDataSource" />
        <property name="username" value="${cd.datasource.username}" />
        <property name="password" value="${cd.datasource.pwd}" />
        <property name="url" value="${cd.datasource.url}" />
    </bean>
    <bean id="cdSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="cdDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/cd/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.creditDriver"/>
        <property name="sqlSessionFactoryBeanName" value="cdSqlSessionFactory"/>
    </bean>

    <!-- stat datasource -->
    <bean id="statDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="statDataSource" />
        <property name="username" value="${stat.datasource.username}" />
        <property name="password" value="${stat.datasource.pwd}" />
        <property name="url" value="${stat.datasource.url}" />
        <property name="connectProperties">
            <props>
                <prop key="connectTimeout">5000</prop>
                <prop key="socketTimeout">600000</prop>
            </props>
        </property>
    </bean>
    <bean id="statSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="statDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/stat/engine/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.stat"/>
        <property name="sqlSessionFactoryBeanName" value="statSqlSessionFactory"/>
    </bean>

    <!-- stat datasource -->
    <bean id="haohuanDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="statDataSource" />
        <property name="username" value="${haohuan.datasource.username}" />
        <property name="password" value="${haohuan.datasource.pwd}" />
        <property name="url" value="${haohuan.datasource.url}" />
        <property name="connectProperties">
            <props>
                <prop key="connectTimeout">5000</prop>
                <prop key="socketTimeout">600000</prop>
            </props>
        </property>
    </bean>
    <bean id="haohuanSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="haohuanDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/haohuan/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model.haohuan"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.haohuan"/>
        <property name="sqlSessionFactoryBeanName" value="haohuanSqlSessionFactory"/>
    </bean>

</beans>
