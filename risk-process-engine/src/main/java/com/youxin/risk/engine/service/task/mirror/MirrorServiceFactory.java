package com.youxin.risk.engine.service.task.mirror;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 镜像服务工厂类
 * 负责创建和管理不同类型的镜像服务实现
 *
 * <AUTHOR>
 */
@Component
public class MirrorServiceFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(MirrorServiceFactory.class);
    
    /**
     * 镜像服务映射表
     * 存储不同类型的镜像服务实现实例
     */
    private final Map<MirrorService.MirrorType, MirrorService> mirrorServiceMap = new HashMap<>();

    /**
     * 构造函数
     * 通过Spring自动注入所有MirrorService实现，并按类型存储到映射表中
     *
     * @param mirrorServices 所有MirrorService实现的列表
     */
    @Autowired
    public MirrorServiceFactory(List<MirrorService> mirrorServices) {
        LoggerProxy.info("镜像服务", LOGGER, "初始化镜像服务工厂，发现{}个镜像服务实现", mirrorServices.size());
        
        // 将所有镜像服务实现按类型存储到映射表中
        mirrorServices.forEach(mirrorService -> {
            MirrorService.MirrorType mirrorType = mirrorService.getMirrorType();
            mirrorServiceMap.put(mirrorType, mirrorService);
            LoggerProxy.info("镜像服务", LOGGER, "注册镜像服务：类型={}, 实现类={}", 
                    mirrorType, mirrorService.getClass().getSimpleName());
        });
    }

    /**
     * 获取指定类型的镜像服务实现
     *
     * @param mirrorType 镜像类型
     * @return 对应类型的镜像服务实现
     * @throws IllegalArgumentException 如果不存在指定类型的镜像服务实现
     */
    public MirrorService getMirrorService(MirrorService.MirrorType mirrorType) {
        if (!mirrorServiceMap.containsKey(mirrorType)) {
            String errorMsg = String.format("不存在该镜像类型: %s", mirrorType.name());
            LoggerProxy.error("镜像服务", LOGGER, errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
        return mirrorServiceMap.get(mirrorType);
    }
}
