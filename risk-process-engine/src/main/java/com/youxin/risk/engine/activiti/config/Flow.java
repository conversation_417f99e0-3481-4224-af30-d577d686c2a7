package com.youxin.risk.engine.activiti.config;

import com.youxin.risk.commons.utils.Assert0;
import com.youxin.risk.engine.activiti.config.node.Node;
import com.youxin.risk.engine.activiti.service.Command;

public class Flow extends Config {

    private Node source;
    private Node target;

    private String conditionExpression;

    public String getConditionExpression() {
        return conditionExpression;
    }

    public void setConditionExpression(String conditionExpression) {
        this.conditionExpression = conditionExpression;
    }

    public Node getSource() {
        return source;
    }

    public void setSource(Node source) {
        Assert0.notNull(source);
        this.source = source;
    }

    public Node getTarget() {
        return target;
    }

    public void setTarget(Node target) {
        Assert0.notNull(target);
        this.target = target;
    }

    @Override
    public Command getCommand() {
        return null;
    }
}