package com.youxin.risk.engine.service.task.pudao;

import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 朴道随机分流
 */
public class HhRandomPudaoTaskService extends AbstractTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HhRandomPudaoTaskService.class);
    public static final String PUDAO_FL = "isPuDaoFl";
    public static final String PUDAO_FL_SPLIT_FLOW = "pudao.fl.split.flow";

    /**
     * 小于分流走朴道假策略
     * @param processContext
     */
    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        event.set(PUDAO_FL, false);

        int flSplitFlow = getSplitFlow(PUDAO_FL_SPLIT_FLOW);
        int random = Math.abs(event.getUserKey().hashCode()) % 100;
        LoggerProxy.info(LOGGER, "HhRandomPudaoTaskService_param, random = {},splitFlow={}, loanKey={} userKey = {}",
                random, flSplitFlow, event.getLoanKey(),event.getUserKey());
        if (random <flSplitFlow ){
            LoggerProxy.info(LOGGER, "HhRandomPudaoTaskService_execute, splitFlow={}, loanKey={}", flSplitFlow, event.getLoanKey());
            event.set(PUDAO_FL, true);
        }
    }

    /**
     * 流量
     * @param key
     * @return
     */
    private int getSplitFlow(String key) {
        try {
            return ApolloClientAdapter.getIntConfig(ApolloNamespaceEnum.ENGINE_SPACE, key, 0);
        } catch (Exception e) {
            LoggerProxy.error("HhRandomPudaoTaskService_getSplitFlowError", LOGGER, "", e);
        }
        return 0;
    }
}
