package com.youxin.risk.engine.service.task.data.filter;

import com.youxin.risk.commons.constants.SourceSystemEnum;
import com.youxin.risk.commons.model.Event;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2023/10/27 11:21
 * @desc
 */
@Service
public class DataFilterHandler {
    @Resource
    private CommonDataFilter commonDataFilter;
    @Resource
    private HaoHuanDataFilter haoHuanDataFilter;
    @Resource
    private RenRenDaiDataFilter renRenDaiDataFilter;

    public void filter(Event event) {
        // 补全参数
        if (SourceSystemEnum.HAO_HUAN.name().equals(event.getSourceSystem())) {
            haoHuanDataFilter.filter(event);
        } else if (SourceSystemEnum.REN_REN_DAI.name().equals(event.getSourceSystem())) {
            renRenDaiDataFilter.filter(event);
        } else {
            commonDataFilter.filter(event);
        }
    }
}
