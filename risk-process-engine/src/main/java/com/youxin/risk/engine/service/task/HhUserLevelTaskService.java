package com.youxin.risk.engine.service.task;

import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.service.engine.EventService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.clients.RiskDcClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

public class HhUserLevelTaskService extends AbstractTaskService {

    private static final Logger logger = LoggerFactory.getLogger(BlankTaskService.class);

    @Resource
    private RiskDcClient riskDcClient;
    @Resource
    private EventService eventService;

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        Map<String, Object> userLevel = riskDcClient.getUserLevel(event.getUserKey());
        if (userLevel != null) {
            event.getDataVo().putAll(userLevel);
        }
        appendDealInfo(event);
        // 拼入原始上游入参信息
        event.getDataVo().put("originInput", event.getParams());
//        eventService.updateEventToMongo(event);
    }

    /**
     * 放款审核（loan_audit）拼接本次交易信息
     * @param event
     */
    private void appendDealInfo(Event event){
        if(event == null){
            return ;
        }
        Map<String, Object> dataVO = event.getDataVo();
        //（额度、期限、费率，类型（例，好借/好买））
        Map<String, Object> params = event.getParams();
        if(params != null && dataVO  != null){
            Map<String, Object> loanCurByUserKeyService = new HashMap<String, Object>();
            String amountString = (String)params.get("amount");
            if(StringUtils.isNotBlank(amountString)){
                loanCurByUserKeyService.put("amount", amountString.trim());
            }
            String periodsString = (String)params.get("periods");
            if(StringUtils.isNotBlank(periodsString)){
                try{
                    int periods = Integer.parseInt(periodsString.trim());
                    loanCurByUserKeyService.put("periods",periods);
                }catch(Exception e){
                    LoggerProxy.error(logger,"入参期数periods格式非法:"+ periodsString, e);
                }
            }
            String rateString = (String)params.get("rate");
            if(StringUtils.isNotBlank(rateString)){
                loanCurByUserKeyService.put("rate",rateString.trim());
            }
            String loanType = (String)params.get("loanType");
            if(StringUtils.isNotBlank(loanType)){
                loanCurByUserKeyService.put("loanType",loanType.trim());
            }
            if(!loanCurByUserKeyService.isEmpty()){
                dataVO.put("loanCurByUserKeyService",loanCurByUserKeyService);
            }
        }
    }
}
