package com.youxin.risk.engine.service.task;

import com.google.common.collect.Maps;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.utils.EventUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/3 17:39
 */
public class SendEventTaskService extends AbstractTaskService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SendEventTaskService.class);

    @Resource(name = "eventMessageSender")
    private KafkaSyncSender eventMessageSender;
    @Autowired
    private EventService eventService;
    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        String message = JacksonUtil.toJson(event);
        try {
            eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, eventMessageSender, message);
            log(event);
        } catch (Exception e) {
            // 发送失败打异常日志，不阻塞流程
            LoggerProxy.error("sendEventMessageError", LOGGER, "message=" + message, e);
        }
    }

    private void log(Event event) {
        try {
            Map<String, Object> params = event.getParams();
            String occurTime = (String) params.get("occurTime");
            if (StringUtils.isNotBlank(occurTime)) {
                Date createTime = DateUtil.parse(occurTime, "yyyyMMddHHmmss");
                Map<String, Number> values = Maps.newHashMap();
                values.put("cost", System.currentTimeMillis() - createTime.getTime());
                LoggerProxy.info("sendEventTaskLog", LOGGER, "process duration info, eventCode={},step={},values={}", event.getEventCode(), event.getParams().get("step") == null? "NONE" : (String) event.getParams().get("step"), values);
            }
        } catch (Exception e) {
            LoggerProxy.error("sendEventTaskLogError", LOGGER, "eventCode={}", event.getEventCode(), e);
        }
    }
}
