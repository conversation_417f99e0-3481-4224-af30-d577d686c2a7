package com.youxin.risk.engine.service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;

/**
 * 自定义网关请求消息
 * <AUTHOR>
 */
public interface GwMsgCustomHandler {

    /**
     * 事件名称
     * @return
     */
    String getEventCode();

    /**
     * 构建请求事件的入参
     * @param event
     * @param targetEventCode
     * @return
     */
    JSONObject buildGwMsg(Event event, String targetEventCode);

}
