package com.youxin.risk.engine.service;

import com.youxin.risk.commons.dao.engine.VariableAsyncRequestLogMapper;
import com.youxin.risk.commons.model.VariableAsyncRequestLog;
import com.youxin.risk.engine.service.context.VariableExecutionContext;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 变量请求日志服务
 * 负责处理变量请求相关的日志记录、查询和状态更新
 *
 * <AUTHOR>
 * @create 2023/12/20
 */
@Service
public class VariableRequestLogService {
    private final static Logger LOGGER = LoggerFactory.getLogger(VariableRequestLogService.class);

    @Resource
    private VariableAsyncRequestLogMapper variableAsyncRequestLogMapper;



    /**
     * 创建并保存变量请求日志
     */
    public List<VariableAsyncRequestLog> createAndSaveRequestLogs(VariableExecutionContext context) {
        List<VariableAsyncRequestLog> requestLogs = createVariableAsyncRequestLogs(context);
        if (CollectionUtils.isNotEmpty(requestLogs)) {
            variableAsyncRequestLogMapper.insertBatch(requestLogs);
        }
        return requestLogs;
    }

    private List<VariableAsyncRequestLog> createVariableAsyncRequestLogs(VariableExecutionContext context) {
        List<VariableAsyncRequestLog> variableAsyncRequestLogs = new ArrayList<>();
        for (String prefetchVariable : context.getPrefetchVariables()) {
            VariableAsyncRequestLog variableAsyncRequestLog = VariableAsyncRequestLog.init(context.getRequestId(),
                    context.getLoanKey(), context.getSessionId(), context.getUserKey(), prefetchVariable,
                    context.getCurrentNodeId(),
                    context.getStrategyNodeId(),
                    context.varIsBlocking(prefetchVariable));

            variableAsyncRequestLogs.add(variableAsyncRequestLog);
        }
        return variableAsyncRequestLogs;
    }


    /**
     * 获取已获取的变量列表
     */
    public List<String> getVariableCodes(String loanKey, String nodeId) {
        List<VariableAsyncRequestLog> variableAsyncRequestLogList =
                variableAsyncRequestLogMapper.selectAllByLoanKeyAndNodeId(loanKey, nodeId);

        return variableAsyncRequestLogList.stream()
                .map(VariableAsyncRequestLog::getVariableCode)
                .collect(Collectors.toList());
    }

    /**
     * 设置重试状态
     */
    public void updateStatusToRetry(List<VariableAsyncRequestLog> variableAsyncRequestLogs, String retryMessage) {
        if (CollectionUtils.isEmpty(variableAsyncRequestLogs)) {
            return;
        }
        for (VariableAsyncRequestLog variableAsyncRequestLog : variableAsyncRequestLogs) {
            variableAsyncRequestLog.setRetry(retryMessage);
        }
        variableAsyncRequestLogMapper.updateBatch(variableAsyncRequestLogs);
    }

    /**
     * 从日志中提取变量代码列表
     */
    public <T extends VariableAsyncRequestLog> List<String> extractVariableCodes(List<T> variableAsyncRequestLogs) {
        if (CollectionUtils.isEmpty(variableAsyncRequestLogs)) {
            return new ArrayList<>();
        }
        return variableAsyncRequestLogs.stream()
                .map(VariableAsyncRequestLog::getVariableCode)
                .collect(Collectors.toList());
    }

}