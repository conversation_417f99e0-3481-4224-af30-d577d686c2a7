package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.util.TypeUtils;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.engine.activiti.service.impl.DefaultUserTaskService;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.service.engine.EventService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.clients.RmClient;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.ra.utils.RaXmlUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
public class SerialFeatureTaskService extends AbstractTaskService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SerialFeatureTaskService.class);

    @Resource
    private EventService eventService;
    @Resource
    private RmClient rmClient;

    @Override
    public void execute(ProcessContext processContext) {
        LoggerProxy.info("beginSerialFeature", LOGGER, "");
        // 根据串行节点featureName,查询三方属性,组织子dataVo
        Event event = processContext.getEvent();
        event.setCurrentStep(processContext.getAttribute("step"));
        // defalut flag is true
        Boolean hasStrategyResult = TypeUtils.castToBoolean(processContext.getAttribute("hasStrategyResult"));
        hasStrategyResult = null == hasStrategyResult ? true : hasStrategyResult;
        event.set("hasStrategyResult", hasStrategyResult);
        String featureName = processContext.getAttribute("featureName");
        if (StringUtils.isEmpty(featureName)){
            throw new RiskRuntimeException("featureName is null, sessionId=" + event.getSessionId());
        }
        event.set("featureName", featureName);
        // default rm get feature
        String xml = event.getXml();
        JSONObject serialFsResult = rmClient.getSerialFeatureXml(event);
        String rmXml = serialFsResult.getString("xml");
        xml = RaXmlUtils.mergeXml(xml, rmXml);
        point(event.getEventCode(), xml);
        // check xml存储路径
		String savePath = processContext.getAttribute(DefaultUserTaskService.SAVE_PATH);
        if (StringUtils.isEmpty(savePath)) {
			event.setXml(xml);
		} else {
        	saveXmlPointPath(event, xml, savePath);
		}
		// save serial verify result
        if (hasStrategyResult != null && true == hasStrategyResult) {
            event.setSerialVerifyResult((Map<String, Object>) serialFsResult.get("result"));
        }
		// update event
//        eventService.updateEventToMongo(event);
    }

	private void saveXmlPointPath(Event event, String xml, String savePath) {
		try {
			// 自定义xml存储路径为event.verifyResult顶级目录开始, 不存在则创建
			JSONPath.set(event.getVerifyResult(), savePath, xml);
		} catch (Exception e) {
			LoggerProxy.error("saveXmlError", LOGGER, String.format("savePath=%s,xml=%s", savePath, xml), e);
		}
	}

    private void point(String eventCode, String xml) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put("eventCode", eventCode);
            Map<String, Number> values = new HashMap<>();
            int value;
            if (StringUtils.isNotBlank(xml)) {
                value = 1;
            } else {
                value = 0;
            }
            values.put("valueFlag", value);
            MetricsAPI.point(PointConstant.ra_datavo_feature, tags, values);
        } catch (Exception e) {
            LoggerProxy.error("pointException", LOGGER, "eventCode={}", eventCode, e);
        }
    }
}
