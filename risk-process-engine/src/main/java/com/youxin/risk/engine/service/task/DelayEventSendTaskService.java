package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.engine.service.GwMsgCustomHandler;
import com.youxin.risk.engine.service.impl.GwMsgCustomProxy;
import com.youxin.risk.engine.utils.EventUtils;
import com.youxin.risk.commons.model.engine.DelayEventMessageLog;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 延迟事件发送节点
 * <AUTHOR>
 */
@Service
public class DelayEventSendTaskService extends AbstractTaskService {

    private static final Logger logger = LoggerFactory.getLogger(DelayEventSendTaskService.class);

    @Resource
    private GwMsgCustomProxy gwMsgCustomProxy;

    @Resource
    @Qualifier("retryableJedis")
    private RetryableJedis redisService;

    @Autowired
    private EventService eventService;

    @Resource(name = "delayEventMessageLogSender")
    private KafkaSyncSender delayEventMessageLogSender;

    public static final int LOCK_TIME_OUT = 60 * 24;

    @Override
    public void execute(ProcessContext processContext) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        String targetIcode = processContext.getAttribute("targetIcode");
        String targetEventCode = processContext.getAttribute("targetEventCode");
        String delayTimeMin = processContext.getAttribute("delayTimeMin");

        Event event = processContext.getEvent();
        GwMsgCustomHandler gwMsgCustomHandler = gwMsgCustomProxy.getGwMsgCustomHandler(targetEventCode);
        JSONObject gwMsg = gwMsgCustomHandler.buildGwMsg(event, targetEventCode);

        String transId = event.getString("transId");
        // redis幂等,有效期一天,key transId+userKey, value 1是0否,保证卡单重试不会重复触发
        String redisKey = transId + event.getUserKey();
        String value = redisService.get(redisKey);
        if(StringUtils.isEmpty(value)){
            redisService.setex(redisKey, LOCK_TIME_OUT, "0");
        }

        if("1".equals(value)){
            LoggerProxy.info("DelayEventSendTaskService", logger,"current record exist, userKey={}", event.getUserKey());
            return;
        }
        // 延迟请求事件记录落库, 供定时任务捞起
        sendGwMsg(event, gwMsg, targetIcode, targetEventCode, delayTimeMin);
        redisService.setex(redisKey, LOCK_TIME_OUT,"1");
        LoggerProxy.info("DelayEventSendTaskService", logger, "record send kafka success, userKey={}", event.getUserKey());

        long costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        // 埋点
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sourceEventCode", event.getEventCode());
        tags.put("targetEventCode", targetEventCode);
        MetricsAPI.point(PointConstant.PE_CALL_DELAY_EVENT_SEND_POINT, tags, costTime, true, MetricsOpType.timer);
    }

    /**
     * 发送延迟触发事件记录到kafka
     * @param gwMsg
     * @param targetIcode
     * @param targetEventCode
     * @param delayTimeMin
     */
    private void sendGwMsg(Event event, JSONObject gwMsg, String targetIcode, String targetEventCode, String delayTimeMin){
        DelayEventMessageLog delayEventMessageLog = new DelayEventMessageLog();
        delayEventMessageLog.setUserKey(event.getUserKey());
        delayEventMessageLog.setSourceLoanKey(event.getLoanKey());
        delayEventMessageLog.setSourceEventCode(event.getEventCode());
        delayEventMessageLog.setTargetIcode(targetIcode);
        delayEventMessageLog.setTargetEventCode(targetEventCode);
        delayEventMessageLog.setGwMessage(JSON.toJSONString(gwMsg));
        delayEventMessageLog.setTriggerTime(new Date(System.currentTimeMillis() + Long.parseLong(delayTimeMin)*60*1000));
        delayEventMessageLog.setCreateTime(new Date());

        eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, delayEventMessageLogSender, JSON.toJSONString(delayEventMessageLog));
    }
}