package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.DataVoUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * irr36和irr24用户打标
 */
public class HhVerifyLatestUserLineTaskService extends AbstractTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HhVerifyLatestUserLineTaskService.class);

    private static final String[] IRR36_KEYS = {"IRR36_VERIFY_AMOUNT", "AMOUNT_ASSIGN_IRR_ALL_FL_36"};
    private static final String[] IRR24_KEYS = {"IRR24_VERIFY_AMOUNT", "AMOUNT_ASSIGN_IRR_ALL_FL"};

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        Map<String, Object> dataVo = event.getDataVo();
        Object thirdPartyData = dataVo.get(DataVoUtils.getThirdPartyDataKey(event.getSourceSystem()));
        if (thirdPartyData == null) {
            throw new RuntimeException("第三方数据为空！");
        }
        JSONObject dataSource = JSONObject.parseObject(JSON.toJSONString(thirdPartyData))
                .getJSONObject("verifyLastestUserLineService");

        if (containsAnyKey(dataSource, IRR36_KEYS)) {
            event.getDataVo().put("isIrr36", true);
        } else if (containsAnyKey(dataSource, IRR24_KEYS)) {
            event.getDataVo().put("isIrr36", false);
        } else {
            throw new RuntimeException("额度数据获取异常！");
        }
        LoggerProxy.info("HhVerifyLatestUserLineService", LOGGER, "isIrr36: {}", event.getDataVo().get("isIrr36"));
    }

    private boolean containsAnyKey(JSONObject jsonObject, String[] keys) {
        for (String key : keys) {
            if (jsonObject.containsKey(key)) {
                return true;
            }
        }
        return false;
    }
}