package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.UUID;

/**
 * 进件测试定制节点(进件审核流程结束自动触发进件审核测试流程)
 */
@Service
public class HaohuanVerifyTestCheckTaskService extends AbstractTaskService {

    private static final Logger logger = LoggerFactory.getLogger(HaohuanVerifyTestCheckTaskService.class);

    @Value("${risk.gateway.url}")
    private String gatewayUrl;

    @Override
    public void execute(ProcessContext processContext) {
        String icode = processContext.getAttribute("icode");
        Event event = processContext.getEvent();
        callGateway(event.getUserKey(), icode, event);
    }

    private void callGateway(String userKey,String icode,Event event) {
        try {
            JSONObject gwMsg = new JSONObject();
            logger.info("callGateway.event:{}", JSONObject.toJSONString(event.getParams()));
            gwMsg.putAll(event.getParams());
            gwMsg.put("eventCode", "haohuanVerifyTest");
            gwMsg.put("hasNotSettled", Boolean.parseBoolean(gwMsg.get("hasNotSettled").toString()));
            gwMsg.put("isMaxOverdueGt10d", Boolean.parseBoolean(gwMsg.get("isMaxOverdueGt10d").toString()));
            gwMsg.put("isOverdueGt1d", Boolean.parseBoolean(gwMsg.get("isOverdueGt1d").toString()));
            gwMsg.put("isSumOverdueGt3t", Boolean.parseBoolean(gwMsg.get("isSumOverdueGt3t").toString()));
            gwMsg.remove("loanKey");

            JSONObject gwData = new JSONObject();
            gwData.put("type", "async");
            gwData.put("requestId", UUID.randomUUID().toString());
            gwData.put("message", gwMsg);

            HashMap<String, String> request = new HashMap<>();
            request.put("icode", icode);
            request.put("data", gwData.toJSONString());

            String result = SyncHTTPRemoteAPI.post(gatewayUrl + "/risk/api/analyse", request, 10000);
            JSONObject resultJson = JSON.parseObject(result);
            JSONObject data = resultJson.getJSONObject("data");
            String retCode = data.getString("retCode");

            if (!RetCodeEnum.SUCCESS.equals(retCode)) {
                logger.error("haohuanVerifyTest error,gateway response:{}", result);
            } else {
                logger.info("haohuanVerifyTest success,request={},resp={}",
                        JSONObject.toJSONString(request), result);
            }
        } catch (Exception e) {
            logger.error("haohuanVerifyTest error, e:", e);
        }
    }
}