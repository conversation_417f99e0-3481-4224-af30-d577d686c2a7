package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.DataVoUtils;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调用交易审核反欺诈数据
 *
 * <AUTHOR>
 * @date 2019-01-03
 */
@Service
public class HHAntifraudTransDataHandler extends ConfigurableDataHandler {

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, String> dataVo = (Map<String, String>) event.getDataVo().get(DataVoUtils.getThirdPartyDataKey(event.getSourceSystem()));
        JSONObject submitIdCard = JSON.parseObject(dataVo.get("findSubmitIdCardByUserKey"));
        JSONObject lastUserLine = JSON.parseObject(dataVo.get("userLineService"));
        JSONObject dataFromVerify = JSON.parseObject(dataVo.get("verifyTransactionService"));


        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        if(null != submitIdCard) {
            params.put("id_card_no", submitIdCard.get("idcardNumber"));
        }
        params.put("order_id", event.get("orderId"));
        params.put("order_time", event.get("orderTime"));
        params.put("ip", event.get("ip"));
        params.put("wifi_mac", event.get("wifiMac"));
        params.put("app_list", event.get("appList"));
        params.put("mobile", event.get("receiverMobile"));
        params.put("serviceVersion", "1.0");
        params.put("requestTime", System.currentTimeMillis());
        params.put("partnerUserId", event.getUserKey());
        params.put("partner", "HAO_HUAN");
        params.put("GPS", ImmutableMap.<String, Object>builder()
                .put("lon", event.get("longitude"))
                .put("lat", event.get("latitude"))
                .build());
        params.put("td_fingerprint", event.get("tdFingerprint"));
        params.put("address", event.get("receiverAddress"));

        params.put("receiver", ImmutableMap.<String, Object>builder()
                .put("address", event.get("receiverAddress"))
                .put("name", event.get("receiverName"))
                .put("mobile", event.get("receiverMobile"))
                .build());
        List<Object> transaction = Lists.newArrayList();
        List products = (List) event.get("transaction");
        for (Object product : products) {
            Map<String,Object> productJsonObject = (Map<String,Object>) product;
            HashMap<Object, Object> productParams = Maps.newHashMap();
            productParams.put("product_type", productJsonObject.get("productType"));
            productParams.put("product_name", productJsonObject.get("productName"));
            productParams.put("product_branch", productJsonObject.get("productBranch"));
            productParams.put("product_price", productJsonObject.get("productPrice"));
            transaction.add(productParams);
        }
        params.put("transaction", transaction);
        if(null != lastUserLine) {
            params.put("haomai_surplus", lastUserLine.get("shopActualLine"));
        }

        if(dataFromVerify != null){
            JSONObject verifyTransData = (JSONObject) dataFromVerify.getJSONObject("data");
            if(verifyTransData != null){
                JSONObject verifyTrans = (JSONObject) verifyTransData.get("verifyTrans");
                if(null != verifyTrans) {
                    params.put("last_transaction_result", verifyTrans.getBooleanValue("isFinalPassed") ? 1 : -1);
                }
            }

        }

        params.put("user_actions", event.get("userActions"));
        params.put("register_mobile", event.get("registerMobile"));
        params.put("bank_mobile", event.get("bankMobile"));
        params.put("emergency_contact", event.get("emergencyContact"));

        HashMap<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("dataObject",params);
        paramMap.put("sourceSystem",event.get("sourceSystem"));
        paramMap.put("system_id",event.get("sourceSystem"));
        paramMap.put("userKey",event.get("userKey"));

        return paramMap;
    }

    public  static void main(String[] ag){

        HashMap<Object, Object> map = Maps.newHashMap();
        ArrayList<Object> arrays = Lists.newArrayList();
        map.put("list",arrays
        );
        arrays.add("str");
        try{
            JSONArray products = (JSONArray) map.get("list");
            System.out.println(products.get(0).toString());
        }catch (Exception e){
            e.printStackTrace();
        }




    }
}
