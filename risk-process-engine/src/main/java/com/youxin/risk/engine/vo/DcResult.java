package com.youxin.risk.engine.vo;

import java.util.Map;

import com.youxin.risk.commons.constants.RetCodeEnum;

public class DcResult {

    public DcResult(RetCodeEnum retCode) {
        this.retCode = retCode;
    }

    public DcResult(RetCodeEnum retCode, Map<String, Object> dataMap) {
        this.retCode = retCode;
        this.dataMap = dataMap;
    }

    private RetCodeEnum retCode;

    private Map<String, Object> dataMap;

    public RetCodeEnum getRetCode() {
        return retCode;
    }

    public void setRetCode(RetCodeEnum retCode) {
        this.retCode = retCode;
    }

    public Map<String, Object> getDataMap() {
        return dataMap;
    }

    public void setDataMap(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }
}
