package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.DCServiceCodeEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class HHBairongLimitConsumptionHandler extends ConfigurableDataHandler {


    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        params.put("systemId", event.getSourceSystem());
        JSONObject idcard = JSON.parseObject((String) event.getDataVo().get(DCServiceCodeEnum.ID_CARD.name()));
        if (idcard != null) {
            params.put("idCardNo", idcard.get("idcardNumber"));
            params.put("name", idcard.get("idcardName"));
        }

        JSONObject mobile = JSON.parseObject((String) event.getDataVo().get(DCServiceCodeEnum.REGISTER.name()));
        if (mobile != null) {
            params.put("mobile", mobile.get("mobile"));
        }

        return params;
    }

}
