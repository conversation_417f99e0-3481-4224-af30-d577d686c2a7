package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.vo.datavo.SubmitAddressVo;
import com.youxin.risk.engine.service.task.data.BaseDataHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 设置婚姻状态
 *
 * <AUTHOR>
 * @date 2024/08/06 14:36
 */
@Service
public class SSJKMarriageServiceHandler extends BaseDataHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(SSJKMarriageServiceHandler.class);

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);

        try {
            Map<String, Object> dataVo = event.getDataVo();
            // submitAddressVo
            SubmitAddressVo submitAddressVo = JSONObject.parseObject(JSONObject.toJSONString(dataVo.get("submitAddressVo")), SubmitAddressVo.class);
            if (submitAddressVo != null) {
                // 设置婚姻状态
                params.put("marriage", submitAddressVo.getMarriage());
            }
        } catch (Exception e) {
            LOGGER.error("get submitAddressVo params error: loanKey: {}", event.getLoanKey(), e);
        }
        return params;
    }


}
