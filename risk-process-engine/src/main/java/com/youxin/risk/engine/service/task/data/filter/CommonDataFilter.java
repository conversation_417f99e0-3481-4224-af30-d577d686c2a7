package com.youxin.risk.engine.service.task.data.filter;

import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.GlobalUtil;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/25 16:24
 */
@Service
public class CommonDataFilter {
    public void filter(Event event) {
        Map<String, Object> dataInput = Maps.newHashMap();
        dataInput.putAll(event.getParams());
        dataInput.put("idno", event.getString("idcardNumber"));
        dataInput.put("name", event.getString("idcardName"));
        dataInput.put("systemId", event.getSourceSystem());
        dataInput.put("bankNo", event.getString("bankcardNo"));
        dataInput.put("username", event.getString("idcardName"));
        dataInput.put("unionpayOrderId", GlobalUtil.getGlobalId());
        event.set("dataInput", dataInput);
    }
}
