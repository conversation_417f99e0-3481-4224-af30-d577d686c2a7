package com.youxin.risk.engine.service.task.data.haohuan;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.vo.DataVo;
import com.youxin.risk.commons.vo.datavo.SubmitAddressVo;
import com.youxin.risk.commons.vo.datavo.SubmitIdcardVo;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * deviceFeaturesService参数
 * <AUTHOR>
 * @since 2022/1/5 17:33
 */
@Service
public class HHDeviceFeatureHandler extends ConfigurableDataHandler {
    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = Maps.newHashMap();
        DataVo dataVo = JSONObject.parseObject(JSONObject.toJSONString(event.getDataVo()), DataVo.class);
        SubmitIdcardVo submitIdcardVo = dataVo.getSubmitIdcardVo();
        SubmitAddressVo submitAddressVo = dataVo.getSubmitAddressVo();
        params.put("userKey", event.getUserKey());
        params.put("sourceSystem", event.getSourceSystem());
        params.put("systemid", event.getSourceSystem());
        if(Objects.nonNull(submitIdcardVo)) {
            params.put("idCardNo", submitIdcardVo.getIdcardNumber());
            params.put("idCard", submitIdcardVo.getIdcardNumber());
        }
        if(Objects.nonNull(submitAddressVo)) {
            params.put("address", submitAddressVo.getCity());
            params.put("city", submitAddressVo.getCity());
        }

        this.buildAcquisitionTime(nodeCode,dataCode,event,params);
        return params;
    }
}
