package com.youxin.risk.engine.service.task.data.haohuan;

import com.google.common.collect.ImmutableMap;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-01-04
 */
@Service
public class HHSubmitIdCardDataHandler extends ConfigurableDataHandler {

    @Override
    public Map<String, Object> buildRequestParams(Event event, String dataCode, String nodeCode) {
        Map<String, Object> params = super.buildRequestParams(event, dataCode, nodeCode);
        params.put("serviceCode", dataCode);
        params.put("params", ImmutableMap.builder()
                .put("userKey", event.getUserKey())
                .build());
        return params;
    }
}
