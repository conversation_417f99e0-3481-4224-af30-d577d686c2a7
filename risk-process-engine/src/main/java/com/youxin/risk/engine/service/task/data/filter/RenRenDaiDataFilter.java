package com.youxin.risk.engine.service.task.data.filter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.RRDDataVoUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/19 14:22
 */
@Service
public class RenRenDaiDataFilter {
    public void filter(Event event) {
        if (event.containsKey("dataInput")) {
            return;
        }
        Map<String, Object> dataInput = Maps.newHashMap();
        dataInput.putAll(event.getParams());
        dataInput.put("systemId", event.getSourceSystem());
        dataInput.put("idType", "0");
        dataInput.put("isValid", true);
        if (event.containsKey("userBasicInfo")) {
            JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
            dataInput.put("username", userBasicInfo.getString("realName"));
            dataInput.put("name", userBasicInfo.getString("realName"));
            dataInput.put("idno", userBasicInfo.getString("idNo"));
            dataInput.put("idCard", userBasicInfo.getString("idNo"));
            dataInput.put("idCardNo", userBasicInfo.getString("idNo"));
            dataInput.put("mobile", userBasicInfo.getString("mobile"));
            dataInput.put("bankNo", userBasicInfo.getString("cardNo"));
            dataInput.put("bankcard", userBasicInfo.getString("cardNo"));
            dataInput.put("userKey", userBasicInfo.getString("userKey"));
        }
        event.set("dataInput", dataInput);
    }
}
