package com.youxin.risk.engine.service.task.data;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.NodeDataParam;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.utils.CacheUtil;
import org.mvel2.MVEL;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.youxin.risk.engine.utils.CacheUtil.*;

/**
 * <AUTHOR>
 * @date 2019-04-23
 */
@Service
public class ResolveParamService {

    public Map<String, Object> resolveParams(Event event, String dataCode, String nodeId) {
        Map<String, Object> resolvedParams = Maps.newHashMap();

        //查询本地缓存，无缓存直接返回
        String cacheKey = getCacheKey(dataCode, nodeId);
        String data = CacheUtil.getCache(cacheKey);
        if (StringUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        List<NodeDataParam> nodeDataParams = JSONObject.parseArray(data, NodeDataParam.class);
        for (NodeDataParam nodeDataParam : nodeDataParams) {
            //数据源入参设置
            resolvedParams.put(nodeDataParam.getParamName(), resolveExpression(event, nodeDataParam.getExpression()));
        }
        return resolvedParams;
    }


    public static String getCacheKey(String dataCode, String nodeId) {
        return CACHE_UPDATE_NODE_DATA_PREFIX+ SEPARATION + dataCode + SEPARATION + nodeId;
    }

    public Object resolveExpression(Object context, String expression) {
        try {
            return MVEL.eval(expression, context);
        } catch (Exception e) {
            try {
                Map<String, Object> map = JSONObject.parseObject(JsonUtils.toJsonString(context));
                return MVEL.eval(expression, map);
            } catch (Exception e1) {
            }
            throw new RuntimeException("MVEL: " + expression, e);
        }
    }
}
