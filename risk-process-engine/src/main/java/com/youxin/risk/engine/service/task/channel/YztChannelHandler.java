/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.engine.service.task.channel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 壹账通
 */
@Service
public class YztChannelHandler extends YnxtChannelHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(YztChannelHandler.class);

    @Override
    public String getFundChannel() {
        return "YZT";
    }


}