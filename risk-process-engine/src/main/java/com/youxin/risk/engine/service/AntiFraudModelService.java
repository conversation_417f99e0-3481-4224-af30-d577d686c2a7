/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.engine.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.ra.constants.AntiFraudModelTaskStatus;
import com.youxin.risk.ra.constants.RiskProduct;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.ra.vo.AntiFraudModelResult;
import com.youxin.risk.ra.vo.AntiFraudModelResultVo;
import com.youxin.risk.ra.vo.ModelRequest;
import com.youxin.risk.ra.vo.ModelResponse;
import com.youxin.risk.ra.vo.ModelResultVo;

@Service
public class AntiFraudModelService {

	protected Logger LOGGER = LoggerFactory.getLogger(getClass());

	@Autowired
	private AntiFraudModelResultService antiFraudModelResultService;

	public List<AntiFraudModelResultVo> getAntiFraudModelResultList(
			String sourceSystem, List<String> versions,
			String userKey, String requestKey,
			String reportXml) {
		List<AntiFraudModelResultVo> result = new ArrayList<>();
		ModelRequest request = new ModelRequest();
		request.setLoan_id(requestKey);
		request.setFeature_xml(reportXml);
		List<String> businessLines = new ArrayList<>();
		for (String version : versions) {
			String line = this.getBusinessLine(sourceSystem, version);
			businessLines.add(line);
		}
		String businessLine = StringUtils.join(businessLines, "|");
		request.setBusiness_line(businessLine);
		request.setOther_info(null);
		ModelResponse respose = AntiFraudModelClient.invoke(request);
		//logger.error(respose.getResult());
		JSONArray jsonArray = null;
		if (respose != null && respose.getResult() != null) {
			try {
				jsonArray = JSON.parseArray(respose.getResult());
			} catch (Exception e) {
				LoggerProxy.error("paserModelResponseError", LOGGER,String.format("解析[%s]的反欺诈模型出错", respose.getLoan_id()),e);
			}
		}
		if (jsonArray == null || jsonArray.isEmpty()) {
			for (String version : versions) {
				result.add(this.saveFailInvokeResult(sourceSystem, version, userKey, requestKey));
			}
			return result;
		}

		Map<String, AntiFraudModelResultVo> map = new HashMap<String, AntiFraudModelResultVo>();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject jsonObject = jsonArray.getJSONObject(i);
			AntiFraudModelResultVo antiFraudModelResultVo = this.
					getAntiFraudModelResultVoFromJson(jsonObject, userKey, requestKey);
			if (antiFraudModelResultVo.getBusinessLine() != null) {
				map.put(antiFraudModelResultVo.getBusinessLine(), antiFraudModelResultVo);
			}
		}

		for (String version : versions) {
			AntiFraudModelResultVo vo = null;
			String line = this.getBusinessLine(sourceSystem, version);
			if (map.containsKey(line)) {
				vo = map.get(line);
				vo.setVersion(this.getNewVersion(version));
			} else {
				vo = new AntiFraudModelResultVo();
				vo.setBusinessLine(line);
				vo.setSuccess(false);
				vo.setTaskStatus(AntiFraudModelTaskStatus.MODEL_FAIL);
				vo.setVersion(this.getNewVersion(version));
			}
			this.saveAntiFraudModelResultFromVo(line, userKey, requestKey, vo);
			result.add(vo);
		}
		return result;
	}

	private void saveAntiFraudModelResultFromVo(String sourceSystem,
			String userKey, String requestKey,
			AntiFraudModelResultVo antiFraudModelResultVo) {
		AntiFraudModelResult antiFraudModelResult= new AntiFraudModelResult();
		antiFraudModelResult.setSourceSystem(sourceSystem);
		antiFraudModelResult.setUserKey(userKey);
		antiFraudModelResult.setRequestKey(requestKey);
		if (antiFraudModelResultVo != null) {
			antiFraudModelResult.setTaskStatus(antiFraudModelResultVo.getTaskStatus());
			ModelResultVo modelResultVo = antiFraudModelResultVo.getResult();
			if (modelResultVo != null) {
				antiFraudModelResult.setModelId(modelResultVo.getModelId());
				antiFraudModelResult.setProb(modelResultVo.getProb());
				antiFraudModelResult.setStatus(modelResultVo.getModelStatus());
				antiFraudModelResult.setResult(modelResultVo.getResult());
			}

		} else {
			antiFraudModelResult.setTaskStatus(AntiFraudModelTaskStatus.MODEL_FAIL);
		}
		this.antiFraudModelResultService.saveModelResult(antiFraudModelResult);
	}


	private AntiFraudModelResultVo getAntiFraudModelResultVoFromJson(JSONObject json,
			String userKey, String requestKey) {
		AntiFraudModelResultVo antiFraudModelResultVo = new AntiFraudModelResultVo();
		Integer status = null;
		ModelResultVo modelResultVo = new ModelResultVo();
		modelResultVo.setLoanId(requestKey);
		try {
			if (json != null) {
				modelResultVo.setResult(JSON.toJSONString(json));
				if (json.containsKey("model_status")) {
					status = json.getInteger("model_status");
					modelResultVo.setModelStatus(status);
					json.remove("model_status");
				}
				if (json.containsKey("model_id")) {
					modelResultVo.setModelId(json.getString("model_id"));
					json.remove("model_id");
				}
				if (json.containsKey("prob")) {
					modelResultVo.setProb(json.getDouble("prob"));
					json.remove("prob");
				}
				if (json.containsKey("business_line")) {
					antiFraudModelResultVo.setBusinessLine(json.getString("business_line"));
					json.remove("business_line");
				}
				try {
					modelResultVo.setModelFeature(JSON.toJSONString(json));
				} catch (Exception e) {
					LoggerProxy.error("checkModelFeatureFailed", LOGGER,String.format("[%s]的modelFeature为空或有错", requestKey),e);
				}
			}
		} catch (Exception e) {
			LoggerProxy.error("parseModelError", LOGGER,String.format("解析[%s]的反欺诈模型出错", requestKey),e);
		}
		if (status == null || status !=0) {
			antiFraudModelResultVo.setTaskStatus(AntiFraudModelTaskStatus.MODEL_FAIL);
			antiFraudModelResultVo.setSuccess(false);
		} else {
			antiFraudModelResultVo.setTaskStatus(AntiFraudModelTaskStatus.SUCCESS);
			antiFraudModelResultVo.setSuccess(true);
		}
		antiFraudModelResultVo.setResult(modelResultVo);
		return antiFraudModelResultVo;
	}

	private AntiFraudModelResultVo saveFailInvokeResult(String sourceSystem, String version,
			String userKey, String requestKey) {
		AntiFraudModelResult antiFraudModelResult = new AntiFraudModelResult();
		String businessLine = this.getBusinessLine(sourceSystem, version);
		antiFraudModelResult.setSourceSystem(businessLine);
		antiFraudModelResult.setUserKey(userKey);
		antiFraudModelResult.setRequestKey(requestKey);
		antiFraudModelResult.setTaskStatus(AntiFraudModelTaskStatus.INVOKE_FAIL);
		// 只留存mongodb
		this.antiFraudModelResultService.saveModelResult(antiFraudModelResult);

		AntiFraudModelResultVo antiFraudModelResultVo = new AntiFraudModelResultVo();
		antiFraudModelResultVo.setTaskStatus(AntiFraudModelTaskStatus.INVOKE_FAIL);
		antiFraudModelResultVo.setSuccess(false);
		antiFraudModelResultVo.setVersion(this.getNewVersion(version));
		return antiFraudModelResultVo;
	}

	private String getBusinessLine(String sourceSystem, String version) {
		RiskProduct product = RiskProduct.getRiskProduct(sourceSystem);
    	if (product == null) {
    		return null;
    	}
    	String businessLine = product.getAntiFraudModelBusinessLine();
    	if (!StringUtils.isEmpty(version)) {
    		businessLine = businessLine + "." + version;
    	}
    	return businessLine;
	}

	private String getNewVersion(String version) {
		String newVersion = "";
		if (!StringUtils.isEmpty(version)) {
			newVersion = "_" + version;
		}
		return newVersion;
	}
}
