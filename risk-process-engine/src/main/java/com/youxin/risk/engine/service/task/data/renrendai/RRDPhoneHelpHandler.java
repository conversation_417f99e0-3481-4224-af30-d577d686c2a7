package com.youxin.risk.engine.service.task.data.renrendai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.DataVoUtils;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.RRDDataVoUtils;
import com.youxin.risk.engine.clients.RiskDiClient;
import com.youxin.risk.engine.service.task.data.ConfigurableDataHandler;
import com.youxin.risk.engine.vo.DiResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018/12/12 10:21
 */
@Service
public class RRDPhoneHelpHandler extends ConfigurableDataHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RRDPeopleBankHandler.class);

    private static final List<String> keywords = Arrays.asList("妈", "爸", "老婆|妻子", "老公", "姐", "妹", "哥", "弟", "姨", "姑", "伯", "叔", "舅");

    private static final String REQUEST_RESULT_FAILED = "请求邦秒配获取数据失败";

    private static final String MOBILE_IS_BLANK = "该手机号为空或不合法，无法请求邦秒配";

    private static final Integer TOP_NUM = 5;

    @Resource
    private RiskDiClient riskDiClient;

    @Override
    public DiResult callDi(Event event, String dataCode, EngineAsyncRequestLog engineAsyncRequestLog) {
        JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
        JSONArray emergencyContactList = RRDDataVoUtils.getEmergencyContactList(event);
        JSONArray contactList = RRDDataVoUtils.getContactList(event);
        // 注册手机号码
        String registerMobile = delMobilePrefix(userBasicInfo.getString("mobile"));
        // 银行卡预留手机号码
        String bankcardMobile = delMobilePrefix(userBasicInfo.getString("reserveMobile"));
        List<String> emergencyMobiles = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(emergencyContactList)) {
            for (int i = 0; i < emergencyContactList.size(); i++) {
                JSONObject emergency = emergencyContactList.getJSONObject(i);
                String mobile = delMobilePrefix(emergency.getString("mobile"));
                if (StringUtils.isNotBlank(mobile)) {
                    emergencyMobiles.add(mobile);
                }
            }
        }
        // 通讯录手机号码
        List<String> contactMobiles = getTopFiveFamilyContacts(contactList);
        // 通话详单手机号码
        List<String> frequestMobiles = getTopFiveFrequestContacts(event);
        Set<String> mobileSet = Sets.newHashSet();
        if (StringUtils.isNotBlank(registerMobile)) {
            mobileSet.add(registerMobile);
        }
        if (StringUtils.isNotBlank(bankcardMobile)) {
            mobileSet.add(bankcardMobile);
        }
        for (String mobile : emergencyMobiles) {
            mobileSet.add(mobile);
        }
        for (String mobile : contactMobiles) {
            mobileSet.add(mobile);
        }
        for (String mobile : frequestMobiles) {
            mobileSet.add(mobile);
        }
        String mobiles = String.join(",", mobileSet);

        Map<String, Object> params = Maps.newHashMap();
        params.put("systemId", event.getSourceSystem());
        params.put("mobiles", mobiles);
        this.buildAcquisitionTime(engineAsyncRequestLog.getNodeId(),dataCode,event,params);
        DiResult diResult = riskDiClient.callDi("batchPhoneHelpService", engineAsyncRequestLog.getAsyncRequestId(), event.getUserKey(), event.getLoanKey(), params);
        Map<String, Object> data = new HashMap<>();
        if (!diResult.getRetCode().equals(DiResult.DiRetCode.SUCCESS)) {
            LoggerProxy.info(LOGGER, "request for phonehelp data failed,userKey={},loanKey={},params={}", event.getUserKey(), event.getLoanKey(), JSON.toJSONString(params));
            data.put("data", REQUEST_RESULT_FAILED);
        } else {
            JSONObject jsonData = JSONObject.parseObject(diResult.getData());
            // 注册手机号
            Map<String, Object> registerMap = this.fillMapData(registerMobile, jsonData);
            data.put("register", registerMap);

            // 银行卡预留手机号
            Map<String, Object> bankcardMap = this.fillMapData(bankcardMobile, jsonData);
            data.put("bankcard", bankcardMap);

            // 紧急联系人
            List<Object> emergencyDataList = this.fillListData(emergencyMobiles, jsonData);
            data.put("emergencycontact", emergencyDataList);

            // 通话详单top5
            List<Object> frequentDataList = this.fillListData(frequestMobiles, jsonData);
            data.put("frequent", frequentDataList);

            // 通讯录亲情手机号top5
            List<Object> familyDataList = this.fillListData(contactMobiles, jsonData);
            data.put("familycontact", familyDataList);
        }
        JSONObject dataObj = new JSONObject();
        dataObj.put("data", JSON.toJSONString(data));

        JSONObject object = new JSONObject();
        object.put("status", 0);
        object.put("data", dataObj);
        DataVoUtils.putThirdPartyData(event, getThirdType(dataCode, engineAsyncRequestLog.getNodeId()), object.toJSONString());
        return new DiResult(DiResult.DiRetCode.SUCCESS);
    }

    private Map<String, Object> fillMapData(String mobile, JSONObject jsonData) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(mobile)) {
            map.put("mobile", mobile);
            map.put("data", jsonData.get(mobile));
        } else {
            map.put("data", MOBILE_IS_BLANK);
        }
        return map;
    }

    private List<Object> fillListData(List<String> mobileList, JSONObject jsonData) {
        List<Object> mobileDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mobileList)) {
            for (int i = 0; i < mobileList.size(); i++) {
                String mobile = mobileList.get(i);
                Map<String, Object> emergencyMap = new HashMap<>();
                emergencyMap.put("rank", i);
                if (StringUtils.isNotBlank(mobile)) {
                    emergencyMap.put("mobile", mobile);
                    emergencyMap.put("data", jsonData.get(mobile));
                } else {
                    emergencyMap.put("data", MOBILE_IS_BLANK);
                }
                mobileDataList.add(emergencyMap);
            }
        }
        return mobileDataList;
    }

    /**
     * 获取通话时长最长的TOP5 通话记录手机号 通话详单中（申请日期近90天内）根据通话时长加和排序top5手机号
     *
     * @param event
     * @return
     */
    private List<String> getTopFiveFrequestContacts(Event event) {
        List<String> resultList = Lists.newArrayList();
        try {
            // 调用DI获取通话详单
            JSONObject userBasicInfo = RRDDataVoUtils.getUserBasicInfo(event);
            Map<String, Object> params = Maps.newHashMap();
            params.put("systemId", event.getSourceSystem());
            params.put("jobId", userBasicInfo.getString("callHistoryJobId"));
            DiResult diResult = riskDiClient.callDi("callHistoryService", event.getSessionId() + "_" + GlobalUtil.getGlobalId(), event.getUserKey(), event.getLoanKey(), params);
            if (!DiResult.DiRetCode.SUCCESS.equals(diResult.getRetCode())) {
                LoggerProxy.warn("getCallHistoryFailed", LOGGER, "request={}, diResult={}", JSONObject.toJSONString(params), JSONObject.toJSONString(diResult));
                return resultList;
            }
            JSONObject callHistory = JSONObject.parseObject(diResult.getData());
            if (callHistory == null) {
                return resultList;
            }
            JSONArray records = callHistory.getJSONArray("records");
            if (records == null || records.size() <= 0) {
                return resultList;
            }
            JSONObject callRecord = records.getJSONObject(0);
            JSONArray details = callRecord.getJSONArray("details");
            if (details == null || details.size() <= 0) {
                return resultList;
            }
            Map<String, Integer> callMap = new HashMap<>();
            // 统计详单中所有人的通话时间
            for (int i = 0; i < details.size(); i++) {
                JSONObject call = details.getJSONObject(i);
                String callMobile = delMobilePrefix(call.getString("toMobile"));
                if (StringUtils.isNotBlank(callMobile)) {
                    if (callMap.containsKey(callMobile)) {
                        if (call.containsKey("duration")) {
                            Integer duration = callMap.get(callMobile);
                            duration += call.getInteger("duration");
                            callMap.put(callMobile, duration);
                        }
                    } else {
                        if (call.containsKey("duration")) {
                            callMap.put(callMobile, call.getInteger("duration"));
                        }
                    }
                }
            }
            LoggerProxy.info(LOGGER, "all calls duration (before sort),size={}, durationMap={}", callMap.size(), JSON.toJSONString(callMap));
            // 先按照通话时长排序，然后找出top20
            Map<String, Integer> sortMap = new LinkedHashMap<>();
            callMap.entrySet().stream().sorted(Map.Entry.<String, Integer>comparingByValue().reversed()).forEachOrdered(e -> sortMap.put(e.getKey(), e.getValue()));
            LoggerProxy.info(LOGGER, "all calls duration (after sort),size={}, durationMap={}", sortMap.size(), JSON.toJSONString(sortMap));

            if (sortMap.size() > TOP_NUM) {
                LoggerProxy.info(LOGGER, "all calls size greater than 5,actual={}", sortMap.size());
                int i = 0;
                for (Map.Entry<String, Integer> entry : sortMap.entrySet()) {
                    resultList.add(entry.getKey());
                    i++;
                    if (i == TOP_NUM) {
                        break;
                    }
                }

            } else {
                for (Map.Entry<String, Integer> entry : sortMap.entrySet()) {
                    resultList.add(entry.getKey());
                }
            }
            LoggerProxy.info(LOGGER, "phonehelp call analysis: top5 frequent contact info: size={}, contacts={}", resultList.size(), JSON.toJSONString(resultList));
        } catch (Exception e) {

        }
        return resultList;
    }

    /**
     * 获取通讯录中亲情手机号联系人TOP5 通讯录中姓名匹配List[“妈”， “爸”， “老婆|妻子”， “老公”，“姐”，”妹”， “哥”，”弟”，
     * “姨”， “姑”，”伯“，”叔”， “舅”] 的优先级排
     *
     * @param contactList
     * @return
     */
    private List<String> getTopFiveFamilyContacts(JSONArray contactList) {
        List<String> resultList = new ArrayList<String>();

        // 依次匹配keywords
        for (String keyword : keywords) {
            List<String> matchedMobileList = this.getMatchedMobileList(contactList, keyword);
            if (CollectionUtils.isNotEmpty(matchedMobileList)) {
                resultList.addAll(matchedMobileList);
            }
            if (resultList.size() > TOP_NUM) {
                LoggerProxy.info(LOGGER, "all matched family contacts:{}", JSON.toJSONString(resultList));
                for (int i = resultList.size() - TOP_NUM; i > 0; i--) {
                    String item = resultList.get(resultList.size() - 1);
                    resultList.remove(item);
                }
                LoggerProxy.info(LOGGER, "top5 matched family contacts:{}", JSON.toJSONString(resultList));
            }
            if (resultList.size() == TOP_NUM) {
                LoggerProxy.info(LOGGER, "top5 matched family contacts:{}", JSON.toJSONString(resultList));
                break;
            }
        }
        return resultList;
    }

    /**
     * find matched mobile list
     *
     * @param contactList
     * @return
     */
    private List<String> getMatchedMobileList(JSONArray contactList, String keyword) {
        List<String> matchedMobileList = new ArrayList<>();
        if (contactList == null || contactList.isEmpty()) {
            return  matchedMobileList;
        }

        if (StringUtils.isNotBlank(keyword)) {
            String[] words = keyword.split("\\|");
            for (int i = 0; i < words.length; i++) {
                String word = words[i];
                Pattern pattern = Pattern.compile(word);
                for (int j = 0; j < contactList.size(); j++) {
                    JSONObject contact = contactList.getJSONObject(j);
                    if (StringUtils.isNotBlank(contact.getString("mobile")) && StringUtils.isNotBlank(contact.getString("name"))) {
                        Matcher matcher = pattern.matcher(contact.getString("name"));
                        if (matcher.find()) {
                            String mobile = delMobilePrefix(contact.getString("mobile"));
                            if (StringUtils.isNotBlank(mobile)) {
                                matchedMobileList.add(mobile);
                            }
                        }
                    }
                }
            }
        }
        return matchedMobileList;
    }

    /**
     * 去除手机号前+86;
     * 过滤后判断长度是否＜7，＜则不调用；长度≥7时，判断是否含有特殊字符，若含有则不调用，若不含有，则正常调用；
     *
     * @param mobile
     * @return
     */
    private static String delMobilePrefix(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            boolean isPlus86 = mobile.startsWith("+86");

            if (isPlus86) {
                mobile = mobile.substring(3);
            }
            mobile = mobile.trim();
            int len = mobile.length();
            if (len > 6 && StringUtils.isNumeric(mobile)) {
                return mobile;
            }
        }
        return null;
    }
}
