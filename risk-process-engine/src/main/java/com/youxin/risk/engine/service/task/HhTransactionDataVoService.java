package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeFilter;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.verify.UserLineMidverifyResult;
import com.youxin.risk.commons.service.engine.EventService;
import com.youxin.risk.commons.utils.DataVoUtils;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.UnderScoreNameFilter;
import com.youxin.risk.engine.service.task.engine.EngineEventMongoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * 好分期交易审核，组装dataVo
 *
 * <AUTHOR>
 * @date 2019-01-02
 */
public class HhTransactionDataVoService extends AbstractTaskService {

    private static final Logger logger = LoggerFactory.getLogger(HhTransactionDataVoService.class);
    @Resource
    private EventService eventService;
    @Autowired
    private EngineEventMongoService engineEventMongoService;


    private static final String ANTIFRAUD_TRANS_SERVICE = "transAntifraudService";

    private static final String VERIFY_SERVICE_CODE = "verifyTransactionService";

    private static final String USER_LINE_SERVICE = "userLineService";

    private static final String USER_LINE_MID_VERIFY_SERVICE = "userLineMidVerifyService";

    private static final String PHONE_HELP_DATA = "phoneHelpService";

    private static final String LOAN_HISTORY = "loanHistoryByUserKeyService";

    private static final String USER_LEVEL = "userLevel";

    private static final String VERIFY_RESULT = "verifyResult";

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        Map<String, Object> params = event.getParams();
        Map<String, String> originDataVo = (Map<String, String>) event.getDataVo().get(DataVoUtils.getThirdPartyDataKey(event.getSourceSystem()));
        JSONObject dataVo = JSONObject.parseObject(originDataVo.get(ANTIFRAUD_TRANS_SERVICE));
        if (null == dataVo) {
            dataVo = new JSONObject();
        } else {
            dataVo = dataVo.getJSONObject("data");
        }
        // app_version
        addAppVersion(params, originDataVo, dataVo);
        // down_payment
        addDownPayment(params, originDataVo, dataVo);
        // last_verify_result
        addLastVerifyResult(event, originDataVo, dataVo);
        // last_amount_strategy_result
        addLastAmountStrategyResult(params, originDataVo, dataVo);
        // details
        addDetails(params, originDataVo, dataVo);
        // user_by_bangmiaopei
        addUserByBangMiaoPei(params, originDataVo, dataVo);
        // last_mid_verify_result
        addLastMidVerifyResult(params, originDataVo, dataVo);
        // high_freq
        addHighFreq(params, originDataVo, dataVo);

        ((Map<String, String>) event.getDataVo().get(DataVoUtils.getThirdPartyDataKey(event.getSourceSystem()))).put("originDataVo", dataVo.toJSONString());

//        eventService.updateEventToMongo(event);
    }

    private void addHighFreq(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        if (originDataVo.get("high_freq") != null) {
            dataVo.put("high_freq", JSONObject.parseObject(originDataVo.get("high_freq")));
        } else {
            dataVo.put("high_freq", "INVALID");
        }
    }

    private void addAppVersion(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        JSONObject operationLog = JSONObject.parseObject(originDataVo.get("findOperationLogByUserKeyAndSourceSystem"));
        if (null == operationLog) {
            operationLog = new JSONObject();
        }
        dataVo.put("app_version", operationLog.getOrDefault("appVersion", "Invalid"));
    }

    private void addDownPayment(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        JSONObject transDownPay = new JSONObject();
        transDownPay.put("orderHasFirstPay", params.get("orderHasFirstPay"));
        transDownPay.put("orderAmount", params.get("orderAmount") == null ? "null" : params.get("orderAmount"));
        transDownPay.put("orderFirstPayRate", params.get("orderFirstPayRate") == null ? "null" : params.get("orderFirstPayRate"));
        dataVo.put("down_payment", transDownPay);
    }

    private void addLastVerifyResult(Event event, Map<String, String> originDataVo, JSONObject dataVo) {

        JSONObject loanData = new JSONObject();

        JSONObject verifyInfo = JSON.parseObject(originDataVo.get(VERIFY_SERVICE_CODE));
        JSONObject verifyInfoData = null;
        if (verifyInfo != null) {
            verifyInfoData = (JSONObject) verifyInfo.get("data");
        }
        if (verifyInfoData != null && verifyInfoData.containsKey(USER_LEVEL)) {
            JSONObject userLevelParams = (JSONObject) verifyInfoData.get(USER_LEVEL);
            loanData.put("user_level", userLevelParams.get("userLevel"));
            loanData.put("user_point", userLevelParams.get("score"));
        }

        if (verifyInfoData != null && verifyInfoData.containsKey(VERIFY_RESULT)) {
            event.set("userLoanKey", verifyInfoData.getJSONObject(VERIFY_RESULT).get("loanKey"));
        }

        Map<String, Object> verifyResult = getLastVerifyResult(event, verifyInfoData);
        if (null != verifyResult) {
            loanData.put("reason_code", verifyResult.get("reasonCode"));
            if ("1".equals("" + verifyResult.get("isManual"))) {
                loanData.put("is_manual", true);
            } else {
                loanData.put("is_manual", false);
            }

        }

        // 进件人工审核信息暂不拼接
        dataVo.put("last_verify_result", loanData);
    }

    private Map<String, Object> getLastVerifyResult(Event event, JSONObject verifyInfo) {
        Map<String, Object> verifyResult = (Map<String, Object>) verifyInfo.get(VERIFY_RESULT);
        if (verifyResult != null) {
            return verifyResult;
        }
//      Event lastEvent = eventService.lastEvent(event); todo 替换
        Event lastEvent = engineEventMongoService.lastEvent(event);
        if (null == lastEvent) {
            return null;
        }
        return lastEvent.getVerifyResult();
    }

    private void addLastAmountStrategyResult(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        if (!originDataVo.containsKey(USER_LINE_SERVICE)) {
            return;
        }
        dataVo.put("last_amount_strategy_result", JSONObject.parseObject(originDataVo.get(USER_LINE_SERVICE)));
    }


    private void addDetails(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        JSONObject detailsData;
        if (dataVo.containsKey("details")) {
            detailsData = (JSONObject) dataVo.get("details");
        } else {
            detailsData = new JSONObject();
        }

        Map<String, Object> submitIdCard = JSON.parseObject(originDataVo.get("findSubmitIdCardByUserKey"));
        Map<String, Object> submitRegister = JSON.parseObject(originDataVo.get("findSubmitRegisterByUserKey"));
        // register
        JSONObject registerJson = new JSONObject();
        if (submitIdCard != null) {
            registerJson.put("name", submitIdCard.get("idcardName"));
            params.put("name", submitIdCard.get("idcardName"));
            params.put("idno", submitIdCard.get("idcardNumber"));
        }

        if (submitRegister != null) {
            registerJson.put("mobile", submitRegister.get("mobile"));
        }

        detailsData.put("register", registerJson);

        // 增加商品信息
        if (params.containsKey("transaction")) {
            JSONArray products = JSONObject.parseArray(JSONObject.toJSONString(params.get("transaction")));
            JSONArray productArray = new JSONArray();
            SerializeFilter serializeFilter = new UnderScoreNameFilter();
            for (Object productVo : products) {
                JSONObject productData = JSONObject.parseObject(JSONObject.toJSONString(productVo, serializeFilter));
                // rename key
                Object totalPrice = productData.remove("total_price");
                productData.put("totalPrice", totalPrice);

                Object productBranch = productData.remove("product_brand");
                productData.put("product_branch", productBranch);

                JSONObject productJson = new JSONObject();
                productJson.put("product", productData);
                productArray.add(productJson);
            }
            detailsData.put("products", productArray);
        }

        dataVo.put("details", detailsData);
    }

    private void addUserByBangMiaoPei(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        String phoneHelpData = originDataVo.get(PHONE_HELP_DATA);
        if (StringUtils.isBlank(phoneHelpData)) {
            phoneHelpData = "好买收货手机号请求邦秒配数据失败";
        }
        dataVo.put("user_by_bangmiaopei", phoneHelpData);
    }


    private void addHistLoanPayPlanInfos(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {

        if (!originDataVo.containsKey(LOAN_HISTORY)) {
            dataVo.put("histLoanPayPlanInfos", "invalid");
            return;
        }

        JSONObject histLoanPayPlanInfos = new JSONObject();
        JSONObject loanHistoryMap = JSON.parseObject(originDataVo.get(LOAN_HISTORY));
        JSONArray loans = loanHistoryMap.getJSONArray("loans");
        this.parseDate(loans);
        histLoanPayPlanInfos.put("AccountLoanInfo", loans);

        dataVo.put("histLoanPayPlanInfos", histLoanPayPlanInfos);
    }

    private void parseDate(JSONArray loans) {
        if (loans == null) {
            return;
        }

        for (int j = 0; j < loans.size(); j++) {
            JSONObject loan = loans.getJSONObject(j);

            this.parseDate(loan, "payoffTime");
            this.parseDate(loan, "loanTime");
            JSONArray periods = loan.getJSONArray("periods");
            if (periods != null) {
                for (int i = 0; i < periods.size(); i++) {
                    JSONObject period = periods.getJSONObject(i);
                    this.parseDate(period, "payoffTime");
                    this.parseDate(period, "periodExpirationDate");
                }
            }
        }
    }

    private void parseDate(JSONObject loan, String key) {
        try {
            if (loan == null || !loan.containsKey(key)) {
                return;
            }
            loan.put(key, new Date(loan.getLong(key)).toString());
        } catch (Exception e) {
            LoggerProxy.warn(logger, "parse Datetime failed,loan={},key={}", loan, key);
        }
    }


    private void addLastMidVerifyResult(Map<String, Object> params, Map<String, String> originDataVo, JSONObject dataVo) {
        UserLineMidverifyResult midverifyResult = JSON.parseObject(originDataVo.get(USER_LINE_MID_VERIFY_SERVICE), UserLineMidverifyResult.class);
        if (midverifyResult != null) {
            JSONObject midverifyObj = new JSONObject();
            midverifyObj.put("b_score", midverifyResult.getbScore());
            midverifyObj.put("amount_score", midverifyResult.getAmountScore());
            midverifyObj.put("is_increased", midverifyResult.getIsIncreased());
            midverifyObj.put("inc_level", midverifyResult.getIncLevel());
            midverifyObj.put("is_closed", midverifyResult.getIsClosed());
            midverifyObj.put("segment_code", midverifyResult.getSegmentCode());
            midverifyObj.put("reason_code", midverifyResult.getReasonCode());
            midverifyObj.put("test_code", midverifyResult.getTestCode());
            dataVo.put("last_mid_verify_result", midverifyObj);
        } else {
            dataVo.put("last_mid_verify_result", "INVALID");
        }

    }

}
