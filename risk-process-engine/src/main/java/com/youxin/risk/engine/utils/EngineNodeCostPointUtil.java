package com.youxin.risk.engine.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import com.youxin.risk.commons.utils.SpringContext;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.service.ProcessWakeupService;
import com.youxin.risk.engine.vo.NodeCostPointModel;
import com.youxin.risk.engine.vo.ProcessNodeInfo;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.List;
import java.util.Map;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.ENGINE_SPACE;

/**
 * @description: engine节点耗时打点工具类
 * @author: juxiang
 * @create: 2022-11-10 11:26
 **/
public class EngineNodeCostPointUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(EngineNodeCostPointUtil.class);

    private static int FIVE_MINUTES = 5 * 60 * 1000;

    private static final String LEND_AUDIT="haoHuanLendAudit";

    private static final String IS_OPEN_NEW_POINT="isOpenNewPoint";

    public static void point( ProcessContext processContext) {
        try {
            long lastNodeTime = processContext.getLastNodeTime();
            if(lastNodeTime!=0){
                Map<String, String> tags = Maps.newHashMap();
                String eventCode=processContext.getEvent().getEventCode();
                String node=processContext.getParentNodeId();
                tags.put("eventCode", eventCode);
                tags.put("node", node);
                long cost = System.currentTimeMillis() - lastNodeTime;
                if (cost > FIVE_MINUTES) {
                    LoggerProxy.info("EngineNodeCostPointUtilPoint", LOGGER, "node={},lastNodeTime={},cost={}ms", node,
                            lastNodeTime, cost);
                }
                MetricsAPI.point(PointConstant.PE_CALL_NODE_POINT, tags, cost, true, MetricsOpType.timer);
                //LoggerProxy.info("EngineNodeCostPointUtilPoint", LOGGER, "point success!");
                addPointInfo(processContext,cost);
            }
            //流程运行时节点信息
            buildAndAddProcessNodeInfo(processContext, processContext.getParentNodeId(),
                    processContext.getCurrentNodeId());

            processContext.setLastNodeTime(System.currentTimeMillis());
        }catch (Exception e){
            LoggerProxy.warn("EngineNodeCostPointUtilPointWarn", LOGGER, "error:",e);
        }
    }
    public static void buildAndAddProcessNodeInfo(ProcessContext processContext, String nodeId) {
        buildAndAddProcessNodeInfo(processContext, nodeId, null);
    }

    public static void buildAndAddProcessNodeInfo(ProcessContext processContext, String nodeId,
                                                  String currentRunNodeId) {
        long lastNodeTime = processContext.getLastNodeTime();
        if (lastNodeTime == 0) {
            return;
        }
        String eventCode = processContext.getEvent().getEventCode();
        List<String> notPointEventCodes = ApolloClientAdapter.getListConfig(ENGINE_SPACE, "runtime.metric.not" +
                ".eventCodes", String.class);
        if (notPointEventCodes.contains(eventCode)) {
            return;
        }

        ProcessNodeInfo currentNodeInfo = processContext.getCurrentNodeInfo();
        String loanKey = processContext.getEvent().getLoanKey();
        currentNodeInfo.setEventCode(eventCode);
        currentNodeInfo.setProcessDefId(processContext.getProcessInstance().getProcessDefId());
        currentNodeInfo.setTid(loanKey);
        currentNodeInfo.setNodeId(nodeId);
        currentNodeInfo.setCost(System.currentTimeMillis() - lastNodeTime);
        if (StringUtils.isNotBlank(currentRunNodeId)) {
            currentNodeInfo.setCurrentRunNodeId(currentRunNodeId);
        }
        processContext.addProcessNodeInfos(currentNodeInfo);
    }

    public static void sendProcessNodeInfo(ProcessContext processContext) {

        KafkaTemplate monitorDataKafkaTemplate = (KafkaTemplate) SpringContext.getBean("monitorDataKafkaTemplate");
        if (monitorDataKafkaTemplate == null) {
            LoggerProxy.warn("发送流程运行时消息", LOGGER, "异常，monitorDataKafkaTemplate为空");
            return;
        }
        for (ProcessNodeInfo processNodeInfo : processContext.getProcessNodeInfos()) {
            LoggerProxy.info("发送流程运行时消息", LOGGER, "loanKey={}, nodeId={},cost={}", processNodeInfo.getTid(),
                    processNodeInfo.getNodeId(), processNodeInfo.getCost());
            monitorDataKafkaTemplate.send("risk.engine.runtime.metric", processContext.getEvent().getLoanKey(), JSON.toJSONString(processNodeInfo));
        }
    }

    public static void addPointInfo(ProcessContext processContext,long cost) {
        try{
            Boolean doPoint = ApolloClientAdapter.getBooleanConfig(ENGINE_SPACE, IS_OPEN_NEW_POINT, false);
            if(!doPoint){
                return;
            }
            String parentNode=processContext.getParentNodeId();
            List nodePointInfo = processContext.getNodePointInfo();
            NodeCostPointModel nodeCostPointModel=new NodeCostPointModel();
            nodeCostPointModel.setCost(cost);
            nodeCostPointModel.setNode(parentNode);
            nodePointInfo.add(nodeCostPointModel);
        }catch (Exception e){
            LoggerProxy.warn("EngineNodeCostPointUtilAddPointWarn", LOGGER, "error:",e);
        }
    }

    public static void pointNew(ProcessContext processContext){
        try {
            Boolean doPoint = ApolloClientAdapter.getBooleanConfig(ENGINE_SPACE, IS_OPEN_NEW_POINT, false);
            if(!doPoint){
                return;
            }
            Event event=processContext.getEvent();
            String eventCode=event.getEventCode();
            for (Object nodePointInfo : processContext.getNodePointInfo()) {
                NodeCostPointModel nodeCostPoint = null;
                if (nodePointInfo instanceof NodeCostPointModel) {
                    nodeCostPoint = (NodeCostPointModel) nodePointInfo;
                } else {
                    nodeCostPoint = JSONObject.parseObject(JSON.toJSONString(nodePointInfo),
                            NodeCostPointModel.class);
                }
                Map<String, String> tags = Maps.newHashMap();
                tags.put("eventCode",eventCode);
                tags.put("node",nodeCostPoint.getNode());
                if(StringUtils.equals(eventCode,LEND_AUDIT) || StringUtils.equals(eventCode,"haoHuanLendAuditReloan")){
                    tags.put(EventVariableKeyEnum.isNewLoan.name(),isNewLoan(processContext));
                }
                tags.put(EventVariableKeyEnum.verifyResult.name(),String.valueOf(event.getVerifyResult().getOrDefault(EventVariableKeyEnum.verifyResult.name(),"None")));
                MetricsAPI.point(PointConstant.PE_CALL_NODE_POINT_NEW, tags, nodeCostPoint.getCost(), true, MetricsOpType.timer);
            }
        }catch (Exception e){
            LoggerProxy.warn("EngineNodeCostPointUtilPointNewWarn", LOGGER, "error occurred during pointNew method: " + e.getMessage(), e);
        }
    }

    /**
     * 判断放款审核首复贷
     * @param processContext
     * @return
     */
    private static String isNewLoan(ProcessContext processContext){
        String isNewLoan;
        try{
            Event event = processContext.getEvent();
            isNewLoan = String.valueOf(event.getOrDefault("isNewLoan","None"));
        }catch(Exception e){
            LoggerProxy.warn("isNewLoan", LOGGER, "", e);
            isNewLoan = "None";
        }
        return isNewLoan;
    }
}
