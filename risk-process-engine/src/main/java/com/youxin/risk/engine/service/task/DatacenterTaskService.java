package com.youxin.risk.engine.service.task;

import com.google.common.collect.Lists;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.model.datacenter.DcRequest;
import com.youxin.risk.commons.remote.model.datacenter.DcRequestService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.clients.RiskDcClient;
import com.youxin.risk.engine.service.task.datacenter.DatacenterHandlerManager;
import com.youxin.risk.engine.vo.DcResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

public class DatacenterTaskService extends AbstractTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DatacenterTaskService.class);

    @Resource
    private DatacenterHandlerManager datacenterHandlerManager;

    @Resource
    private RiskDcClient riskDcClient;

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        String serviceCode = processContext.getAttribute("serviceCode");
        List<String> serviceCodes = Arrays.asList(serviceCode.split(","));
        handler(processContext, event, serviceCodes);
//        this.eventService.updateEventToMongo(event);

    }

    public void handler(ProcessContext processContext, Event event, List<String> serviceCodes) {

        //build request param
        DcRequest request = this.buildDcRequest(event, serviceCodes, processContext.getCurrentNodeId());

        //call dc
        DcResult dcResult = this.riskDcClient.callDc(request);

        //save data
        if (RetCodeEnum.SUCCESS.equals(dcResult.getRetCode())) {
            if (dcResult.getDataMap() == null) {
                LoggerProxy.error("userInfoNotFound", LOGGER, "user info not found,userKey={}", request.getUserKey());
                throw new RiskRuntimeException("user info not found");
            }
            for (String key : dcResult.getDataMap().keySet()) {
                if (dcResult.getDataMap().get(key) != null) {
                    event.getDataVo().put(key, dcResult.getDataMap().get(key));
                } else {
                    LoggerProxy.info("getDcValueNull", LOGGER, "get DC service={} result is null,userKey={}", key, request.getUserKey());
                }
            }

        }

    }

    private DcRequest buildDcRequest(Event event, List<String> serviceCodes, String nodeId) {
        ArrayList<DcRequestService> services = Lists.newArrayList();
        Object apiLoanSource = event.get("apiLoanSource");
        for (String serviceCode : serviceCodes) {
            DcRequestService serviceReq = new DcRequestService();
            serviceReq.setServiceCode(serviceCode);
            Map<String,Object> params=this.datacenterHandlerManager.getDatacenterHandler(serviceCode).buildRequestParams(event,
                    serviceCode, nodeId);
            if(Objects.nonNull(apiLoanSource)){
                params.put("apiLoanSource",apiLoanSource);
            }
            serviceReq.setParams(params);
            services.add(serviceReq);
        }
        DcRequest request = new DcRequest();
        request.setServices(services);
        request.setUserKey(event.getUserKey());
        return request;
    }
}