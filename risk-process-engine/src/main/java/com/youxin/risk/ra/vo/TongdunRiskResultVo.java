package com.youxin.risk.ra.vo;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class TongdunRiskResultVo {
	private Integer id;
	private Boolean success;
	private String seqId;
	private String reasonCode;
	private String finalDecision;
	private Double finalScore;
	private List<TongdunRulesVo> hitRules;
	private List<TongdunRulesVo> hitRulesConvert;
	private String deviceInfo;
	private String geoipInfo;
	private String atrribution;
	private String policySetName;
	private List<TongdunPolicyVo> policySet;
	private Date queryTime;
	private Date backTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public String getSeqId() {
		return seqId;
	}

	public void setSeqId(String seqId) {
		this.seqId = seqId;
	}

	public String getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	public String getFinalDecision() {
		return finalDecision;
	}

	public void setFinalDecision(String finalDecision) {
		this.finalDecision = finalDecision;
	}

	public Double getFinalScore() {
		return finalScore;
	}

	public void setFinalScore(Double finalScore) {
		this.finalScore = finalScore;
	}

	public List<TongdunRulesVo> getHitRules() {
		return hitRules;
	}

	public void setHitRules(List<TongdunRulesVo> hitRules) {
		this.hitRules = hitRules;
	}

	public List<TongdunRulesVo> getHitRulesConvert() {
		return hitRulesConvert;
	}

	public void setHitRulesConvert(List<TongdunRulesVo> hitRulesConvert) {
		this.hitRulesConvert = hitRulesConvert;
	}

	public String getDeviceInfo() {
		return deviceInfo;
	}

	public void setDeviceInfo(String deviceInfo) {
		this.deviceInfo = deviceInfo;
	}

	public String getGeoipInfo() {
		return geoipInfo;
	}

	public void setGeoipInfo(String geoipInfo) {
		this.geoipInfo = geoipInfo;
	}

	public String getAtrribution() {
		return atrribution;
	}

	public void setAtrribution(String atrribution) {
		this.atrribution = atrribution;
	}

	public String getPolicySetName() {
		return policySetName;
	}

	public void setPolicySetName(String policySetName) {
		this.policySetName = policySetName;
	}

	public List<TongdunPolicyVo> getPolicySet() {
		return policySet;
	}

	public void setPolicySet(List<TongdunPolicyVo> policySet) {
		this.policySet = policySet;
	}

	public Date getQueryTime() {
		return queryTime;
	}

	public void setQueryTime(Date queryTime) {
		this.queryTime = queryTime;
	}

	public Date getBackTime() {
		return backTime;
	}

	public void setBackTime(Date backTime) {
		this.backTime = backTime;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
