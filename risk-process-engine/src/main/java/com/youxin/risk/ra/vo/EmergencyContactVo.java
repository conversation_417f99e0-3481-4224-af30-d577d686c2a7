package com.youxin.risk.ra.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.ra.utils.XmlEntity;
import com.youxin.risk.ra.utils.XmlNode;

@XmlEntity()
public class EmergencyContactVo {

    @JSONField(name = "bank_mobile")
    @XmlNode(name = "bank_mobile")
    private BankMobileVo bankMobile;

    @JSONField(name = "haomai_receiver")
    @XmlNode(name = "haomai_receiver")
    private ReceiverVo haomaiReceiverVo;

    @JSONField(name = "taobao_receiver")
    @XmlNode(name = "taobao_receiver")
    private ReceiverVo taobaoReceiverVo;

    @JSONField(name = "callrec_details")
    @XmlNode(name = "callrec_details")
    private CallerDetailVo callerDetailVo;

    @JSONField(name = "phone_book")
    @XmlNode(name = "phone_book")
    private PhoneBookCallVo phoneBookCallVo;

    @JSONField(name = "emergency_contact")
    @XmlNode(name = "emergency_contact")
    private ContactCallVo contactCallVo;

    public ReceiverVo getHaomaiReceiverVo() {
        return haomaiReceiverVo;
    }

    public void setHaomaiReceiverVo(ReceiverVo haomaiReceiverVo) {
        this.haomaiReceiverVo = haomaiReceiverVo;
    }

    public ReceiverVo getTaobaoReceiverVo() {
        return taobaoReceiverVo;
    }

    public void setTaobaoReceiverVo(ReceiverVo taobaoReceiverVo) {
        this.taobaoReceiverVo = taobaoReceiverVo;
    }

    public CallerDetailVo getCallerDetailVo() {
        return callerDetailVo;
    }

    public void setCallerDetailVo(CallerDetailVo callerDetailVo) {
        this.callerDetailVo = callerDetailVo;
    }

    public PhoneBookCallVo getPhoneBookCallVo() {
        return phoneBookCallVo;
    }

    public void setPhoneBookCallVo(PhoneBookCallVo phoneBookCallVo) {
        this.phoneBookCallVo = phoneBookCallVo;
    }

    public ContactCallVo getContactCallVo() {
        return contactCallVo;
    }

    public void setContactCallVo(ContactCallVo contactCallVo) {
        this.contactCallVo = contactCallVo;
    }

    public BankMobileVo getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(BankMobileVo bankMobile) {
        this.bankMobile = bankMobile;
    }
}
