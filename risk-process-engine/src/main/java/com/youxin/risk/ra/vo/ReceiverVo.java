package com.youxin.risk.ra.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.ra.utils.XmlEntity;
import com.youxin.risk.ra.utils.XmlNode;

@XmlEntity()
public class ReceiverVo {

    @JSONField(name = "mobile")
    @XmlNode(name = "mobile")
    private String mobile;

    @JSONField(name = "cnt_pid")
    @XmlNode(name = "cnt_pid")
    private int cntPid;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public int getCntPid() {
        return cntPid;
    }

    public void setCntPid(int cntPid) {
        this.cntPid = cntPid;
    }
}
