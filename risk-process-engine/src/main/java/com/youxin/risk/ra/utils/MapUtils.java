package com.youxin.risk.ra.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.alibaba.fastjson.JSONObject;

/**
 * 使用reflect进行转换
 */
public class MapUtils {

	public static <T> T mapToObject(Map<String, Object> map, Class<T> beanClass)
			throws Exception {
		if (map == null)
			return null;

		T obj = beanClass.newInstance();

		Field[] fields = obj.getClass().getDeclaredFields();
		for (Field field : fields) {
			int mod = field.getModifiers();
			if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
				continue;
			}

			field.setAccessible(true);
			field.set(obj, map.get(field.getName()));
		}

		return obj;
	}

	public static Map<String, Object> objectToMap(Object obj) throws Exception {
		if (obj == null) {
			return null;
		}

		Map<String, Object> map = new HashMap<String, Object>();

		Field[] declaredFields = obj.getClass().getDeclaredFields();
		for (Field field : declaredFields) {
			field.setAccessible(true);
			map.put(field.getName(), field.get(obj));
		}

		return map;
	}

	/**
	 * 将对象表达为string, string的map 要求所有的对象属性都有toString的方法。 只适用于无内部结构的对象
	 * 
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public static Map<String, String> objectToMapString(Object obj)
			throws Exception {
		Map<String, Object> map = (Map<String, Object>) JSONObject.toJSON(obj);
		Map<String, String> result = new HashMap<String, String>();
		for (String key : map.keySet()) {
			if (map.get(key) == null) {
				continue;
			}
			result.put(key, map.get(key).toString());
		}
		return result;
	}

	public static List<Entry<String, String>> sortMapByKey(
			Map<String, String> map) {
		List<Entry<String, String>> result = new ArrayList<Entry<String, String>>(
				map.entrySet());
		Collections.sort(result, new Comparator<Entry<String, String>>() {
			@Override
			public int compare(Entry<String, String> o1,
					Entry<String, String> o2) {
				return (o1.getKey()).toString().compareTo(o2.getKey());
			}
		});
		return result;
	}

	public static JSONObject underLineKeyToCamelKey(Map<String, Object> map) {
		JSONObject newMap = new JSONObject();
		for (String key : map.keySet()) {
			Object value = map.get(key);
			String newKey = StringUtils.underlineToCamel(key);
			if (value instanceof Map) {
				Map<String, Object> json = underLineKeyToCamelKey((JSONObject) value);
				newMap.put(newKey, json);
			} else if (value instanceof List) {
				List<Object> list = new ArrayList<Object>();
				for (Object obj : (List) value) {
					if (obj instanceof Map) {
						list.add(underLineKeyToCamelKey((JSONObject) obj));
					} else {
						list.addAll((List) value);
						break;
					}
				}
				newMap.put(newKey, list);
			} else {
				newMap.put(newKey, value);
			}

		}
		return newMap;
	}

	public static JSONObject camelKeyToUnderLineKey(Map<String, Object> map) {
		JSONObject newMap = new JSONObject();
		for (String key : map.keySet()) {
			Object value = map.get(key);
			String newKey = StringUtils.camelToUnderline(key);
			if (value instanceof Map) {
				Map<String, Object> json = camelKeyToUnderLineKey((JSONObject) value);
				newMap.put(newKey, json);
			} else if (value instanceof List) {
				List<Object> list = new ArrayList<Object>();
				for (Object obj : (List) value) {
					if (obj instanceof Map) {
						list.add(camelKeyToUnderLineKey((JSONObject) obj));
					} else {
						list.addAll((List) value);
						break;
					}
				}
				newMap.put(newKey, list);
			} else {
				newMap.put(newKey, value);
			}

		}
		return newMap;
	}

	public static void main(String[] args) {
		String json = "{\"sourceSystem\":\"PAY_DAY_LOAN\",\"idNumber\":\"131102198908240210\"}";
		JSONObject obj = JSONObject.parseObject(json);
		JSONObject obj2 = camelKeyToUnderLineKey(obj);
		System.out.println(JSONObject.toJSONString(obj2));
	}
}
