<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:mongo="http://www.springframework.org/schema/data/mongo"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
     			http://www.springframework.org/schema/data/mongo http://www.springframework.org/schema/data/mongo/spring-mongo-1.10.xsd">


	<!-- 去除集合里的_class属性 -->
	<bean id="mappingContext" class="org.springframework.data.mongodb.core.mapping.MongoMappingContext" />
	<bean id="defaultMongoTypeMapper"
		  class="org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper">
		<constructor-arg name="typeKey">
			<null />
		</constructor-arg>
	</bean>

	<!-- transfer -->
	<mongo:mongo-client id="mongoDataSource" replica-set="${mongo.host}" credentials="${mongo.credentials}">
		<!-- #autoConnectRetry 		控制系统在发生连接错误时是否重试 -->
		<!-- #connectionsPerHost	每个主机的连接池大小 -->
		<!-- #slave-ok				是否可以在从节点读取数 -->
		<mongo:client-options connections-per-host="500" write-concern="SAFE"
							  threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"
							  connect-timeout="30000" max-wait-time="1500"  socket-keep-alive="true"
							  socket-timeout="30000" max-connection-idle-time="60000"/>
	</mongo:mongo-client>
	<mongo:db-factory id="mongoFactory" mongo-ref="mongoDataSource" dbname="${mongo.database}" />
	<bean id="mappingMongoConverter"
		  class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">
		<constructor-arg name="mongoDbFactory" ref="mongoFactory" />
		<constructor-arg name="mappingContext" ref="mappingContext" />
		<property name="typeMapper" ref="defaultMongoTypeMapper" />
	</bean>
	<bean id="mongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
		<constructor-arg name="mongoDbFactory" ref="mongoFactory"/>
		<constructor-arg name="mongoConverter" ref="mappingMongoConverter"></constructor-arg>
	</bean>

	<!-- risk -->
	<mongo:mongo-client id="riskMongoDataSource" replica-set="${mongo.risk.host}" credentials="${mongo.risk.credentials}">
		<!-- #autoConnectRetry 		控制系统在发生连接错误时是否重试 -->
		<!-- #connectionsPerHost	每个主机的连接池大小 -->
		<!-- #slave-ok				是否可以在从节点读取数 -->
		<mongo:client-options connections-per-host="500" write-concern="SAFE"
							  threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"
							  connect-timeout="30000" max-wait-time="1500"  socket-keep-alive="true"
							  socket-timeout="10000" max-connection-idle-time="60000"/>
	</mongo:mongo-client>
	<mongo:db-factory id="riskMongoFactory" mongo-ref="riskMongoDataSource" dbname="${mongo.risk.database}" />
	<bean id="riskMappingMongoConverter"
		  class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">
		<constructor-arg name="mongoDbFactory" ref="riskMongoFactory" />
		<constructor-arg name="mappingContext" ref="mappingContext" />
		<property name="typeMapper" ref="defaultMongoTypeMapper" />
	</bean>
	<bean id="riskMongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
		<constructor-arg name="mongoDbFactory" ref="riskMongoFactory"/>
		<constructor-arg name="mongoConverter" ref="mappingMongoConverter"></constructor-arg>
	</bean>

	<bean id="mongoDao" class="com.youxin.risk.commons.mongo.MongoDao"/>
<!--	<bean id="eventDao" class="com.youxin.risk.commons.mongo.EventDao" />-->
	<bean id="verifyResultDataDao" class="com.youxin.risk.commons.mongo.VerifyResultDataDao"/>
	<bean id="VerifyResultPuDaoDataDao" class="com.youxin.risk.commons.mongo.VerifyResultPuDaoDataDao"/>
	<bean id="analysisReportDao" class="com.youxin.risk.commons.mongo.AnalysisReportDao"/>

	<bean id="strategyInvokeMongoDao" class="com.youxin.risk.commons.mongo.StrategyInvokeMongoDao">
		<property name="template" ref="riskMongoTemplate"/>
	</bean>

	<bean id="processSplitFlowResultMongoDao" class="com.youxin.risk.commons.mongo.ProcessSplitFlowResultMongoDao">
		<property name="template" ref="riskMongoTemplate"/>
	</bean>

	<bean id="verifyStrategyResultDao" class="com.youxin.risk.commons.mongo.VerifyStrategyResultDao"/>

	<bean id="bohaiSubmitDao" class="com.youxin.risk.commons.mongo.BohaiSubmitDao"/>

	<bean id="callHistoryDao" class="com.youxin.risk.commons.mongo.CallHistoryDao"/>

	<!-- sharding -->
<!--	<mongo:mongo-client id="shardingMongoDataSource" replica-set="${mongo.sharding.host}"-->
<!--						credentials="${mongo.sharding.credentials}">-->
<!--		<mongo:client-options connections-per-host="500" write-concern="W1"-->
<!--							  threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"-->
<!--							  connect-timeout="30000" max-wait-time="1500" socket-keep-alive="true"-->
<!--							  socket-timeout="10000" max-connection-idle-time="60000"/>-->
<!--	</mongo:mongo-client>-->
<!--	<mongo:db-factory id="shardingMongoFactory" mongo-ref="shardingMongoDataSource"-->
<!--					  dbname="${mongo.sharding.database}"/>-->
<!--	<bean id="shardingMappingMongoConverter"-->
<!--		  class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">-->
<!--		<constructor-arg name="mongoDbFactory" ref="shardingMongoFactory"/>-->
<!--		<constructor-arg name="mappingContext" ref="mappingContext"/>-->
<!--		<property name="typeMapper" ref="defaultMongoTypeMapper"/>-->
<!--	</bean>-->

<!--	<bean id="shardingMongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">-->
<!--		<constructor-arg name="mongoDbFactory" ref="shardingMongoFactory"/>-->
<!--	</bean>-->

	<!-- sharding slave 只有engine读取三方缓存时才能使用此配置-->
<!--	<mongo:mongo-client id="shardingMongoDataSourceCache" replica-set="${mongo.sharding.host}"-->
<!--						credentials="${mongo.sharding.credentials}">-->
<!--		<mongo:client-options connections-per-host="500" write-concern="W1" read-preference="SECONDARY_PREFERRED"-->
<!--							  threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"-->
<!--							  connect-timeout="30000" max-wait-time="1500" socket-keep-alive="true"-->
<!--							  socket-timeout="10000" max-connection-idle-time="60000"/>-->
<!--	</mongo:mongo-client>-->
<!--	<mongo:db-factory id="shardingMongoFactoryCache" mongo-ref="shardingMongoDataSourceCache"-->
<!--					  dbname="${mongo.sharding.database}"/>-->
<!--	<bean id="shardingMongoTemplateCache" class="org.springframework.data.mongodb.core.MongoTemplate">-->
<!--		<constructor-arg name="mongoDbFactory" ref="shardingMongoFactoryCache"/>-->
<!--	</bean>-->

	<mongo:mongo-client id="newTransferMongoDataSource" replica-set="${transfer.mongo.host}" credentials="${mongo.credentials}">
		<!-- #autoConnectRetry 		控制系统在发生连接错误时是否重试 -->
		<!-- #connectionsPerHost	每个主机的连接池大小 -->
		<!-- #slave-ok				是否可以在从节点读取数 -->
		<mongo:client-options connections-per-host="70" write-concern="SAFE"
							  threads-allowed-to-block-for-connection-multiplier="50" heartbeat-connect-timeout="5000"
							  connect-timeout="30000" max-wait-time="1500"  socket-keep-alive="true"
							  socket-timeout="10000" max-connection-idle-time="60000"/>
	</mongo:mongo-client>
	<!-- 定义MONGO ANTIFRAUD 连接工厂 -->
	<mongo:db-factory id="newMongoFactory"
					  mongo-ref="newTransferMongoDataSource" dbname="${mongo.database}" />

	<!-- 定义Template -->
	<bean id="newTransferMongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
		<constructor-arg name="mongoDbFactory" ref="newMongoFactory"/>
		<constructor-arg name="mongoConverter" ref="mappingMongoConverter"></constructor-arg>
	</bean>


</beans>
