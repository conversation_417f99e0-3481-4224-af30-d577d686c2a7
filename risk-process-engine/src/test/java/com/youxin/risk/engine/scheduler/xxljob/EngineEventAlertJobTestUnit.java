package com.youxin.risk.engine.scheduler.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.youxin.risk.commons.constants.EngineEventStatusEnum;
import com.youxin.risk.commons.model.EngineAlertVo;
import com.youxin.risk.commons.service.WeChatService;
import com.youxin.risk.commons.service.engine.EngineEventService;
import com.youxin.risk.engine.service.ApolloClientAdapterWrapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class EngineEventAlertJobTestUnit {


    @InjectMocks
    private EngineEventAlertJob engineEventAlertJob ;

    @Mock
    private EngineEventService engineEventService;

    @Mock
    private ApolloClientAdapterWrapper apolloClientAdapterWrapper;

    @Mock
    private WeChatService weChatService;

    @Test
    public void test_getCurrentEventBlockCount0() {
        int currentEventBlockCount = engineEventAlertJob.getCurrentEventBlockCount(new ArrayList<>());
        assertEquals(0, currentEventBlockCount);
    }

    @Test
    public void test_getCurrentEventBlockCount1() {

        ArrayList<EngineAlertVo> engineAlertVos = new ArrayList<>();
        EngineAlertVo engineAlertVo = new EngineAlertVo();
        engineAlertVo.setEventCode("test");
        engineAlertVo.setCnt(1L);
        engineAlertVo.setSessionIdCnt(10L);
        engineAlertVo.setDataCode("none");
        engineAlertVos.add(engineAlertVo);
        int currentEventBlockCount = engineEventAlertJob.getCurrentEventBlockCount(engineAlertVos);
        assertEquals(1, currentEventBlockCount);
    }

    @Test
    public void test_getCurrentEventBlockCount2() {

        EngineAlertVo engineAlertVo = new EngineAlertVo();
        engineAlertVo.setEventCode("test");
        engineAlertVo.setCnt(1L);
        engineAlertVo.setSessionIdCnt(10L);
        engineAlertVo.setDataCode("none");

        EngineAlertVo engineAlertVo2= new EngineAlertVo();
        engineAlertVo2.setEventCode("test");
        engineAlertVo2.setCnt(10L);
        engineAlertVo2.setSessionIdCnt(10L);
        engineAlertVo2.setDataCode("none2");

        ArrayList<EngineAlertVo> engineAlertVos = new ArrayList<>();
        engineAlertVos.add(engineAlertVo);
        engineAlertVos.add(engineAlertVo2);
        int currentEventBlockCount = engineEventAlertJob.getCurrentEventBlockCount(engineAlertVos);
        assertEquals(10, currentEventBlockCount);
    }

    @Test
    public void test_getCurrentEventBlockCount3() {

        EngineAlertVo engineAlertVo = new EngineAlertVo();
        engineAlertVo.setEventCode("test");
        engineAlertVo.setCnt(1L);
        engineAlertVo.setSessionIdCnt(10L);
        engineAlertVo.setDataCode("none");

        EngineAlertVo engineAlertVo2= new EngineAlertVo();
        engineAlertVo2.setEventCode("test");
        engineAlertVo2.setCnt(9L);
        engineAlertVo2.setSessionIdCnt(10L);
        engineAlertVo2.setDataCode("none2");

        ArrayList<EngineAlertVo> engineAlertVos = new ArrayList<>();
        engineAlertVos.add(engineAlertVo);
        engineAlertVos.add(engineAlertVo2);
        int currentEventBlockCount = engineEventAlertJob.getCurrentEventBlockCount(engineAlertVos);
        assertEquals(10, currentEventBlockCount);
    }

    static EngineAlertVo ignoreVarAlertVo = new EngineAlertVo();
    static EngineAlertVo initAlertVo= new EngineAlertVo();
    static {
        ignoreVarAlertVo.setEventCode("test");
        ignoreVarAlertVo.setCnt(9L);
        ignoreVarAlertVo.setSessionIdCnt(10L);
        ignoreVarAlertVo.setDataCode("jg_fraud_case_audit_result");
        ignoreVarAlertVo.setStatus(EngineEventStatusEnum.SUSPENDED.name());

        initAlertVo.setEventCode("test");
        initAlertVo.setCnt(1L);
        initAlertVo.setSessionIdCnt(10L);
        initAlertVo.setDataCode("none");
        initAlertVo.setStatus(EngineEventStatusEnum.INIT.name());
    }



    @Test
    public void test_execJobHandler() {
        List<EngineAlertVo> engineAlertVos = new ArrayList<>();
        engineAlertVos.add(ignoreVarAlertVo);
        engineAlertVos.add(initAlertVo);
        when(engineEventService.selectForAlert(any())).thenReturn(engineAlertVos);

        when(apolloClientAdapterWrapper.getListConfig(any(), any(), any())).thenReturn(Collections.singletonList(
                "jg_fraud_case_audit_result"));
//        weChatService = new WeChatService("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=");
        doNothing().when(weChatService).sendMarkdownMsg(any(), any());

        //todo
        ReturnT<String> returnT = engineEventAlertJob.execJobHandler("");
        assertEquals(ReturnT.SUCCESS, returnT);

//        engineEventAlertJob.
//        engineEventAlertJob.execJobHandler();
    }

    @Test
    public void test_buildAlertMessage() {
        List<EngineAlertVo> engineAlertVos = new ArrayList<>();
        engineAlertVos.add(initAlertVo);
        Map<String, List<EngineAlertVo>> eventCodeAndalertListMap = new HashMap<>();
        eventCodeAndalertListMap.put("test", engineAlertVos);
        String alertMessage = engineEventAlertJob.buildAlertMessage(eventCodeAndalertListMap);
        assertTrue(alertMessage.contains("引擎卡单报警(需手动处理) 总数量：<font color=\"warning\">1</font> "));
        assertTrue(alertMessage.contains("test: <font color=\"warning\">1</font> "));
    }
//    @Test
//    public void test_buildAlertMessage_error() {
//        List<EngineAlertVo> engineAlertVos = new ArrayList<>();
//        engineAlertVos.add(initAlertVo);
//        Map<String, List<EngineAlertVo>> eventCodeAndalertListMap = new HashMap<>();
//        eventCodeAndalertListMap.put("test", engineAlertVos);
//        String alertMessage = engineEventAlertJob.buildAlertMessage(eventCodeAndalertListMap);
//        assertTrue(alertMessage.contains("引擎卡单报警(需手动处理) 总数量：<font color=\"warning\">10</font> "));
//        assertTrue(alertMessage.contains("test: <font color=\"warning\">10</font> "));
//    }

}