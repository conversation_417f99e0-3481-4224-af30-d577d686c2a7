package com.youxin.risk.engine.test;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.clients.RiskDcClient;
import com.youxin.risk.engine.vo.QuotaMessageVO;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/spring-config.xml"})
public class RiskDcClientTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiskDcClientTest.class);

    @Autowired
    private RiskDcClient riskDcClient;
    @Value("${risk.quota.url}")
    private String riskQuotaUrl;

    @Test
    public void testSendQuotaResultAndCompareData() throws IOException {
        String jsonContent = getJsonContent("data/event.json");
        JSONObject jsonObject = JSONObject.parseObject(jsonContent);
        Event event = new Event();
        event.setUserKey(jsonObject.getString("userKey"));
        String loanKey = UUID.randomUUID().toString();
        event.setLoanKey(loanKey);
        event.setEventCode(jsonObject.getString("eventCode"));
        event.setParams(jsonObject.getJSONObject("params"));
        event.setVerifyResult(jsonObject.getJSONObject("verifyResult"));

        String jsonContent1 = getJsonContent("data/result.json");
        JSONObject lineResult = JSONObject.parseObject(jsonContent1);

        QuotaMessageVO quotaMessageVO = QuotaMessageVO.toQuotaObject(event, "IRR24_VERIFY_AMOUNT");

        String response = riskDcClient.sendHttpRequestToQuotaCenter(riskQuotaUrl + "/quota/api/handleQuota", quotaMessageVO);
        JSONObject responseJson = JSONObject.parseObject(response);
        int status = responseJson.getInteger("status");
        if (status != 0) {
            LoggerProxy.error("sendQuotaResult", LOGGER
                    , "userKey={}, loanKey={}, eventCode={}, sendQuotaResult failed:{}"
                    , event.getUserKey(), event.getLoanKey(), event.getEventCode(), responseJson.getString("message"));
            throw new RiskRuntimeException(RetCodeEnum.FAILED, responseJson.getString("message"));
        }

        JSONObject processedLineResult = responseJson.getJSONObject("data");

        JSONObject newLineResult = riskDcClient.buildNewLineResult(lineResult, processedLineResult);
        riskDcClient.compareData(lineResult, newLineResult, event.getEventCode());
    }

    private String getJsonContent(String path) throws IOException {
        String jsonContent;
        ClassLoader classLoader = getClass().getClassLoader();
        try (InputStream inputStream = classLoader.getResourceAsStream(path)) {
            if (inputStream == null) {
                throw new IllegalArgumentException("File not found!");
            }
            jsonContent = IOUtils.toString(inputStream, String.valueOf(StandardCharsets.UTF_8));
        }
        return jsonContent;
    }

    @Test
    public void testIsUpdateVerifyResultRequired() throws IOException {
        boolean irr24VerifyAmount = riskDcClient.isUpdateVerifyResultRequired("test1");
        System.out.println(irr24VerifyAmount);
    }
}