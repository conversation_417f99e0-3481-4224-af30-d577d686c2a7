package com.youxin.risk.engine.test;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.cacheloader.service.NodeDataParamService;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.NodeDataParam;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.engine.service.task.data.ResolveParamService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mvel2.PropertyAccessException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config-test.xml")
public class ResolveParamServiceTest {

    private Event event;

    private Event event2;

    @Resource
    private ResolveParamService resolveParamService;

    @Before
    public void setUp() {

    }
    @Resource
    private NodeDataParamService nodeDataParamService;

    @Test
    public void resolveParams() {
        List<NodeDataParam> nodeDataParams =
                nodeDataParamService.selectByNodeCodeAndServiceCode("", "");
        System.out.println(JSONObject.toJSONString(nodeDataParams));
    }

    @Test
    public void testResolveExpression1() {
        Assert.assertEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "params.eventCode"), "prepareHaoHuanApply");
    }

    @Test
    public void testResolveExpression2() {
        Assert.assertEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "params.userStatusInfo.taobaoTime"), 0);
    }

    @Test
    public void testResolveExpression3() {
        Assert.assertNull(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "params.userStatusInfo.taobaoStatus"));
    }

    @Test(expected = PropertyAccessException.class)
    public void testResolveExpression4() {
        resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "params.userStatusInfo.null");
    }

    @Test
    public void testResolveExpression5() {
        Assert.assertEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "return \"test\""), "test");
    }

    @Test
    public void testResolveExpression6() {
        Assert.assertNotEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "return \"test\""), "test2");
    }

    @Test
    public void testResolveExpression7() {
        Assert.assertEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "\"test\""), "test");
    }

    @Test
    public void testResolveExpression8() {
        Assert.assertNotEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "\"test\""), "test2");
    }

    @Test
    public void testResolveExpression9() {
        Assert.assertNull(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "params.userStatusInfo.?null.null.null"));
    }

    @Test
    public void testResolveExpression10() {
        Assert.assertNotNull(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "new java.util.Date()"));
    }

    @Test
    public void testResolveExpression11() {
        Assert.assertEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "com.alibaba.fastjson.JSONObject.parseObject(dataVo.ID_CARD)['idcardNumber']"), "350426198509113027");
        Assert.assertNull(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "com.alibaba.fastjson.JSONObject.parseObject(dataVo.ID_CARD)['notExist']"));
//        Assert.assertEquals(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "dataVo.ID_CARD.idcardNumber"), "350426198509113027");
//        Assert.assertNull(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), "dataVo.ID_CARD.?notExist"));
        System.out.println(resolveParamService.resolveExpression(JSONObject.parseObject(JsonUtils.toJsonString(event)), " '' + System.currentTimeMillis()"));
    }
}