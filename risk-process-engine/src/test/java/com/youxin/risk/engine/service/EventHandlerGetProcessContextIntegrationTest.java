package com.youxin.risk.engine.service;

import com.youxin.risk.commons.constants.EngineEventStatusEnum;
import com.youxin.risk.commons.model.EngineEvent;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.engine.activiti.constants.ProcessInstanceStatusEnum;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.ProcessInstance;
import com.youxin.risk.engine.activiti.service.persist.ProcessInstanceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

/**
 * EventHandler.getProcessContext方法的集成测试
 * 测试在不同场景下获取流程上下文的行为
 */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration("classpath:spring/spring-config-test.xml")
public class EventHandlerGetProcessContextIntegrationTest {

    @InjectMocks
    private EventHandler eventHandler;

    @Mock
    private ProcessInstanceService processInstanceService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试非重试流程场景
     * 当EngineEvent不是错误状态或没有流程实例ID时，应创建新的上下文
     */
    @Test
    public void testGetProcessContext_NonRetryProcess() {
        // 准备测试数据
        Event event = new Event();
        event.setSessionId("test-session-id");
        
        EngineEvent engineEvent = new EngineEvent();
        engineEvent.setStatus(EngineEventStatusEnum.INIT.name());
        engineEvent.setError(null);
        engineEvent.setProcessInstanceId(null);
        
        ProcessInstance newProcessInstance = new ProcessInstance("test-process-def-id");
        
        // 执行测试
        ProcessContext result = eventHandler.getProcessContext(engineEvent, newProcessInstance, event);
        
        // 验证结果
        assertNotNull("应返回非空的ProcessContext", result);
        assertSame("ProcessContext应包含传入的事件", event, result.getVariables().get(Event.EVENT_KEY));
        assertSame("ProcessContext应包含传入的流程实例", newProcessInstance, result.getProcessInstance());
    }

    /**
     * 测试重试流程场景 - 异常状态
     * 当EngineEvent是错误状态且有流程实例ID，且数据库中存在异常状态的流程实例时，应返回数据库中的上下文
     */
    @Test
    public void testGetProcessContext_RetryProcess_ExceptionStatus() {
        // 准备测试数据
        String processInstanceId = "test-process-instance-id";
        
        Event event = new Event();
        event.setSessionId("test-session-id");
        
        EngineEvent engineEvent = new EngineEvent();
        engineEvent.setStatus(EngineEventStatusEnum.FAILED.name());
        engineEvent.setError("测试错误");
        engineEvent.setProcessInstanceId(processInstanceId);
        
        ProcessInstance newProcessInstance = new ProcessInstance("test-process-def-id");
        ProcessInstance dbProcessInstance = new ProcessInstance("test-process-def-id", 
                ProcessInstanceStatusEnum.EXCEPTION.name(), processInstanceId);
        
        ProcessContext dbContext = new ProcessContext(dbProcessInstance);
        
        // 模拟ProcessInstanceService行为
        when(processInstanceService.queryByStatusWithoutEvent(processInstanceId, ProcessInstanceStatusEnum.EXCEPTION))
                .thenReturn(dbContext);
        
        // 执行测试
        ProcessContext result = eventHandler.getProcessContext(engineEvent, newProcessInstance, event);
        
        // 验证结果
        assertNotNull("应返回非空的ProcessContext", result);
        assertSame("应返回数据库中的ProcessContext", dbContext, result);
        assertEquals("ProcessContext中的事件应被更新", event, result.getVariables().get(Event.EVENT_KEY));
        assertEquals("ProcessContext中的工作流应被更新", newProcessInstance.getWorkFlow(),
                result.getProcessInstance().getWorkFlow());
    }

    /**
     * 测试重试流程场景 - 挂起状态
     * 当EngineEvent是错误状态且有流程实例ID，且数据库中存在挂起状态的流程实例时，应返回数据库中的上下文
     */
    @Test
    public void testGetProcessContext_RetryProcess_SuspendStatus() {
        // 准备测试数据
        String processInstanceId = "test-process-instance-id";
        
        Event event = new Event();
        event.setSessionId("test-session-id");
        
        EngineEvent engineEvent = new EngineEvent();
        engineEvent.setStatus(EngineEventStatusEnum.FAILED.name());
        engineEvent.setError("测试错误");
        engineEvent.setProcessInstanceId(processInstanceId);
        
        ProcessInstance newProcessInstance = new ProcessInstance("test-process-def-id");
        ProcessInstance dbProcessInstance = new ProcessInstance("test-process-def-id", 
                ProcessInstanceStatusEnum.SUSPENDED.name(), processInstanceId);
        
        ProcessContext dbContext = new ProcessContext(dbProcessInstance);
        
        // 模拟ProcessInstanceService行为
        when(processInstanceService.queryByStatusWithoutEvent(processInstanceId, ProcessInstanceStatusEnum.EXCEPTION))
                .thenReturn(null);
        when(processInstanceService.querySuspend(processInstanceId)).thenReturn(dbContext);
        
        // 执行测试
        ProcessContext result = eventHandler.getProcessContext(engineEvent, newProcessInstance, event);
        
        // 验证结果
        assertNotNull("应返回非空的ProcessContext", result);
        assertSame("应返回数据库中的ProcessContext", dbContext, result);
        assertEquals("ProcessContext中的事件应被更新", event, result.getVariables().get(Event.EVENT_KEY));
        assertEquals("ProcessContext中的工作流应被更新", newProcessInstance.getWorkFlow(),
                result.getProcessInstance().getWorkFlow());
    }

    /**
     * 测试重试流程场景 - 找不到流程实例
     * 当EngineEvent是错误状态且有流程实例ID，但数据库中找不到对应的流程实例时，应创建新的上下文
     */
    @Test
    public void testGetProcessContext_RetryProcess_NotFound() {
        // 准备测试数据
        String processInstanceId = "test-process-instance-id";
        
        Event event = new Event();
        event.setSessionId("test-session-id");
        
        EngineEvent engineEvent = new EngineEvent();
        engineEvent.setStatus(EngineEventStatusEnum.FAILED.name());
        engineEvent.setError("测试错误");
        engineEvent.setProcessInstanceId(processInstanceId);
        
        ProcessInstance newProcessInstance = new ProcessInstance("test-process-def-id");
        
        // 模拟ProcessInstanceService行为
        when(processInstanceService.queryByStatusWithoutEvent(processInstanceId, ProcessInstanceStatusEnum.EXCEPTION))
                .thenReturn(null);
        when(processInstanceService.querySuspend(processInstanceId)).thenReturn(null);
        
        // 执行测试
        ProcessContext result = eventHandler.getProcessContext(engineEvent, newProcessInstance, event);
        
        // 验证结果
        assertNotNull("应返回非空的ProcessContext", result);
        assertSame("ProcessContext应包含传入的事件", event, result.getVariables().get(Event.EVENT_KEY));
        assertSame("ProcessContext应包含传入的流程实例", newProcessInstance, result.getProcessInstance());
    }
}