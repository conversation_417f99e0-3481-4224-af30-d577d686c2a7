# 限流效果验证方案

## 1. Redis 监控命令
```bash
# 监控 Redis 限流 key 的变化
redis-cli monitor | grep "flowLimit-"

# 查看特定事件的限流值
redis-cli get "flowLimit-haoHuanAmountRepay"
```

## 2. 日志监控关键字
```
# 网关限流日志
grep "remainLimit" gateway.log

# 定时任务初始化日志  
grep "initRedisLimit" gateway.log

# 限流处理日志
grep "eventLimitHandleCost" gateway.log
```

## 3. 验证步骤
1. 重启应用，确保新代码生效
2. 观察 Redis key 是否正确初始化
3. 模拟大量请求，验证限流是否生效
4. 检查每分钟处理量是否控制在 360 以内

## 4. 预期结果
- Redis key 会自动初始化
- 超过限制的请求会被写入数据库
- 每分钟处理量稳定在配置值以内
