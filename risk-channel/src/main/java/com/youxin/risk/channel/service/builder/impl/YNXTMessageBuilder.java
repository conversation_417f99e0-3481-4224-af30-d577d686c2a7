package com.youxin.risk.channel.service.builder.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.youxin.risk.channel.constants.ChannelConstant;
import com.youxin.risk.channel.constants.FundPlatChannel;
import com.youxin.risk.channel.constants.FundPlatRequestType;
import com.youxin.risk.channel.service.SubmitToFundPlatService;
import com.youxin.risk.commons.constants.AppName;
import com.youxin.risk.commons.constants.RequestType;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import com.youxin.risk.commons.model.ChannelRequestModel;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("YNXTMessageBuilder")
public class YNXTMessageBuilder extends AbstractAgencyMessageBuilder implements InitializingBean {

    private Logger logger = LoggerFactory.getLogger(SubmitToFundPlatService.class);

    @Resource(name = "configProperties")
    private Properties configProperties;
    @Value("${url.dataplatform.get}")
    private String urlGet;

    private static final String FILE_SUFFIX = ".jpg";// 默认文件后缀
    private static final String DEFAULT_SYSTEMID = "HAOHUAN";// 云信-好分期默认系统标识
    private static final String FILE_TYPE_ID_CARD_FRONT = "ID_CARD_FRONT";// 身份证正面
    private static final String FILE_TYPE_ID_CARD_BACK = "ID_CARD_BACK";// 身份证反面
    private static final String DC_SUCCESS_RETCODE = "07S0000";// 数据中心successCode
    private String SALT_FUND_PLATFORM;// 资金平台加密salt
    private String DEFAULT_PRODUCTKEY;// 资金平台-好还默认产品KEY
    private Map<String, String> MAP_FUND_HAOHUAN_PRODUCTKEY; // 资金平台-好还对应各产品KEY


    @Override
    public void afterPropertiesSet() throws Exception {
        SALT_FUND_PLATFORM = configProperties.getProperty("salt.fund.platform");
        DEFAULT_PRODUCTKEY = configProperties.getProperty("productkey.haohuan.borrow");
        MAP_FUND_HAOHUAN_PRODUCTKEY = new HashMap<>();
//        MAP_FUND_HAOHUAN_PRODUCTKEY.put("CREDIT_PAY", configProperties.getProperty("productkey.haohuan.pay")); // 好买
        MAP_FUND_HAOHUAN_PRODUCTKEY.put("CASH_BORROW", configProperties.getProperty("productkey.haohuan.borrow")); // 好借
//        MAP_FUND_HAOHUAN_PRODUCTKEY.put("CONSUMER_LOAN", configProperties.getProperty("productkey.haohuan.loan")); // 好还

    }

    @Override
    public String build(ChannelRequestModel model, ChannelRequestAgency agencyModel) {
        // 根据调用环节组织请求参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("serviceVersion", "1.0");
        jsonObject.put("sourceCode", "PARTNER_VERIFY");
        jsonObject.put("requestSerialNo", createUuid());
        jsonObject.put("requestTime", System.currentTimeMillis());
        JSONObject extraData = (JSONObject) agencyModel.getExtraData();
        FundPlatRequestType requestType = (FundPlatRequestType) extraData.get("fundPlatRequestType");
        switch (requestType) {
            case submit:
                buildStepOneParam(model, jsonObject, agencyModel);
                break;
            case upload_idcard_front:
                buildStepTwoParam(model, jsonObject, agencyModel);
                break;
            case upload_idcard_back:
                buildStepThreeParam(model, jsonObject, agencyModel);
                break;
            case audit:
                buildStepFourParam(model, jsonObject, agencyModel);
                break;
            case upload_extra_image:
                buildStepFiveParam(model, jsonObject, agencyModel);
                break;
            default:
                LoggerProxy.warn("ynxtReqestTypeInvalid", logger, "can find valid request type to reqynxt, reqestType:{}", requestType.name());
                break;
        }
        jsonObject.put("sign", getSignInfo(jsonObject));

        return jsonObject.toJSONString();
    }

    private String createUuid() {
        String id = GlobalUtil.getGlobalId().substring(1);
        return DateUtil.formatCurrent(DateUtil.SHORT_FORMAT) + id;
    }

    // 审核信息
    private void buildStepFourParam(ChannelRequestModel model, JSONObject jsonObject, ChannelRequestAgency agencyModel) {
        Event request = JacksonUtil.toObject(model.getRequestMessage(), Event.class);
        jsonObject.put("applicationNo", agencyModel.getDpJobId());
        jsonObject.put("partnerCode", DEFAULT_SYSTEMID);
        jsonObject.put("auditType", buildRquestType(request));
    }

    private void buildStepFiveParam(ChannelRequestModel model, JSONObject jsonObject,
                                    ChannelRequestAgency agencyModel) {
        jsonObject.put("applicationNo", agencyModel.getDpJobId());
        jsonObject.put("partnerCode", DEFAULT_SYSTEMID);
        JSONObject extraData = (JSONObject) agencyModel.getExtraData();
        String fileSuffix = FILE_SUFFIX;
        if (StringUtils.isNotEmpty(extraData.getString("extraImageSuffix"))) {
            fileSuffix = extraData.getString("extraImageSuffix");
        }
        jsonObject.put("fileName", model.getUserKey() + fileSuffix);
        jsonObject.put("fileType", extraData.getString("extraImageType"));
        jsonObject.put("base64FileContent", HttpRequestUtil.download(extraData.getString("extraImageUri")));
    }

    private void buildStepThreeParam(ChannelRequestModel model, JSONObject jsonObject,
                                     ChannelRequestAgency agencyModel) {
        jsonObject.put("applicationNo", agencyModel.getDpJobId());
        jsonObject.put("partnerCode", DEFAULT_SYSTEMID);
        jsonObject.put("fileType", FILE_TYPE_ID_CARD_BACK);
        jsonObject.put("fileName", model.getUserKey() + FILE_SUFFIX);
        JSONObject extraData = (JSONObject) agencyModel.getExtraData();
        jsonObject.put("base64FileContent", HttpRequestUtil.download(extraData.getString("idcardBack")));
    }

    // 身份证照片信息-正面
    private void buildStepTwoParam(ChannelRequestModel model, JSONObject jsonObject, ChannelRequestAgency agencyModel) {
        jsonObject.put("applicationNo", agencyModel.getDpJobId());
        jsonObject.put("partnerCode", DEFAULT_SYSTEMID);
        jsonObject.put("fileType", FILE_TYPE_ID_CARD_FRONT);
        jsonObject.put("fileName", model.getUserKey() + FILE_SUFFIX);
        JSONObject extraData = (JSONObject) agencyModel.getExtraData();
        jsonObject.put("base64FileContent", HttpRequestUtil.download(extraData.getString("idcardFront")));
    }

    // 进件信息
    private void buildStepOneParam(ChannelRequestModel model, JSONObject jsonObject, ChannelRequestAgency agencyModel) {
        Event request = JacksonUtil.toObject(model.getRequestMessage(), Event.class);
        // 数据中心组织原始数据，根据type类型是否为放款审核确定是否需要组合数据
        // 公共参数部分
        jsonObject.put("partnerCode", DEFAULT_SYSTEMID);
        jsonObject.put("stage", buildRquestType(request));
        jsonObject.put("productKey", DEFAULT_PRODUCTKEY);//
        jsonObject.put("partnerApplNo", String.valueOf(request.getInteger("loanId")));
//        jsonObject.put("applicationNo", agencyModel.getDpJobId());
        // 查询数据中心用户基本信息
        String dcData = getDataCenterMsg(model);
//        String dcData = "{     \"user\":{            \"userName\": \"唐正兵\",         \"gender\": \"0\",         \"userMobile\": \"18235148767\",         \"idCardNo\": \"532129198412150719\",         \"birthDate\": 1539086995035,         \"firstContactMobile\":\"18700010302\",         \"firstContactName\":\"王长柱\",         \"firstContactRelation\":\"1\",         \"zipCode\":\"999999\",         \"liveProvince\":\"11\",         \"liveArea\":\"1101\",         \"liveAddress\":\"北京市朝阳区永和园花家地小区33号楼203\",         \"liveCity\":\"11\",         \"monthlyIncome\":5000,         \"postCode\":\"999999\",         \"maritalStatus\":\"10\",         \"partnerUserId\": \"d0a1eb466cb67a562fef590133dbf162\",         \"extraUserInfo\":{             \"verificationInfo\":\"[图片]http://www.baidu.com\",             \"partnerCreditScore\":\"80\",             \"deviceNumber\":\"HW-10\",             \"ipAddress\":\"***********\",             \"latitude\":\"37.4\",             \"partnerLoanCount\":\"5\",             \"longitude\":\"100.3\"         }     } }";
        JSONObject oriRecord = JSONObject.parseObject(dcData);
        // 处理user信息
        jsonObject.put("user", buildUserInfo(oriRecord, request));
        // 数据中心返回数据提取新网提交身份证正反面信息
        resolveIdcardPhoto(oriRecord, agencyModel);
        // 如为放款审核，则需拼接loan&板块信息
        if (RequestType.transaction.name().equals(request.getRequestType())) {
            dealLoanInfo(jsonObject, request);
        }
        // extraUserInfo信息转string
        JSONObject user = jsonObject.getJSONObject("user");
        JSONObject extraUserInfo = user.getJSONObject("extraUserInfo");
        user.put("extraUserInfo", extraUserInfo.toJSONString());
    }

    private void resolveIdcardPhoto(JSONObject oriRecord, ChannelRequestAgency agencyModel) {
        // userCertInfo
        JSONObject userCertRec = oriRecord.getJSONObject("userCertInfo");
        JSONObject extraData = null;
        if (agencyModel.getExtraData() != null) {
            extraData = (JSONObject) agencyModel.getExtraData();
        } else {
            extraData = new JSONObject();
            agencyModel.setExtraData(extraData);
        }
        if (userCertRec != null) {
            extraData.put("idcardFront", userCertRec.getString("idcardFront"));
            extraData.put("idcardBack", userCertRec.getString("idcardBack"));
        }
    }

    private String getDataCenterMsg(ChannelRequestModel model) {
        // 构建数据中心请求参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appName", AppName.risk_channel.name());
        jsonObject.put("requestId", createUuid());
        JSONObject data = new JSONObject();
        jsonObject.put("data", data);
        data.put("loanKey", model.getLoanKey());
        data.put("userKey", model.getUserKey());
        JSONArray services = new JSONArray();
        data.put("services", services);
        JSONObject serviceRec = new JSONObject();
        services.add(serviceRec);
        serviceRec.put("serviceCode", "queryUserInfo");
        JSONObject params = new JSONObject();
        serviceRec.put("params", params);
        params.put("userKey", model.getUserKey());
        // 默认为好还
        params.put("sourceSystem", "HAO_HUAN");
        String url = configProperties.getProperty("url.getreqmsg.datacenter.inside");
        String httpret = SyncHTTPRemoteAPI.postJson(url, jsonObject.toJSONString(), null, 60000, false);
        return dealDcRet(httpret);
    }

    private String dealDcRet(String httpret) {
        JSONObject map = JSONObject.parseObject(httpret);
        if (MapUtils.isEmpty(map)) {
            return "";
        }
        String status = String.valueOf(map.getString("retCode"));
        if (DC_SUCCESS_RETCODE.equals(status)) {
            JSONObject resultRec = map.getJSONObject("result");
            JSONArray serviceArr = resultRec.getJSONArray("services");
            JSONObject serviceRec = serviceArr.getJSONObject(0);
            String serviceStatus = String.valueOf(serviceRec.getString("retCode"));
            if (DC_SUCCESS_RETCODE.equals(serviceStatus)) {
                JSONObject detailRet = serviceRec.getJSONObject("result");
                return detailRet.getJSONObject("data").toJSONString();
            }

        }
        return null;
    }

    private String buildRquestType(Event request) {
        String requestType = request.getRequestType();
        if (RequestType.transaction.name().equals(requestType) && FundPlatChannel.YNXT.name().equals(request.getAgencyCode())) {
            return "LOAN";
        } else {
            return "APPLICATION";
        }
    }

    private void dealLoanInfo(JSONObject jsonObject, Event request) {
        String loanInfo = request.getString("data");
        if (StringUtils.isEmpty(loanInfo)) {
            return;
        }
        JSONObject loanInfoRec = JSONObject.parseObject(loanInfo);
        JSONObject loanExtraData = loanInfoRec.getJSONObject("loan");
        if (loanExtraData != null) {
            jsonObject.put("productKey", loanExtraData.get("productKey"));
            // 剔除loan下productKey防止签名错误
            loanExtraData.remove("productKey");
        }
        jsonObject.put("loan", loanInfoRec.getJSONObject("loan"));
        jsonObject.put("bank", loanInfoRec.getJSONArray("bank"));
        // 放款审核阶段 业务方进件标识传值为loan
        jsonObject.put("partnerApplNo", loanInfoRec.getString("loanUq"));
        // 放款审核阶段 productKey取值
        //        String loanType = loanInfoRec.getString("loanType");
        //        if (StringUtils.isNotEmpty(loanType)) {
        //            String loanProKey = MAP_FUND_HAOHUAN_PRODUCTKEY.get(loanType);
        //            if (StringUtils.isNotEmpty(loanProKey)) {
        //            }
        //        }
        // 额外信息处理
        JSONObject userLoanInfo = loanInfoRec.getJSONObject("user");
        // todo check applicationNo to update
        jsonObject.put("applicationNo", userLoanInfo.get("applicationNo"));
        JSONObject user = jsonObject.getJSONObject("user");
        JSONObject extraUserInfo = user.getJSONObject("extraUserInfo");
        if (extraUserInfo != null) {
            extraUserInfo.put("partnerLoanCount", userLoanInfo.get("loanCount"));
            extraUserInfo.put("currentBalance", userLoanInfo.get("currentBalance"));
            extraUserInfo.put("isMaxOverdueGt10d", userLoanInfo.get("isMaxOverdueGt10d"));
            extraUserInfo.put("hasNotSettled", userLoanInfo.get("hasNotSettled"));
            extraUserInfo.put("firstLoanTime", userLoanInfo.get("firstLoanTime"));
            extraUserInfo.put("zaCustType", userLoanInfo.get("zaCustType"));
            extraUserInfo.put("isSumOverdueGt3t", userLoanInfo.get("isSumOverdueGt3t"));
            extraUserInfo.put("isOverdueGt1d", userLoanInfo.get("isOverdueGt1d"));
            extraUserInfo.put("latitude", userLoanInfo.get("latitude"));
            extraUserInfo.put("longitude", userLoanInfo.get("longitude"));
            extraUserInfo.put("creditAmount", userLoanInfo.get("creditAmount"));
            extraUserInfo.put("partnerCreditScore", userLoanInfo.get("partnerCreditScore"));
        }
    }

    @Override
    public RetCodeEnum parseResponse(ChannelRequestAgency agency, String response) {
        JSONObject map = JSONObject.parseObject(response);
        JSONObject dataRet = map.getJSONObject("data");
        if (dataRet != null && dataRet.containsKey("applicationNo")) {
            // 视为进件重复提交
            agency.setDpJobId(dataRet.getString("applicationNo"));
            return RetCodeEnum.SUCCESS;
        } else if ("********".equals(map.getString("status")) && map.getString("message").contains("请先绑卡")) {
            //用户未绑卡
            return RetCodeEnum.NOT_BIND_CARD;
        } else if ("********".equals(map.getString("status")) && map.getString("message").contains("进件用户姓名与银行卡信息的账户名称不一致")) {
            //进件用户姓名与银行卡信息的账户名称不一致
            return RetCodeEnum.USER_BANK_DIFF;
        } else if ("********".equals(map.getString("status")) && map.getString("message").contains("签名错误")) {
            //用户未绑卡
            return RetCodeEnum.SIGN_ERROR;
        }
        return RetCodeEnum.FAILED;
    }

    @Override
    public String parseResponseMessage(ChannelRequestAgency agency, String data) {
        return data;
    }

    private String contactRelation(String innerRelationType) {
        // 和业务确认遗留对照关系,父母默认父亲，配偶默认妻子,子女和其他亲属对应，朋友
        if (StringUtils.isBlank(innerRelationType) || "其他".equals(innerRelationType)) {
            return "2";
        }
        if ("8_3_父母".equals(innerRelationType) || "父母".equals(innerRelationType)) {
            // 对方父母是分开定义的，和业务方确认都传"2(父亲)"
            return "2";
        }
        if ("9_1_朋友".equals(innerRelationType) || "朋友".equals(innerRelationType)) {
            return "1";
        }
        if ("8_5_其他亲属".equals(innerRelationType) || "其他亲属".equals(innerRelationType)) {
            return "2";
        }
        if ("9_2_同事".equals(innerRelationType) || "同事".equals(innerRelationType)) {
            return "0";
        }
        if ("8_1_配偶".equals(innerRelationType) || "配偶".equals(innerRelationType)) {
            return "6";
        }
        if ("8_2_子女".equals(innerRelationType) || "子女".equals(innerRelationType)) {
            return "2";
        }
        if ("8_4_兄弟姐妹".equals(innerRelationType) || "兄弟姐妹".equals(innerRelationType)) {
            return "4";
        }
        return "2";
    }

    private JSONObject buildUserInfo(JSONObject oriRecord, Event request) {
        JSONObject user = oriRecord.getJSONObject("user");
        if (user == null) {
            user = new JSONObject();
        }
        user.put("monthlyIncome", salary(user.getString("monthlyIncome")));
        user.put("maritalStatus", marriage(user.getString("maritalStatus")));
        user.put("firstContactRelation", contactRelation(user.getString("firstContactRelation")));
        // 补充通话详单信息
        // 修改为channel接收上游jobId自行拼接调用地址, 需业务方确定是否必须字段
        dealCallRecord(user, request.getString("callRecordUrl"));
        // extraUserInfo信息转string
        JSONObject extraUserInfo = user.getJSONObject("extraUserInfo");
        oriRecord.put("extraUserInfo", extraUserInfo.toJSONString());
        // 云信进件信息调用地址字段传默认码值   北京2 北京市36 海淀区384
        user.put("liveProvince", "2");
        user.put("liveCity", "36");
        user.put("liveArea", "384");

        //add info of user credit
        String dataInfo = request.getString("data");
        if (StringUtils.isEmpty(dataInfo)) {
            return user;
        }
        JSONObject dataJson = JSONObject.parseObject(dataInfo);
        JSONObject userJson = dataJson.getJSONObject("user");
        if (userJson != null) {
            this.addInfoNotNull(user, userJson, "userName");
            this.addInfoNotNull(user, userJson, "userMobile");
            this.addInfoNotNull(user, userJson, "gender");
            this.addInfoNotNull(user, userJson, "idCardNo");
            this.addInfoNotNull(user, userJson, "birthDate");
            this.addInfoNotNull(user, userJson, "highestDegree");
            this.addInfoNotNull(user, userJson, "highestEducation");
            this.addInfoNotNull(user, userJson, "email");
            this.addInfoNotNull(user, userJson, "liveConditions");
            this.addInfoNotNull(user, userJson, "liveProvince");
            this.addInfoNotNull(user, userJson, "liveArea");
            this.addInfoNotNull(user, userJson, "liveCity");
            this.addInfoNotNull(user, userJson, "zipCode");
            this.addInfoNotNull(user, userJson, "liveAddress");
            this.addInfoNotNull(user, userJson, "postCode");
            this.addInfoNotNull(user, userJson, "postAddress");
            this.addInfoNotNull(user, userJson, "jobTitle");
            this.addInfoNotNull(user, userJson, "jobDuty");
            this.addInfoNotNull(user, userJson, "jobType");
            this.addInfoNotNull(user, userJson, "companyIndustry");
            this.addInfoNotNull(user, userJson, "companyName");
            this.addInfoNotNull(user, userJson, "maritalStatus");
            this.addInfoNotNull(user, userJson, "nickName");
            // add userlevel to extrauserinfo
            this.addInfoNotNull(extraUserInfo, userJson, "partnerCreditScore");
        }
        JSONObject riskInfoJson = dataJson.getJSONObject("riskInfo");
        if (riskInfoJson != null) {
            extraUserInfo.put("riskInfo", riskInfoJson.toJSONString());
        }
        JSONObject otherUserMsg = (JSONObject) JSONPath.read(dataJson.toJSONString(), "otherParams.user");
        if (otherUserMsg != null) {
            user.putAll(otherUserMsg);
        }
        JSONObject otherExtraUserMsg = (JSONObject) JSONPath.read(dataJson.toJSONString(), "otherParams.extraUserInfo");
        if (otherExtraUserMsg != null) {
            extraUserInfo.putAll(otherExtraUserMsg);
        }
        return user;
    }

    private void addInfoNotNull(JSONObject user, JSONObject data, String key) {
        Object value = data.get(key);
        if (value != null) {
            user.put(key, value);
        }
    }

    private void dealCallRecord(JSONObject user, String callRecordUrl) {
        String url = String.format(this.urlGet, "CALL_HISTORY", callRecordUrl, ChannelConstant.CHANNEL_DATAPLAT_DEFAULT_ACTIVE_GET_DATA_SYSTEM);
        JSONObject extraUserInfo = user.getJSONObject("extraUserInfo");
        extraUserInfo.put("verificationInfo", url);
    }

    private String salary(String salary) {
        if (StringUtils.isEmpty(salary)) {
            return "0";
        }
        if ("6_1_小于1000元".equals(salary) || "6_2_1000-2000元".equals(salary) || "6_3_2000-4000元".equals(salary) ||
                "6_4_4000-6000元".equals(salary) || "6_5_6000-10000元".equals(salary)) {
            return "1";
        } else if ("6_6_10000元以上".equals(salary)) {
            return "2";
        } else {
            return "0";
        }
    }

    private String marriage(String marriage) {
        if (StringUtils.isBlank(marriage)) {
            return "90";
        }
        if ("2_4_离异".equals(marriage)) {
            return "40";
        } else if ("2_5_丧偶".equals(marriage)) {
            return "30";
        } else if ("2_1_未婚".equals(marriage)) {
            return "10";
        } else if ("2_3_已婚未育".equals(marriage) || "2_2_已婚已育".equals(marriage)) {
            return "20";
        } else {
            return "90";
        }
    }

    public String getSignInfo(JSONObject record) {
        String sign = "";
        try {
            // 签名盐
            String signSalt = SALT_FUND_PLATFORM;
            // 拼装签名串
            String signStr = buildSignSrc(record) + signSalt;
            LoggerProxy.info("createSignMsg", logger, "ready sign msg：{}", signStr);
            sign = MD5Util.md5Str(signStr).toLowerCase();
            LoggerProxy.info("createSignMsg", logger, "sign：{}", sign);
        } catch (Exception e) {
            LoggerProxy.error("getFundSignFailed", logger, "get sign to req fundplat fail, param:" + record.toJSONString());
        }
        return sign;
    }

    /**
     * 生成待签名串
     *
     * @param jsonObject
     * @return
     */
    private static String buildSignSrc(JSONObject jsonObject) throws Exception {
        // 按照key做首字母升序排列
        List<String> keys = new ArrayList<>(jsonObject.keySet());
        keys.sort(String.CASE_INSENSITIVE_ORDER);
        StringBuilder content = new StringBuilder();
        for (String key : keys) {
            Object value_type = jsonObject.get(key);
            String value = jsonObject.getString(key);
            // 空串值不参与签名
            if ("sign".equals(key) || StringUtils.isBlank(value)) {
                continue;
            }
            // 对内部对象进行排序
            if (value_type != null && value_type.getClass().getSimpleName().equals("JSONObject")) {
                value = JSONObject.toJSONString(value_type, SerializerFeature.MapSortField);
            } else if (value_type != null && value_type.getClass().getSimpleName().equals("JSONArray")) {
                value = JSONArray.toJSONString(value_type, SerializerFeature.MapSortField);
            }
            content.append("&" + key + "=" + value);
        }

        String signSrc = content.toString();
        if (signSrc.startsWith("&")) {
            signSrc = signSrc.replaceFirst("&", "");
        }
        return signSrc;
    }

}
