<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         metadata-complete="true" version="3.0">

    <display-name>risk-channel</display-name>
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:spring/spring-config.xml</param-value>
    </context-param>

    <servlet>
        <servlet-name>springmvc</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:spring/spring-mvc.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>springmvc</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>DruidStatView</servlet-name>
        <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DruidStatView</servlet-name>
        <url-pattern>/druid/*</url-pattern>
    </servlet-mapping>
    <!-- 添加Web应用等监控-->
    <filter>
        <filter-name>DruidWebStatFilter</filter-name>
        <filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>
        <init-param>
            <param-name>exclusions</param-name>
            <param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>
        </init-param>
        <init-param>
            <param-name>profileEnable</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>principalCookieName</param-name>
            <param-value>USER_COOKIE</param-value>
        </init-param>
        <init-param>
            <param-name>principalSessionName</param-name>
            <param-value>USER_SESSION</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>DruidWebStatFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <!--<servlet>-->
    <!--<servlet-name>health</servlet-name>-->
    <!--<servlet-class>com.youxin.risk.commons.web.servlet.HealthServlet</servlet-class>-->
    <!--</servlet>-->
    <!--<servlet-mapping>-->
    <!--<servlet-name>health</servlet-name>-->
    <!--<url-pattern>/health</url-pattern>-->
    <!--</servlet-mapping>-->

    <servlet>
        <servlet-name>stateChange</servlet-name>
        <servlet-class>com.youxin.risk.commons.web.servlet.StateChangeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>stateChange</servlet-name>
        <url-pattern>/stateChange</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>cacheApi</servlet-name>
        <servlet-class>com.youxin.risk.commons.web.servlet.CacheApiServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>cacheApi</servlet-name>
        <url-pattern>/cacheApi</url-pattern>
    </servlet-mapping>

    <!--<servlet>-->
    <!--<servlet-name>metrics</servlet-name>-->
    <!--<servlet-class>com.codahale.metrics.servlets.AdminServlet</servlet-class>-->
    <!--</servlet>-->
    <!--<servlet-mapping>-->
    <!--<servlet-name>metrics</servlet-name>-->
    <!--<url-pattern>/metrics/*</url-pattern>-->
    <!--</servlet-mapping>-->
</web-app>
