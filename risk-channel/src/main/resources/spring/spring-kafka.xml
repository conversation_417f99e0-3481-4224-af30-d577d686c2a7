<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <!-- 监控检查和上下线通知接口相关 -->
    <bean id="kafkaMessageListener" class="com.youxin.risk.commons.health.impl.KafKaMessageListener"/>

    <!-- spring和kafka集成相关配置 -->
    <bean id="consumerProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="${kafka.dp.hosts}"/>
                <entry key="group.id" value="${kafka.dp.topic.group.id}"/>
                <entry key="enable.auto.commit" value="false"/>
                <entry key="auto.commit.interval.ms" value="1000"/>
                <entry key="session.timeout.ms" value="30000"/>
                <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
                <entry key="value.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="consumerFactory" class="org.springframework.kafka.core.DefaultKafkaConsumerFactory">
        <constructor-arg>
            <ref bean="consumerProperties"/>
        </constructor-arg>
    </bean>

    <bean id="containerProperties" class="org.springframework.kafka.listener.config.ContainerProperties">
        <constructor-arg value="${kafka.dp.topic}"/>
        <property name="messageListener" ref="dpMsgListener"/>
        <property name="ackMode" value="MANUAL_IMMEDIATE"/>
    </bean>

    <bean id="messageListenerContainer" class="org.springframework.kafka.listener.KafkaMessageListenerContainer"
          init-method="doStart">
        <constructor-arg ref="consumerFactory"/>
        <constructor-arg ref="containerProperties"/>
    </bean>

    <!-- mirror -->
    <bean id="consumerMirrorProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="${kafka.mirror.dp.hosts}"/>
                <entry key="group.id" value="${kafka.dp.topic.group.id}"/>
                <entry key="enable.auto.commit" value="false"/>
                <entry key="auto.commit.interval.ms" value="1000"/>
                <entry key="session.timeout.ms" value="30000"/>
                <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
                <entry key="value.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="consumerMirrorFactory" class="org.springframework.kafka.core.DefaultKafkaConsumerFactory">
        <constructor-arg>
            <ref bean="consumerMirrorProperties"/>
        </constructor-arg>
    </bean>

    <bean id="containerMirrorProperties" class="org.springframework.kafka.listener.config.ContainerProperties">
        <constructor-arg value="${kafka.dp.topic}"/>
        <property name="messageListener" ref="dpMsgMirrorListener"/>
        <property name="ackMode" value="MANUAL_IMMEDIATE"/>
    </bean>

    <bean id="messageListenerMirrorContainer" class="org.springframework.kafka.listener.KafkaMessageListenerContainer"
          init-method="doStart">
        <constructor-arg ref="consumerMirrorFactory"/>
        <constructor-arg ref="containerMirrorProperties"/>
    </bean>

    <!-- risk kafka api -->
    <bean id="dpMsgListener" class="com.youxin.risk.commons.kafkav2.RiskKafkaAcknowledgingMessageListener">
        <property name="filters">
            <list>
                <ref bean="dpMsgPaserFilter"/>
            </list>
        </property>
        <property name="handler" ref="dpMsgHander"/>
    </bean>

    <bean id="dpMsgMirrorListener" class="com.youxin.risk.commons.kafkav2.RiskKafkaAcknowledgingMessageListener">
        <property name="filters">
            <list>
                <ref bean="dpMsgPaserFilter"/>
            </list>
        </property>
        <property name="handler" ref="dpMsgHander"/>
    </bean>

    <!-- 消息解析filter，将消息反序列化对对象，默认使用fastjson，可自己实现 -->
    <bean id="dpMsgPaserFilter" class="com.youxin.risk.commons.kafkav2.filter.impl.ParseFilter">
        <property name="serializedBeanClassName" value="com.youxin.risk.commons.vo.DataPlatformMessageVo"/>
    </bean>

    <!-- 消息处理器，继承BaseKafKaMessageHandler，注入RetryableJedis，会进行判重操作，重复的请求不再处理 -->
    <bean id="dpMsgHander" class="com.youxin.risk.channel.service.DataPlatformMessageHandler">
    	<property name="retryableJedis" ref="retryableJedis"/>
    </bean>


    <!-- dc consumer相关配置 -->
<!--    <bean id="dcConsumerProperties" class="java.util.HashMap">-->
<!--        <constructor-arg>-->
<!--            <map>-->
<!--                <entry key="bootstrap.servers" value="${kafka.dp.hosts}"/>-->
<!--                <entry key="group.id" value="${kafka.dc.topic.default.group.id}"/>-->
<!--                <entry key="enable.auto.commit" value="true"/>-->
<!--                <entry key="auto.commit.interval.ms" value="1000"/>-->
<!--                <entry key="session.timeout.ms" value="30000"/>-->
<!--                <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>-->
<!--                <entry key="value.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>-->
<!--            </map>-->
<!--        </constructor-arg>-->
<!--    </bean>-->
<!--    <bean id="defaultConsumerFactory" class="org.springframework.kafka.core.DefaultKafkaConsumerFactory">-->
<!--        <constructor-arg>-->
<!--            <ref bean="dcConsumerProperties"/>-->
<!--        </constructor-arg>-->
<!--    </bean>-->
    <!--<bean id="defaultContainerProperties" class="org.springframework.kafka.listener.config.ContainerProperties">
        <constructor-arg value="${kafka.dc.topic.default}"/>
        <property name="messageListener" ref="defaultMsgListener"/>
    </bean>-->
    <!--<bean id="defaultMessageListenerContainer" class="org.springframework.kafka.listener.KafkaMessageListenerContainer"
          init-method="doStart">
        <constructor-arg ref="defaultConsumerFactory"/>
        <constructor-arg ref="defaultContainerProperties"/>
    </bean>-->
    <!--<bean id="defaultMsgListener" class="com.youxin.risk.channel.listener.DcDataKafkaMessageListener"/>-->
</beans>